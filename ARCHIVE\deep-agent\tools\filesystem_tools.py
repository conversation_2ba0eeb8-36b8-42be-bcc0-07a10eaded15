"""
Enhanced Filesystem Tools - Integrates existing filesystem functionality with Deep Agent file manager.
Maintains backward compatibility while adding advanced file management capabilities.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional
import json
import asyncio
from datetime import datetime
from pathlib import Path


def get_filesystem_tools(file_system_manager):
    """Get enhanced filesystem tools with Deep Agent integration."""
    
    @tool
    def write_file(
        path: str,
        content: str,
        overwrite: bool = True,
        encoding: str = "utf-8"
    ) -> Dict[str, Any]:
        """
        Write content to a file in the agent workspace with enhanced capabilities.
        
        Args:
            path: Relative path within the workspace (e.g., "analysis/report.md")
            content: Content to write to the file
            overwrite: Whether to overwrite existing files (default: True)
            encoding: File encoding (default: utf-8)
            
        Returns:
            Dictionary confirming the file write operation with enhanced metadata
        """
        try:
            # Use Deep Agent file manager for enhanced functionality
            result = asyncio.run(file_system_manager.write_file(
                path=path,
                content=content,
                encoding=encoding,
                create_dirs=True
            ))
            
            if result["success"]:
                return {
                    "success": True,
                    "path": path,
                    "bytes_written": result["bytes_written"],
                    "encoding": encoding,
                    "file_info": result.get("file_info", {}),
                    "message": f"Successfully wrote {result['bytes_written']} bytes to {path}",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "write_file",
                        "arguments": {"path": path, "content_length": len(content)}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": result.get("message", f"Failed to write file {path}")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to write file {path}: {str(e)}"
            }
    
    @tool
    def read_file(
        path: str,
        encoding: str = "utf-8",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Read content from a file in the agent workspace with caching support.
        
        Args:
            path: Relative path within the workspace
            encoding: File encoding (default: utf-8)
            use_cache: Whether to use cached content if available
            
        Returns:
            Dictionary containing the file content and metadata
        """
        try:
            result = asyncio.run(file_system_manager.read_file(
                path=path,
                encoding=encoding,
                use_cache=use_cache
            ))
            
            if result["success"]:
                return {
                    "success": True,
                    "path": path,
                    "content": result["content"],
                    "encoding": encoding,
                    "file_info": result.get("file_info", {}),
                    "cached": result.get("cached", False),
                    "message": f"Successfully read {path}",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "read_file",
                        "arguments": {"path": path}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": result.get("message", f"Failed to read file {path}")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to read file {path}: {str(e)}"
            }
    
    @tool
    def list_files(
        path: str = "",
        recursive: bool = False,
        pattern: Optional[str] = None,
        include_hidden: bool = False
    ) -> Dict[str, Any]:
        """
        List files and directories in the agent workspace with enhanced filtering.
        
        Args:
            path: Directory path relative to workspace root (default: root)
            recursive: Whether to list files recursively
            pattern: File pattern to match (glob style)
            include_hidden: Whether to include hidden files
            
        Returns:
            Dictionary containing the list of files and directories with metadata
        """
        try:
            result = asyncio.run(file_system_manager.list_files(
                path=path,
                recursive=recursive,
                pattern=pattern,
                include_hidden=include_hidden
            ))
            
            if result["success"]:
                return {
                    "success": True,
                    "path": path,
                    "files": result["files"],
                    "directories": result["directories"],
                    "total_files": result["total_files"],
                    "total_directories": result["total_directories"],
                    "recursive": recursive,
                    "pattern": pattern,
                    "message": f"Listed {result['total_files']} files and {result['total_directories']} directories",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "list_files",
                        "arguments": {"path": path, "recursive": recursive}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": result.get("message", f"Failed to list directory {path}")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to list directory {path}: {str(e)}"
            }
    
    @tool
    def delete_file(path: str) -> Dict[str, Any]:
        """
        Delete a file or directory from the agent workspace.
        
        Args:
            path: Relative path within the workspace
            
        Returns:
            Dictionary confirming the deletion operation
        """
        try:
            result = asyncio.run(file_system_manager.delete_file(path))
            
            if result["success"]:
                return {
                    "success": True,
                    "path": path,
                    "file_info": result.get("file_info", {}),
                    "message": f"Successfully deleted {path}",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "delete_file",
                        "arguments": {"path": path}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": result.get("message", f"Failed to delete {path}")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to delete {path}: {str(e)}"
            }
    
    @tool
    def copy_file(source_path: str, dest_path: str) -> Dict[str, Any]:
        """
        Copy a file within the agent workspace.
        
        Args:
            source_path: Source file path relative to workspace root
            dest_path: Destination file path relative to workspace root
            
        Returns:
            Dictionary confirming the copy operation
        """
        try:
            result = asyncio.run(file_system_manager.copy_file(source_path, dest_path))
            
            if result["success"]:
                return {
                    "success": True,
                    "source_path": source_path,
                    "dest_path": dest_path,
                    "file_info": result.get("file_info", {}),
                    "message": f"Successfully copied {source_path} to {dest_path}",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "copy_file",
                        "arguments": {"source_path": source_path, "dest_path": dest_path}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": result.get("message", f"Failed to copy {source_path} to {dest_path}")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to copy {source_path} to {dest_path}: {str(e)}"
            }
    
    @tool
    def save_analysis_report(
        analysis_data: str,
        report_name: str,
        category: str = "general"
    ) -> Dict[str, Any]:
        """
        Save an analysis report with structured organization and metadata.
        
        Args:
            analysis_data: JSON string or text content of the analysis
            report_name: Name for the report file
            category: Category for organization (crypto, research, development, etc.)
            
        Returns:
            Dictionary confirming the report save operation
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_name}_{timestamp}.json"
            file_path = f"analysis/{category}/{filename}"
            
            # Prepare report structure
            if analysis_data.startswith('{') or analysis_data.startswith('['):
                # JSON data
                try:
                    parsed_data = json.loads(analysis_data)
                    report_content = {
                        "report_metadata": {
                            "name": report_name,
                            "category": category,
                            "created_at": datetime.now().isoformat(),
                            "type": "analysis_report",
                            "deep_agent_generated": True
                        },
                        "analysis_data": parsed_data
                    }
                except json.JSONDecodeError:
                    # Treat as text
                    report_content = {
                        "report_metadata": {
                            "name": report_name,
                            "category": category,
                            "created_at": datetime.now().isoformat(),
                            "type": "text_report",
                            "deep_agent_generated": True
                        },
                        "content": analysis_data
                    }
            else:
                # Text data
                report_content = {
                    "report_metadata": {
                        "name": report_name,
                        "category": category,
                        "created_at": datetime.now().isoformat(),
                        "type": "text_report",
                        "deep_agent_generated": True
                    },
                    "content": analysis_data
                }
            
            # Save the report
            result = asyncio.run(file_system_manager.write_file(
                path=file_path,
                content=report_content,
                create_dirs=True
            ))
            
            if result["success"]:
                return {
                    "success": True,
                    "report_name": report_name,
                    "category": category,
                    "file_path": file_path,
                    "bytes_written": result["bytes_written"],
                    "message": f"Successfully saved analysis report '{report_name}' in category '{category}'",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "save_analysis_report",
                        "arguments": {"report_name": report_name, "category": category}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": f"Failed to save analysis report: {result.get('message', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to save analysis report: {str(e)}"
            }
    
    @tool
    def get_workspace_stats() -> Dict[str, Any]:
        """
        Get comprehensive workspace statistics and information.
        
        Returns:
            Dictionary containing workspace statistics and usage information
        """
        try:
            result = asyncio.run(file_system_manager.get_workspace_stats())
            
            if result["success"]:
                return {
                    "success": True,
                    "workspace_stats": {
                        "workspace_root": result["workspace_root"],
                        "total_size_mb": result["total_size_mb"],
                        "file_count": result["file_count"],
                        "directory_count": result["directory_count"],
                        "cache_size_mb": result["cache_size_mb"],
                        "cache_files": result["cache_files"],
                        "operation_stats": result["operation_stats"]
                    },
                    "message": f"Workspace contains {result['file_count']} files using {result['total_size_mb']} MB",
                    "deep_agent_enhanced": True,
                    "function_call": {
                        "name": "get_workspace_stats",
                        "arguments": {}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "message": "Failed to get workspace statistics"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get workspace stats: {str(e)}"
            }
    
    return [
        write_file,
        read_file,
        list_files,
        delete_file,
        copy_file,
        save_analysis_report,
        get_workspace_stats
    ]
