# Chad GPT LangGraph REACT Agent Setup Guide

This guide will help you set up and run the LangGraph REACT agent that powers Chad GPT with advanced reasoning capabilities.

## 🚀 Quick Start

### 1. Install Python Dependencies

Navigate to the LangGraph agent directory and install dependencies:

```bash
cd langgraph-agent
pip install -r requirements.txt
```

### 2. Start the LangGraph Agent

**Option A: Using npm script (recommended)**
```bash
npm run agent
```

**Option B: Direct Python execution**
```bash
cd langgraph-agent
python start.py
```

**Option C: Manual startup**
```bash
cd langgraph-agent
python main.py
```

### 3. Start the Frontend

In a separate terminal:
```bash
npm run dev
```

### 4. Test the Integration

The frontend will now use the LangGraph REACT agent instead of direct OpenRouter calls. You should see:

- More intelligent responses with reasoning capabilities
- Proper widget triggering for PumpFun and DexScreener
- Conversation memory across messages
- Fallback to direct OpenRouter if the agent fails

## 🧪 Testing the Agent

### Test the Agent Directly

```bash
npm run agent:test
```

This will run comprehensive tests including:
- Basic chat functionality
- Widget triggering (<PERSON><PERSON><PERSON><PERSON>, DexScreener)
- Conversation memory
- Crypto knowledge

### Test via API

You can also test the agent API directly:

```bash
curl -X POST http://localhost:8001/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "Show me pump.fun data"}
    ]
  }'
```

### Health Check

```bash
curl http://localhost:8001/health
```

## 🔧 Configuration

### Environment Variables

The agent is pre-configured with:

- **OPENROUTER_API_KEY**: Your OpenRouter API key
- **MODEL_NAME**: moonshotai/kimi-k2 (as requested)
- **PORT**: 8001 (agent server port)

### Frontend Integration

The frontend (`src/lib/ai.ts`) has been updated to:

1. **Primary**: Call the LangGraph agent at `http://localhost:8001/api/chat`
2. **Fallback**: Use direct OpenRouter calls if the agent fails
3. **Compatibility**: Maintain exact same interface as before

## 🎯 Features

### REACT Agent Capabilities

- **Reasoning**: The agent thinks through problems step by step
- **Acting**: Uses tools to accomplish tasks
- **Memory**: Remembers conversation context across messages
- **Tool Use**: Can trigger widgets and perform actions

### Available Tools

1. **show_pumpfun_widget**: Triggers PumpFun widget display
2. **show_dexscreener_widget**: Triggers DexScreener widget display

### Widget Integration

The agent seamlessly integrates with existing widgets:

- **PumpFun**: Triggered by queries about pump.fun, tokens, trending
- **DexScreener**: Triggered by queries about charts, DEX data, trading

## 🔍 Monitoring & Debugging

### Agent Logs

Monitor the agent terminal for:
- Request/response details
- Tool usage
- Error messages
- Performance metrics

### Frontend Logs

Check browser console for:
- API call success/failure
- Fallback activation
- Widget triggering

### Common Issues

1. **Port 8001 in use**: Change PORT in `.env` and update frontend URL
2. **Agent not starting**: Check Python dependencies and API key
3. **Frontend not connecting**: Verify agent is running and CORS is configured
4. **Slow responses**: Normal for REACT agent due to reasoning steps

## 📊 Performance

### Expected Behavior

- **Response Time**: 2-5 seconds (due to REACT reasoning)
- **Accuracy**: Higher quality responses with reasoning
- **Reliability**: Fallback ensures service continuity
- **Memory**: Conversation context preserved per thread

### Optimization Tips

- Use thread IDs for better conversation management
- Monitor response times and optimize prompts if needed
- Consider caching for frequently asked questions

## 🔄 Development Workflow

### Making Changes

1. **Agent Logic**: Edit `langgraph-agent/agent.py`
2. **Tools**: Add/modify tools in `langgraph-agent/tools.py`
3. **API**: Update endpoints in `langgraph-agent/main.py`
4. **Frontend**: Modify `src/lib/ai.ts` for integration changes

### Testing Changes

1. Restart the agent: `npm run agent`
2. Test functionality: `npm run agent:test`
3. Test frontend integration in browser

### Adding New Tools

1. Define tool in `tools.py`
2. Add to TOOLS list
3. Update frontend to handle new function calls
4. Test integration

## 🚀 Production Deployment

For production:

1. Use production WSGI server (Gunicorn)
2. Set up proper logging and monitoring
3. Configure environment variables securely
4. Consider containerization
5. Set up load balancing if needed

## 🆘 Troubleshooting

### Agent Won't Start

```bash
# Check Python version
python --version

# Install dependencies
pip install -r langgraph-agent/requirements.txt

# Check for errors
cd langgraph-agent && python main.py
```

### Frontend Not Connecting

1. Verify agent is running: `curl http://localhost:8001/health`
2. Check browser console for CORS errors
3. Verify API URL in `src/lib/ai.ts`

### Widget Not Triggering

1. Check agent logs for tool usage
2. Verify function_call format in response
3. Test with direct API call

## 📝 Next Steps

The LangGraph REACT agent is now fully integrated with your Chad GPT application. You can:

1. **Extend Tools**: Add more sophisticated tools for crypto analysis
2. **Improve Prompts**: Optimize the system prompt for better responses
3. **Add Memory**: Implement persistent memory beyond conversation threads
4. **Monitor Performance**: Set up logging and analytics
5. **Scale**: Deploy to production with proper infrastructure

Enjoy your new AI-powered Chad GPT with advanced reasoning capabilities! 🎉
