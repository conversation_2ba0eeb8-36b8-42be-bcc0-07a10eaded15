import React, { useState, useEffect } from 'react';
import { TrendingUp, ExternalLink, RefreshCw, AlertCircle } from 'lucide-react';

interface CompactTokenChartProps {
  tokenAddress: string;
  tokenName?: string;
  tokenSymbol?: string;
  chain?: string;
  height?: string;
  title?: string;
  subtitle?: string;
  isLoading?: boolean;
}

// Map DexScreener chain IDs to DexScreener URL format
function mapChainToDexScreener(chainId: string): string {
  const chainMap: { [key: string]: string } = {
    'ethereum': 'ethereum',
    'solana': 'solana',
    'bsc': 'bsc',
    'base': 'base',
    'arbitrum': 'arbitrum',
    'polygon': 'polygon',
    'avalanche': 'avalanche',
    'optimism': 'optimism',
    'fantom': 'fantom',
    'cronos': 'cronos',
    'xrpl': 'xrpl',
    'tron': 'tron',
    'sui': 'sui',
    'aptos': 'aptos'
  };

  return chainMap[chainId.toLowerCase()] || 'solana'; // Default to Solana
}

// Build DexScreener embed URL
function buildDexScreenerEmbedUrl(tokenAddress: string, chainId: string): string {
  const chain = mapChainToDexScreener(chainId);

  // Build the embed URL with all the proper parameters
  const embedParams = new URLSearchParams({
    'embed': '1',
    'loadChartSettings': '0',
    'tabs': '0',
    'info': '0',
    'chartLeftToolbar': '0',
    'chartDefaultOnMobile': '1',
    'chartTheme': 'dark',
    'theme': 'dark',
    'chartStyle': '1',
    'chartType': 'usd',
    'interval': '15',
    'hideBranding': '1',
    'hideFooter': '1',
    'minimal': '1',
    'trades': '0'
  });

  return `https://dexscreener.com/${chain}/${tokenAddress}?${embedParams.toString()}`;
}

// Chain detection based on token address format
function detectChain(address: string): string {
  // XRPL addresses start with 'r' and are 25-34 characters
  if (/^r[a-zA-Z0-9]{25,34}$/.test(address)) {
    return 'xrpl';
  }

  // Solana addresses are typically 32-44 characters, base58 encoded
  if (/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)) {
    return 'solana';
  }

  // Ethereum addresses start with 0x and are 42 characters
  if (/^0x[a-fA-F0-9]{40}$/.test(address)) {
    return 'ethereum';
  }

  return 'solana';
}

// Validate if string looks like a contract address
function isValidContractAddress(address: string): boolean {
  const solanaPattern = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
  const evmPattern = /^0x[a-fA-F0-9]{40}$/;
  return solanaPattern.test(address) || evmPattern.test(address);
}

export function CompactTokenChart({
  tokenAddress,
  tokenName,
  tokenSymbol,
  chain,
  height = "400px",
  title = "Live Chart",
  subtitle = "Powered by DexScreener",
  isLoading = false
}: CompactTokenChartProps) {
  const [chartLoading, setChartLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [detectedChain, setDetectedChain] = useState<string>('');

  useEffect(() => {
    if (!tokenAddress) {
      setError('No token address provided');
      setChartLoading(false);
      return;
    }

    if (!isValidContractAddress(tokenAddress)) {
      setError('Invalid contract address format');
      setChartLoading(false);
      return;
    }

    // If chain is provided, use it directly, otherwise detect from address
    const finalChain = chain || detectChain(tokenAddress);
    setDetectedChain(finalChain);
    setError(null);
    setChartLoading(true);
  }, [tokenAddress, chain]);

  const handleRefresh = () => {
    setChartLoading(true);
    setError(null);
    // Force iframe reload by changing src
    const iframe = document.querySelector(`iframe[data-token="${tokenAddress}"]`) as HTMLIFrameElement;
    if (iframe) {
      const currentSrc = iframe.src;
      iframe.src = '';
      setTimeout(() => {
        iframe.src = currentSrc;
      }, 100);
    }
  };

  const openInNewTab = () => {
    const chain = mapChainToDexScreener(detectedChain);
    const url = `https://dexscreener.com/${chain}/${tokenAddress}`;
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (error) {
    return (
      <div className="bg-[#0A0A0A] rounded-xl p-6 border border-[#222]">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <h3 className="font-medium text-white">{title}</h3>
          </div>
        </div>
        <div className="text-center py-8">
          <div className="text-red-400 text-sm mb-2">{error}</div>
          <div className="text-[#666] text-xs">
            Please provide a valid contract address to view the chart.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#0A0A0A] rounded-xl overflow-hidden border border-[#222] hover:border-[#333] transition-all duration-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-[#222]">
        <div className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4 text-[#22c55e]" />
          <div>
            <h3 className="font-medium text-white text-sm">{title}</h3>
            <p className="text-xs text-[#666]">{subtitle}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <button
            onClick={handleRefresh}
            disabled={chartLoading || isLoading}
            className="p-1.5 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200 disabled:opacity-50"
            title="Refresh Chart"
          >
            <RefreshCw className={`w-3.5 h-3.5 ${(chartLoading || isLoading) ? 'animate-spin' : ''}`} />
          </button>
          
          <button
            onClick={openInNewTab}
            className="p-1.5 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200"
            title="Open Full Chart"
          >
            <ExternalLink className="w-3.5 h-3.5" />
          </button>
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative" style={{ height }}>
        {(chartLoading || isLoading) && (
          <div className="absolute inset-0 bg-[#0A0A0A] bg-opacity-90 flex items-center justify-center z-10">
            <div className="flex items-center gap-2 text-[#666]">
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span className="text-sm">Loading chart...</span>
            </div>
          </div>
        )}
        
        <div className="dexscreener-embed w-full h-full">
          <style>{`
            .dexscreener-embed iframe {
              position: absolute;
              width: 100%;
              height: 100%;
              top: 0;
              left: 0;
              border: 0;
            }
          `}</style>
          <iframe
            data-token={tokenAddress}
            src={buildDexScreenerEmbedUrl(tokenAddress, detectedChain)}
            className="w-full h-full border-0"
            title={`DexScreener Chart for ${tokenName || tokenSymbol || tokenAddress}`}
            loading="lazy"
            sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
            onLoad={() => setChartLoading(false)}
            onError={() => {
              setError('Failed to load chart');
              setChartLoading(false);
            }}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 py-2 border-t border-[#222] bg-[#111]">
        <div className="flex items-center justify-between text-xs text-[#666]">
          <div className="flex items-center gap-2">
            {tokenSymbol && (
              <>
                <span className="text-[#22c55e] font-medium">{tokenSymbol}</span>
                <span>•</span>
              </>
            )}
            <span className="uppercase font-medium">{detectedChain}</span>
          </div>
          <button
            onClick={openInNewTab}
            className="flex items-center gap-1 hover:text-white transition-colors"
          >
            <span>Full Chart</span>
            <ExternalLink className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default CompactTokenChart;
