/**
 * React Hook for Agent Memory Management
 * 
 * Provides interface for managing agent memory, context retrieval,
 * and conversation history with any backend memory system.
 */

import { useState, useEffect, useCallback } from 'react';
import { agentIntegration } from '../agentIntegration';

export interface MemoryEntry {
  id: string;
  content: string;
  type: 'conversation' | 'widget_interaction' | 'user_preference' | 'fact';
  threadId: string;
  timestamp: Date;
  relevanceScore?: number;
  metadata?: Record<string, any>;
}

export interface UseAgentMemoryOptions {
  threadId: string;
  enableAutoSave?: boolean;
  maxMemoryEntries?: number;
}

export interface UseAgentMemoryReturn {
  // Memory retrieval
  getRelevantMemories: (query: string, limit?: number) => Promise<MemoryEntry[]>;
  getAllMemories: () => Promise<MemoryEntry[]>;
  
  // Memory management
  addMemory: (content: string, type: MemoryEntry['type'], metadata?: Record<string, any>) => Promise<void>;
  removeMemory: (memoryId: string) => Promise<void>;
  clearAllMemories: () => Promise<void>;
  
  // Context building
  buildContextSummary: () => Promise<string>;
  getConversationSummary: () => Promise<string>;
  
  // Memory statistics
  memoryStats: {
    totalEntries: number;
    conversationEntries: number;
    widgetEntries: number;
    lastUpdated: Date | null;
  };
  
  // Status
  isLoading: boolean;
  error: string | null;
}

export function useAgentMemory(options: UseAgentMemoryOptions): UseAgentMemoryReturn {
  const { 
    threadId, 
    enableAutoSave = true, 
    maxMemoryEntries = 1000 
  } = options;
  
  // State management
  const [memoryStats, setMemoryStats] = useState({
    totalEntries: 0,
    conversationEntries: 0,
    widgetEntries: 0,
    lastUpdated: null as Date | null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Get relevant memories based on query
  const getRelevantMemories = useCallback(async (
    query: string, 
    limit: number = 5
  ): Promise<MemoryEntry[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          threadId,
          limit,
          includeScore: true
        })
      });
      
      if (!response.ok) {
        throw new Error(`Memory search failed: ${response.status}`);
      }
      
      const data = await response.json();
      return data.memories || [];
      
    } catch (err: any) {
      setError(err.message || 'Failed to retrieve memories');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [threadId]);
  
  // Get all memories for the thread
  const getAllMemories = useCallback(async (): Promise<MemoryEntry[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/thread/${threadId}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) {
        throw new Error(`Memory retrieval failed: ${response.status}`);
      }
      
      const data = await response.json();
      const memories = data.memories || [];
      
      // Update stats
      setMemoryStats({
        totalEntries: memories.length,
        conversationEntries: memories.filter((m: MemoryEntry) => m.type === 'conversation').length,
        widgetEntries: memories.filter((m: MemoryEntry) => m.type === 'widget_interaction').length,
        lastUpdated: new Date()
      });
      
      return memories;
      
    } catch (err: any) {
      setError(err.message || 'Failed to retrieve memories');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [threadId]);
  
  // Add new memory entry
  const addMemory = useCallback(async (
    content: string,
    type: MemoryEntry['type'],
    metadata: Record<string, any> = {}
  ): Promise<void> => {
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          type,
          threadId,
          metadata,
          timestamp: new Date().toISOString()
        })
      });
      
      if (!response.ok) {
        throw new Error(`Memory addition failed: ${response.status}`);
      }
      
      // Update stats
      setMemoryStats(prev => ({
        ...prev,
        totalEntries: prev.totalEntries + 1,
        conversationEntries: type === 'conversation' ? prev.conversationEntries + 1 : prev.conversationEntries,
        widgetEntries: type === 'widget_interaction' ? prev.widgetEntries + 1 : prev.widgetEntries,
        lastUpdated: new Date()
      }));
      
    } catch (err: any) {
      setError(err.message || 'Failed to add memory');
      throw err;
    }
  }, [threadId]);
  
  // Remove memory entry
  const removeMemory = useCallback(async (memoryId: string): Promise<void> => {
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/${memoryId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) {
        throw new Error(`Memory removal failed: ${response.status}`);
      }
      
      // Refresh stats
      await getAllMemories();
      
    } catch (err: any) {
      setError(err.message || 'Failed to remove memory');
      throw err;
    }
  }, [getAllMemories]);
  
  // Clear all memories for the thread
  const clearAllMemories = useCallback(async (): Promise<void> => {
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/thread/${threadId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) {
        throw new Error(`Memory clearing failed: ${response.status}`);
      }
      
      // Reset stats
      setMemoryStats({
        totalEntries: 0,
        conversationEntries: 0,
        widgetEntries: 0,
        lastUpdated: new Date()
      });
      
      // Clear local message history
      agentIntegration.clearMessageHistory(threadId);
      
    } catch (err: any) {
      setError(err.message || 'Failed to clear memories');
      throw err;
    }
  }, [threadId]);
  
  // Build context summary from memories
  const buildContextSummary = useCallback(async (): Promise<string> => {
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/context-summary`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          threadId,
          includeWidgets: true,
          includeConversations: true,
          maxEntries: 20
        })
      });
      
      if (!response.ok) {
        throw new Error(`Context summary failed: ${response.status}`);
      }
      
      const data = await response.json();
      return data.summary || '';
      
    } catch (err: any) {
      setError(err.message || 'Failed to build context summary');
      return '';
    }
  }, [threadId]);
  
  // Get conversation summary
  const getConversationSummary = useCallback(async (): Promise<string> => {
    setError(null);
    
    try {
      const response = await fetch(`${agentIntegration.getConfig().baseUrl}/api/memory/conversation-summary`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          threadId,
          maxMessages: 50
        })
      });
      
      if (!response.ok) {
        throw new Error(`Conversation summary failed: ${response.status}`);
      }
      
      const data = await response.json();
      return data.summary || '';
      
    } catch (err: any) {
      setError(err.message || 'Failed to get conversation summary');
      return '';
    }
  }, [threadId]);
  
  // Auto-save conversation messages
  useEffect(() => {
    if (!enableAutoSave) return;
    
    const handleMessageReceived = async (response: any) => {
      if (response.threadId === threadId) {
        try {
          await addMemory(
            `User: ${response.userMessage}\nAssistant: ${response.content}`,
            'conversation',
            {
              messageType: response.type,
              timestamp: response.timestamp
            }
          );
        } catch (error) {
          console.warn('Failed to auto-save conversation memory:', error);
        }
      }
    };
    
    agentIntegration.on('messageReceived', handleMessageReceived);
    
    return () => {
      agentIntegration.off('messageReceived', handleMessageReceived);
    };
  }, [threadId, enableAutoSave, addMemory]);
  
  // Load initial memory stats
  useEffect(() => {
    getAllMemories();
  }, [getAllMemories]);
  
  return {
    // Memory retrieval
    getRelevantMemories,
    getAllMemories,
    
    // Memory management
    addMemory,
    removeMemory,
    clearAllMemories,
    
    // Context building
    buildContextSummary,
    getConversationSummary,
    
    // Memory statistics
    memoryStats,
    
    // Status
    isLoading,
    error
  };
}
