"""
Widget management system for Chad GPT Deep Agents.
Handles bidirectional communication between agents and React widgets.
"""

import json
import asyncio
import websockets
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import uuid
from dataclasses import dataclass, asdict


@dataclass
class WidgetState:
    """Represents the state of a widget instance."""
    widget_id: str
    widget_type: str
    thread_id: str
    data: Dict[str, Any]
    created_at: datetime
    last_updated: datetime
    is_active: bool
    metadata: Dict[str, Any]


@dataclass
class WidgetMessage:
    """Represents a message between agent and widget."""
    message_id: str
    widget_id: str
    message_type: str  # 'data_update', 'state_change', 'user_interaction', 'command'
    payload: Dict[str, Any]
    timestamp: datetime
    direction: str  # 'agent_to_widget', 'widget_to_agent'


class WidgetManager:
    """
    Manages widget lifecycle, state, and communication for the platform.
    Provides bidirectional communication between agents and React widgets.
    """

    def __init__(self, websocket_port: int = 8002):
        """Initialize widget manager."""
        self.websocket_port = websocket_port
        self.active_widgets: Dict[str, WidgetState] = {}
        self.widget_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.message_handlers: Dict[str, Callable] = {}
        self.websocket_server = None
        
        # Widget type configurations
        self.widget_configs = {
            "pumpfun": {
                "component": "PumpFunWidget",
                "data_sources": ["supabase", "pumpfun_api"],
                "refresh_interval": 30,
                "capabilities": ["token_browsing", "real_time_data", "filtering"]
            },
            "token_chart": {
                "component": "TokenChartWidget", 
                "data_sources": ["gmgn", "dexscreener"],
                "refresh_interval": 10,
                "capabilities": ["price_charts", "technical_analysis", "multi_timeframe"]
            },
            "jupiter": {
                "component": "JupiterWidget",
                "data_sources": ["jupiter_api"],
                "refresh_interval": 5,
                "capabilities": ["token_swapping", "route_optimization", "slippage_control"]
            },
            "phantom": {
                "component": "PhantomWidget",
                "data_sources": ["phantom_sdk"],
                "refresh_interval": 0,
                "capabilities": ["wallet_connection", "transaction_signing", "balance_display"]
            },
            "dexscreener": {
                "component": "DexScreenerWidget",
                "data_sources": ["dexscreener_api"],
                "refresh_interval": 15,
                "capabilities": ["live_charts", "dex_data", "pair_analysis"]
            }
        }
        
        # Initialize WebSocket server
        asyncio.create_task(self._start_websocket_server())
    
    async def _start_websocket_server(self):
        """Start WebSocket server for widget communication."""
        try:
            self.websocket_server = await websockets.serve(
                self._handle_websocket_connection,
                "localhost",
                self.websocket_port
            )
            print(f"Widget WebSocket server started on port {self.websocket_port}")
        except Exception as e:
            print(f"Error starting WebSocket server: {e}")
    
    async def _handle_websocket_connection(self, websocket, path):
        """Handle new WebSocket connections from widgets."""
        try:
            async for message in websocket:
                data = json.loads(message)
                await self._process_widget_message(websocket, data)
        except websockets.exceptions.ConnectionClosed:
            # Handle disconnection
            await self._handle_widget_disconnect(websocket)
        except Exception as e:
            print(f"WebSocket error: {e}")
    
    async def _process_widget_message(self, websocket, data: Dict[str, Any]):
        """Process incoming messages from widgets."""
        message_type = data.get("type")
        widget_id = data.get("widget_id")
        
        if message_type == "widget_register":
            await self._register_widget(websocket, data)
        elif message_type == "widget_data_update":
            await self._handle_widget_data_update(widget_id, data)
        elif message_type == "user_interaction":
            await self._handle_user_interaction(widget_id, data)
        elif message_type == "widget_state_change":
            await self._handle_widget_state_change(widget_id, data)
    
    async def _register_widget(self, websocket, data: Dict[str, Any]):
        """Register a new widget connection."""
        widget_id = data.get("widget_id")
        if widget_id and widget_id in self.active_widgets:
            self.widget_connections[widget_id] = websocket
            
            # Send initial state to widget
            widget_state = self.active_widgets[widget_id]
            await self._send_to_widget(widget_id, {
                "type": "initial_state",
                "data": widget_state.data,
                "metadata": widget_state.metadata
            })
    
    async def _handle_widget_disconnect(self, websocket):
        """Handle widget disconnection."""
        # Find and remove disconnected widget
        widget_id_to_remove = None
        for widget_id, conn in self.widget_connections.items():
            if conn == websocket:
                widget_id_to_remove = widget_id
                break
        
        if widget_id_to_remove:
            del self.widget_connections[widget_id_to_remove]
            if widget_id_to_remove in self.active_widgets:
                self.active_widgets[widget_id_to_remove].is_active = False
    
    async def create_widget(
        self,
        widget_type: str,
        thread_id: str,
        initial_data: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new widget instance."""
        widget_id = str(uuid.uuid4())
        now = datetime.now()
        
        # Validate widget type
        if widget_type not in self.widget_configs:
            raise ValueError(f"Unknown widget type: {widget_type}")
        
        # Create widget state
        widget_state = WidgetState(
            widget_id=widget_id,
            widget_type=widget_type,
            thread_id=thread_id,
            data=initial_data or {},
            created_at=now,
            last_updated=now,
            is_active=True,
            metadata=metadata or {}
        )
        
        self.active_widgets[widget_id] = widget_state
        
        return widget_id
    
    async def update_widget(
        self,
        widget_id: str,
        data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update widget data and notify the frontend."""
        if widget_id not in self.active_widgets:
            return False
        
        widget_state = self.active_widgets[widget_id]
        widget_state.data.update(data)
        widget_state.last_updated = datetime.now()
        
        if metadata:
            widget_state.metadata.update(metadata)
        
        # Send update to widget if connected
        if widget_id in self.widget_connections:
            await self._send_to_widget(widget_id, {
                "type": "data_update",
                "data": data,
                "metadata": metadata or {}
            })
        
        return True
    
    async def _send_to_widget(self, widget_id: str, message: Dict[str, Any]):
        """Send message to a specific widget."""
        if widget_id in self.widget_connections:
            try:
                websocket = self.widget_connections[widget_id]
                await websocket.send(json.dumps(message))
            except Exception as e:
                print(f"Error sending to widget {widget_id}: {e}")
                # Remove failed connection
                if widget_id in self.widget_connections:
                    del self.widget_connections[widget_id]
    
    async def get_widget_state(self, widget_id: str) -> Optional[Dict[str, Any]]:
        """Get current state of a widget."""
        if widget_id in self.active_widgets:
            widget_state = self.active_widgets[widget_id]
            return {
                "widget_id": widget_id,
                "widget_type": widget_state.widget_type,
                "thread_id": widget_state.thread_id,
                "data": widget_state.data,
                "created_at": widget_state.created_at.isoformat(),
                "last_updated": widget_state.last_updated.isoformat(),
                "is_active": widget_state.is_active,
                "metadata": widget_state.metadata
            }
        return None
    
    async def get_active_widgets(self, thread_id: str) -> List[Dict[str, Any]]:
        """Get all active widgets for a conversation thread."""
        widgets = []
        for widget_state in self.active_widgets.values():
            if widget_state.thread_id == thread_id and widget_state.is_active:
                widgets.append({
                    "widget_id": widget_state.widget_id,
                    "widget_type": widget_state.widget_type,
                    "data": widget_state.data,
                    "created_at": widget_state.created_at.isoformat(),
                    "last_updated": widget_state.last_updated.isoformat(),
                    "metadata": widget_state.metadata
                })
        return widgets
    
    async def close_widget(self, widget_id: str) -> bool:
        """Close and remove a widget."""
        if widget_id not in self.active_widgets:
            return False
        
        # Send close command to widget
        if widget_id in self.widget_connections:
            await self._send_to_widget(widget_id, {
                "type": "widget_close",
                "widget_id": widget_id
            })
            del self.widget_connections[widget_id]
        
        # Remove from active widgets
        del self.active_widgets[widget_id]
        return True
    
    async def broadcast_to_thread(self, thread_id: str, message: Dict[str, Any]):
        """Broadcast message to all widgets in a thread."""
        for widget_state in self.active_widgets.values():
            if widget_state.thread_id == thread_id and widget_state.is_active:
                await self._send_to_widget(widget_state.widget_id, message)
    
    async def get_widget_capabilities(self, widget_type: str) -> Dict[str, Any]:
        """Get capabilities and configuration for a widget type."""
        return self.widget_configs.get(widget_type, {})
    
    async def _handle_widget_data_update(self, widget_id: str, data: Dict[str, Any]):
        """Handle data updates from widgets."""
        if widget_id in self.active_widgets:
            widget_state = self.active_widgets[widget_id]
            widget_state.data.update(data.get("data", {}))
            widget_state.last_updated = datetime.now()
            
            # Trigger any registered handlers
            if "widget_data_update" in self.message_handlers:
                await self.message_handlers["widget_data_update"](widget_id, data)
    
    async def _handle_user_interaction(self, widget_id: str, data: Dict[str, Any]):
        """Handle user interactions from widgets."""
        interaction_type = data.get("interaction_type")
        interaction_data = data.get("interaction_data", {})
        
        # Log interaction
        message = WidgetMessage(
            message_id=str(uuid.uuid4()),
            widget_id=widget_id,
            message_type="user_interaction",
            payload={
                "interaction_type": interaction_type,
                "data": interaction_data
            },
            timestamp=datetime.now(),
            direction="widget_to_agent"
        )
        
        # Trigger handlers
        if "user_interaction" in self.message_handlers:
            await self.message_handlers["user_interaction"](widget_id, data)
    
    async def _handle_widget_state_change(self, widget_id: str, data: Dict[str, Any]):
        """Handle widget state changes."""
        if widget_id in self.active_widgets:
            widget_state = self.active_widgets[widget_id]
            new_state = data.get("state")
            
            if new_state == "minimized":
                widget_state.metadata["minimized"] = True
            elif new_state == "maximized":
                widget_state.metadata["minimized"] = False
            elif new_state == "closed":
                widget_state.is_active = False
            
            widget_state.last_updated = datetime.now()
    
    def register_message_handler(self, message_type: str, handler: Callable):
        """Register a handler for specific message types."""
        self.message_handlers[message_type] = handler
    
    async def cleanup(self):
        """Cleanup widget manager resources."""
        # Close all widget connections
        for widget_id in list(self.widget_connections.keys()):
            await self.close_widget(widget_id)
        
        # Stop WebSocket server
        if self.websocket_server:
            self.websocket_server.close()
            await self.websocket_server.wait_closed()
    
    async def export_widget_states(self, thread_id: str) -> Dict[str, Any]:
        """Export widget states for a thread."""
        widgets = await self.get_active_widgets(thread_id)
        return {
            "thread_id": thread_id,
            "export_timestamp": datetime.now().isoformat(),
            "widgets": widgets
        }
    
    async def import_widget_states(self, widget_data: Dict[str, Any]):
        """Import widget states from exported data."""
        thread_id = widget_data["thread_id"]
        
        for widget_info in widget_data["widgets"]:
            widget_id = await self.create_widget(
                widget_type=widget_info["widget_type"],
                thread_id=thread_id,
                initial_data=widget_info["data"],
                metadata=widget_info["metadata"]
            )
            
            # Update timestamps
            if widget_id in self.active_widgets:
                widget_state = self.active_widgets[widget_id]
                widget_state.created_at = datetime.fromisoformat(widget_info["created_at"])
                widget_state.last_updated = datetime.fromisoformat(widget_info["last_updated"])
