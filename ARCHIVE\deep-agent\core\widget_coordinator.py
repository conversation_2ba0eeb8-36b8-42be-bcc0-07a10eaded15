"""
Widget Coordinator - Manages all widget operations for the Deep Agent system.
Provides centralized widget management with real-time communication and state synchronization.
"""

import asyncio
import json
import websockets
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid


class WidgetCoordinator:
    """
    Coordinates all widget operations for the Deep Agent system.
    Manages widget lifecycle, state, and communication with the frontend.
    """

    def __init__(self, websocket_port: int = 8004):
        """Initialize widget coordinator."""
        self.websocket_port = websocket_port
        self.active_widgets: Dict[str, Dict[str, Any]] = {}
        self.thread_widgets: Dict[str, List[str]] = {}
        self.widget_connections: Dict[str, Any] = {}
        self.websocket_server = None
        
        # Widget type configurations
        self.widget_configs = {
            "pumpfun": {
                "name": "PumpFun Widget",
                "description": "Interactive meme token browser",
                "default_data": {"category": "new", "auto_refresh": True}
            },
            "token_chart": {
                "name": "Token Chart Widget",
                "description": "Interactive token price chart",
                "default_data": {"timeframe": "1h", "auto_refresh": True}
            },
            "jupiter": {
                "name": "Jupiter Swap Widget",
                "description": "Token swap interface",
                "default_data": {"slippage": 0.5, "auto_route": True}
            },
            "phantom": {
                "name": "Phantom Wallet Widget",
                "description": "Wallet connection and management",
                "default_data": {"auto_connect": False, "show_balance": True}
            },
            "dexscreener": {
                "name": "DexScreener Widget",
                "description": "DEX trading data and analytics",
                "default_data": {"view_mode": "marketcap", "auto_refresh": True}
            }
        }
        
        # Start WebSocket server
        asyncio.create_task(self._start_websocket_server())
    
    async def _start_websocket_server(self):
        """Start WebSocket server for widget communication."""
        try:
            self.websocket_server = await websockets.serve(
                self._handle_websocket_connection,
                "localhost",
                self.websocket_port
            )
            print(f"✅ Widget WebSocket server started on port {self.websocket_port}")
        except Exception as e:
            print(f"❌ Failed to start widget WebSocket server: {e}")
    
    async def _handle_websocket_connection(self, websocket, path):
        """Handle WebSocket connections from widgets."""
        connection_id = str(uuid.uuid4())
        self.widget_connections[connection_id] = {
            "websocket": websocket,
            "connected_at": datetime.now(),
            "widgets": []
        }
        
        try:
            async for message in websocket:
                await self._process_widget_message(websocket, json.loads(message))
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            print(f"Widget WebSocket error: {e}")
        finally:
            if connection_id in self.widget_connections:
                del self.widget_connections[connection_id]
    
    async def _process_widget_message(self, websocket, message: Dict[str, Any]):
        """Process incoming messages from widgets."""
        message_type = message.get("type")
        
        if message_type == "widget_register":
            widget_id = message.get("widget_id")
            if widget_id in self.active_widgets:
                # Send current widget state to newly connected widget
                await websocket.send(json.dumps({
                    "type": "widget_state",
                    "widget_id": widget_id,
                    "data": self.active_widgets[widget_id]["data"]
                }))
        
        elif message_type == "widget_update":
            widget_id = message.get("widget_id")
            update_data = message.get("data", {})
            
            if widget_id in self.active_widgets:
                # Update widget state
                self.active_widgets[widget_id]["data"].update(update_data)
                self.active_widgets[widget_id]["last_updated"] = datetime.now().isoformat()
                
                # Broadcast update to all connections
                await self._broadcast_widget_update(widget_id, update_data)
        
        elif message_type == "widget_action":
            widget_id = message.get("widget_id")
            action = message.get("action")
            action_data = message.get("data", {})
            
            # Handle widget-specific actions
            await self._handle_widget_action(widget_id, action, action_data)
    
    async def create_widget(
        self,
        widget_type: str,
        thread_id: str,
        initial_data: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new widget instance."""
        widget_id = f"{widget_type}_{str(uuid.uuid4())[:8]}"
        
        # Get widget configuration
        widget_config = self.widget_configs.get(widget_type, {})
        
        # Prepare widget data
        widget_data = widget_config.get("default_data", {}).copy()
        if initial_data:
            widget_data.update(initial_data)
        
        # Create widget record
        widget_record = {
            "id": widget_id,
            "type": widget_type,
            "thread_id": thread_id,
            "name": widget_config.get("name", widget_type.title()),
            "description": widget_config.get("description", ""),
            "data": widget_data,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "status": "active"
        }
        
        # Store widget
        self.active_widgets[widget_id] = widget_record
        
        # Add to thread widgets
        if thread_id not in self.thread_widgets:
            self.thread_widgets[thread_id] = []
        self.thread_widgets[thread_id].append(widget_id)
        
        # Notify frontend about new widget
        await self._broadcast_widget_creation(widget_record)
        
        return widget_id
    
    async def update_widget_data(
        self,
        widget_id: str,
        data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update widget data."""
        if widget_id not in self.active_widgets:
            return False
        
        # Update widget data
        self.active_widgets[widget_id]["data"].update(data)
        self.active_widgets[widget_id]["last_updated"] = datetime.now().isoformat()
        
        if metadata:
            self.active_widgets[widget_id]["metadata"].update(metadata)
        
        # Broadcast update
        await self._broadcast_widget_update(widget_id, data)
        
        return True
    
    async def close_widget(self, widget_id: str) -> bool:
        """Close and remove a widget."""
        if widget_id not in self.active_widgets:
            return False
        
        widget = self.active_widgets[widget_id]
        thread_id = widget["thread_id"]
        
        # Remove from active widgets
        del self.active_widgets[widget_id]
        
        # Remove from thread widgets
        if thread_id in self.thread_widgets and widget_id in self.thread_widgets[thread_id]:
            self.thread_widgets[thread_id].remove(widget_id)
        
        # Notify frontend
        await self._broadcast_widget_closure(widget_id)
        
        return True
    
    async def get_active_widgets(self, thread_id: str) -> List[Dict[str, Any]]:
        """Get all active widgets for a thread."""
        widget_ids = self.thread_widgets.get(thread_id, [])
        widgets = []
        
        for widget_id in widget_ids:
            if widget_id in self.active_widgets:
                widgets.append(self.active_widgets[widget_id])
        
        return widgets
    
    async def update_widget(
        self,
        widget_type: str,
        thread_id: str,
        data: Dict[str, Any]
    ) -> str:
        """Update or create a widget of the specified type."""
        # Find existing widget of this type in the thread
        existing_widgets = await self.get_active_widgets(thread_id)
        existing_widget = next(
            (w for w in existing_widgets if w["type"] == widget_type),
            None
        )
        
        if existing_widget:
            # Update existing widget
            await self.update_widget_data(existing_widget["id"], data)
            return existing_widget["id"]
        else:
            # Create new widget
            return await self.create_widget(widget_type, thread_id, data)
    
    async def broadcast_to_widgets(
        self,
        thread_id: str,
        message: Dict[str, Any],
        target_widgets: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Broadcast a message to widgets in a thread."""
        if target_widgets is None:
            target_widgets = self.thread_widgets.get(thread_id, [])
        
        broadcast_count = 0
        
        for connection_data in self.widget_connections.values():
            try:
                await connection_data["websocket"].send(json.dumps({
                    "type": "broadcast",
                    "thread_id": thread_id,
                    "target_widgets": target_widgets,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }))
                broadcast_count += 1
            except Exception as e:
                print(f"Failed to broadcast to widget connection: {e}")
        
        return {
            "success": True,
            "broadcast_count": broadcast_count,
            "target_widgets": target_widgets
        }
    
    async def _broadcast_widget_creation(self, widget_record: Dict[str, Any]):
        """Broadcast widget creation to all connections."""
        message = {
            "type": "widget_created",
            "widget": widget_record,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast_to_all_connections(message)
    
    async def _broadcast_widget_update(self, widget_id: str, update_data: Dict[str, Any]):
        """Broadcast widget update to all connections."""
        message = {
            "type": "widget_updated",
            "widget_id": widget_id,
            "data": update_data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast_to_all_connections(message)
    
    async def _broadcast_widget_closure(self, widget_id: str):
        """Broadcast widget closure to all connections."""
        message = {
            "type": "widget_closed",
            "widget_id": widget_id,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast_to_all_connections(message)
    
    async def _broadcast_to_all_connections(self, message: Dict[str, Any]):
        """Broadcast message to all WebSocket connections."""
        if not self.widget_connections:
            return
        
        message_json = json.dumps(message)
        
        for connection_data in self.widget_connections.values():
            try:
                await connection_data["websocket"].send(message_json)
            except Exception as e:
                print(f"Failed to broadcast to connection: {e}")
    
    async def _handle_widget_action(
        self,
        widget_id: str,
        action: str,
        action_data: Dict[str, Any]
    ):
        """Handle widget-specific actions."""
        if widget_id not in self.active_widgets:
            return
        
        widget = self.active_widgets[widget_id]
        widget_type = widget["type"]
        
        # Handle type-specific actions
        if widget_type == "pumpfun" and action == "category_change":
            new_category = action_data.get("category", "new")
            await self.update_widget_data(widget_id, {"category": new_category})
        
        elif widget_type == "token_chart" and action == "timeframe_change":
            new_timeframe = action_data.get("timeframe", "1h")
            await self.update_widget_data(widget_id, {"timeframe": new_timeframe})
        
        elif widget_type == "jupiter" and action == "swap_tokens":
            input_token = action_data.get("input_token")
            output_token = action_data.get("output_token")
            if input_token and output_token:
                await self.update_widget_data(widget_id, {
                    "input_token": input_token,
                    "output_token": output_token
                })
    
    async def cleanup(self):
        """Cleanup widget coordinator resources."""
        # Close all widget connections
        for connection_data in self.widget_connections.values():
            try:
                await connection_data["websocket"].close()
            except:
                pass
        
        # Stop WebSocket server
        if self.websocket_server:
            self.websocket_server.close()
            await self.websocket_server.wait_closed()
        
        # Clear all data
        self.active_widgets.clear()
        self.thread_widgets.clear()
        self.widget_connections.clear()
