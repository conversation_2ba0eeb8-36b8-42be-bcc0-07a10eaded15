"""
Planning tools for the Chad GPT Deep Agent.
These tools help the agent create, update, and track progress on complex tasks.
"""

from langchain_core.tools import tool
from typing import List, Dict, Any
import json
import uuid
from datetime import datetime


@tool
def create_plan(task_description: str, subtasks: List[str]) -> Dict[str, Any]:
    """
    Create a structured plan for a complex task.
    
    Args:
        task_description: High-level description of the main task
        subtasks: List of specific subtasks to accomplish the main task
        
    Returns:
        Dictionary containing the plan structure with unique IDs for tracking
    """
    plan_id = str(uuid.uuid4())[:8]
    
    plan = {
        "plan_id": plan_id,
        "task_description": task_description,
        "created_at": datetime.now().isoformat(),
        "status": "active",
        "subtasks": []
    }
    
    for i, subtask in enumerate(subtasks):
        subtask_obj = {
            "id": f"{plan_id}-{i+1}",
            "description": subtask,
            "status": "pending",  # pending, in_progress, completed, skipped
            "created_at": datetime.now().isoformat(),
            "completed_at": None
        }
        plan["subtasks"].append(subtask_obj)
    
    return {
        "success": True,
        "plan": plan,
        "message": f"Created plan '{plan_id}' with {len(subtasks)} subtasks"
    }


@tool
def update_plan_progress(plan_id: str, subtask_id: str, status: str, notes: str = "") -> Dict[str, Any]:
    """
    Update the progress of a specific subtask in the plan.
    
    Args:
        plan_id: The ID of the plan to update
        subtask_id: The ID of the specific subtask to update
        status: New status (pending, in_progress, completed, skipped)
        notes: Optional notes about the progress or completion
        
    Returns:
        Dictionary confirming the update
    """
    timestamp = datetime.now().isoformat()
    
    update_info = {
        "plan_id": plan_id,
        "subtask_id": subtask_id,
        "previous_status": "unknown",  # In a real implementation, this would be retrieved
        "new_status": status,
        "notes": notes,
        "updated_at": timestamp
    }
    
    if status == "completed":
        update_info["completed_at"] = timestamp
    
    return {
        "success": True,
        "update": update_info,
        "message": f"Updated subtask {subtask_id} status to '{status}'"
    }


@tool
def revise_plan(plan_id: str, new_subtasks: List[str], reason: str) -> Dict[str, Any]:
    """
    Revise an existing plan by adding new subtasks or modifying the approach.
    
    Args:
        plan_id: The ID of the plan to revise
        new_subtasks: List of new or modified subtasks
        reason: Explanation for why the plan is being revised
        
    Returns:
        Dictionary containing the revised plan information
    """
    revision_id = str(uuid.uuid4())[:8]
    timestamp = datetime.now().isoformat()
    
    revision = {
        "revision_id": revision_id,
        "original_plan_id": plan_id,
        "reason": reason,
        "revised_at": timestamp,
        "new_subtasks": []
    }
    
    for i, subtask in enumerate(new_subtasks):
        subtask_obj = {
            "id": f"{plan_id}-rev{revision_id}-{i+1}",
            "description": subtask,
            "status": "pending",
            "created_at": timestamp,
            "completed_at": None
        }
        revision["new_subtasks"].append(subtask_obj)
    
    return {
        "success": True,
        "revision": revision,
        "message": f"Revised plan {plan_id} with {len(new_subtasks)} new subtasks"
    }


@tool
def get_plan_summary(plan_id: str) -> Dict[str, Any]:
    """
    Get a summary of the current plan status and progress.
    
    Args:
        plan_id: The ID of the plan to summarize
        
    Returns:
        Dictionary containing plan summary and progress statistics
    """
    # In a real implementation, this would retrieve the actual plan from storage
    # For now, we'll return a template summary
    
    summary = {
        "plan_id": plan_id,
        "status": "active",
        "progress": {
            "total_subtasks": 5,
            "completed": 2,
            "in_progress": 1,
            "pending": 2,
            "completion_percentage": 40
        },
        "next_actions": [
            "Continue working on current in-progress subtask",
            "Review completed subtasks for quality",
            "Begin next pending subtask"
        ],
        "estimated_completion": "Based on current progress, estimated 60% remaining"
    }
    
    return {
        "success": True,
        "summary": summary,
        "message": f"Retrieved summary for plan {plan_id}"
    }


# List of all planning tools
PLANNING_TOOLS = [
    create_plan,
    update_plan_progress,
    revise_plan,
    get_plan_summary
]
