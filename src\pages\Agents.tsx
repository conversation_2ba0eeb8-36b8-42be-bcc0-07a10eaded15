import { useState } from 'react';
import { Plus, Users, ArrowLeft, Sparkles, Star, Store, Search } from 'lucide-react';
import { Link } from 'react-router-dom';
import clsx from 'clsx';

type AgentCategory = 'standard' | 'custom' | 'marketplace';

interface Agent {
  id: string;
  name: string;
  description: string;
  model: string;
  tools: string[];
  createdAt: Date;
  lastActive: Date;
  category: AgentCategory;
  author?: string;
  downloads?: number;
  rating?: number;
}

const standardAgents: Agent[] = [
  {
    id: 'research-agent',
    name: 'Research Assistant',
    description: 'Specialized in web research, data analysis, and report generation',
    model: 'GPT-4',
    tools: ['Web Search', 'PDF Reader', 'Data Analysis'],
    createdAt: new Date(),
    lastActive: new Date(),
    category: 'standard'
  },
  {
    id: 'code-agent',
    name: 'Code Assistant',
    description: 'Expert in code review, debugging, and development assistance',
    model: 'Claude-3',
    tools: ['GitHub', 'Code Interpreter', 'Documentation'],
    createdAt: new Date(),
    lastActive: new Date(),
    category: 'standard'
  }
];

const marketplaceAgents: Agent[] = [
  {
    id: 'crypto-analyst',
    name: 'Crypto Analyst Pro',
    description: 'Advanced cryptocurrency market analysis and trading signals',
    model: 'GPT-4',
    tools: ['Market Data', 'Technical Analysis', 'News Feed'],
    createdAt: new Date(),
    lastActive: new Date(),
    category: 'marketplace',
    author: '@tradingmaster',
    downloads: 1234,
    rating: 4.8
  },
  {
    id: 'nft-scout',
    name: 'NFT Scout',
    description: 'Discover trending NFT collections and market opportunities',
    model: 'Claude-3',
    tools: ['NFT Analytics', 'Social Listening', 'Rarity Check'],
    createdAt: new Date(),
    lastActive: new Date(),
    category: 'marketplace',
    author: '@nftexpert',
    downloads: 856,
    rating: 4.6
  }
];

export default function Agents() {
  const [agents] = useState<Agent[]>([]);
  const [activeCategory, setActiveCategory] = useState<AgentCategory>('standard');
  const [searchTerm, setSearchTerm] = useState('');

  const displayedAgents = () => {
    switch (activeCategory) {
      case 'standard':
        return standardAgents;
      case 'custom':
        return agents;
      case 'marketplace':
        return marketplaceAgents;
      default:
        return [];
    }
  };

  const filteredAgents = displayedAgents().filter(agent =>
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex-1 flex flex-col overflow-hidden bg-black">
      <div className="p-8 border-b border-[#181818]">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-4">
            <Link
              to="/"
              className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
            >
              <ArrowLeft className="w-5 h-5 group-hover:translate-x-[-2px] transition-transform" />
            </Link>
            <h1 className="text-xl font-semibold">Agents</h1>
          </div>
          <button className="flex items-center gap-2 px-4 py-2 bg-[#22c55e] text-black rounded-lg hover:bg-[#22c55e]/90 transition-all duration-200">
            <Plus className="w-4 h-4" />
            <span className="text-sm font-medium">New Agent</span>
          </button>
        </div>
        <p className="text-sm text-[#666] mt-2">Discover, create, and manage AI agents.</p>

        <div className="mt-6 space-y-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setActiveCategory('standard')}
              className={clsx(
                "flex items-center gap-2 px-4 py-2 rounded-lg text-sm transition-all duration-200",
                activeCategory === 'standard' ? "bg-[#22c55e] text-black" : "bg-[#111] text-[#666] hover:bg-[#222] hover:text-white"
              )}
            >
              <Star className="w-4 h-4" />
              <span>Standard</span>
            </button>
            <button
              onClick={() => setActiveCategory('custom')}
              className={clsx(
                "flex items-center gap-2 px-4 py-2 rounded-lg text-sm transition-all duration-200",
                activeCategory === 'custom' ? "bg-[#22c55e] text-black" : "bg-[#111] text-[#666] hover:bg-[#222] hover:text-white"
              )}
            >
              <Sparkles className="w-4 h-4" />
              <span>My Agents</span>
            </button>
            <button
              onClick={() => setActiveCategory('marketplace')}
              className={clsx(
                "flex items-center gap-2 px-4 py-2 rounded-lg text-sm transition-all duration-200",
                activeCategory === 'marketplace' ? "bg-[#22c55e] text-black" : "bg-[#111] text-[#666] hover:bg-[#222] hover:text-white"
              )}
            >
              <Store className="w-4 h-4" />
              <span>Marketplace</span>
            </button>
          </div>

          <div className="relative">
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-[#666] w-4 h-4" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-[#111] rounded-xl py-3 pl-11 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-[#222] placeholder-[#666] transition-all duration-200"
              placeholder={`Search ${activeCategory} agents...`}
            />
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-8">
        {filteredAgents.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
            <div className="w-20 h-20 bg-[#111] rounded-2xl flex items-center justify-center">
              <Users className="w-8 h-8 text-[#666]" />
            </div>
            <h2 className="text-lg font-medium">
              {searchTerm ? 'No matching agents found' : `No ${activeCategory} agents yet`}
            </h2>
            <p className="text-sm text-[#666] max-w-md">
              {activeCategory === 'custom' ? (
                "Create your first AI agent to start building autonomous workflows. " +
                "Each agent can be customized with different models and tools."
              ) : activeCategory === 'marketplace' ? (
                "Explore the marketplace to find specialized agents created by the community."
              ) : (
                "Get started with our standard agents, designed for common use cases."
              )}
            </p>
            {activeCategory === 'custom' && (
              <button className="flex items-center gap-2 px-4 py-2 bg-[#22c55e] text-black rounded-lg hover:bg-[#22c55e]/90 transition-all duration-200 mt-4">
                <Plus className="w-4 h-4" />
                <span className="text-sm font-medium">Create Agent</span>
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAgents.map(agent => (
              <div
                key={agent.id}
                className="bg-[#111] rounded-xl p-6 hover:ring-1 hover:ring-[#22c55e] transition-all duration-200 cursor-pointer group"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-medium">{agent.name}</h3>
                  <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full">
                    {agent.model}
                  </span>
                </div>
                <p className="text-sm text-[#666] mb-4">{agent.description}</p>
                {agent.category === 'marketplace' && (
                  <div className="flex items-center justify-between text-sm mb-4">
                    <div className="text-[#666]">{agent.author}</div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-[#22c55e]" />
                        <span>{agent.rating}</span>
                      </div>
                      <div className="text-[#666]">{agent.downloads} downloads</div>
                    </div>
                  </div>
                )}
                <div className="space-y-2 mb-4">
                  <div className="text-xs text-[#666]">
                    Created {agent.createdAt.toLocaleDateString()}
                  </div>
                  <div className="text-xs text-[#666]">
                    Last active {agent.lastActive.toLocaleDateString()}
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {agent.tools.map((tool, index) => (
                    <span
                      key={index}
                      className="text-xs bg-[#0A0A0A] px-2 py-1 rounded-lg text-[#666]"
                    >
                      {tool}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}