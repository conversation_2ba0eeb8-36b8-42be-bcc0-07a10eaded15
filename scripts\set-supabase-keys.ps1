param()
$ErrorActionPreference='Stop'
$projRoot = (Resolve-Path "$PSScriptRoot\..\").Path
$tmp = Join-Path $projRoot ".secrets-tmp"
New-Item -ItemType Directory -Force -Path $tmp | Out-Null

$sshKey = "c:\Users\<USER>\Desktop\Oracle\ssh-key-2025-05-08.key"
$remote = "opc@*************:/home/<USER>/ap3x-pump-docker/.env"
$local = Join-Path $tmp "server-supabase.env"

# Copy remote env file
& scp -i $sshKey $remote $local | Out-Null
if (!(Test-Path $local)) { throw "Failed to copy $remote" }

# Parse keys
$lines = Get-Content $local
$anonLine = ($lines | Where-Object { $_ -match '^\s*ANON_KEY\s*=' } | Select-Object -First 1)
$serviceLine = ($lines | Where-Object { $_ -match '^\s*SERVICE_ROLE_KEY\s*=' } | Select-Object -First 1)
if (-not $anonLine -or -not $serviceLine) { throw "Keys not found in remote .env" }
$anon = $anonLine -replace '^\s*ANON_KEY\s*=\s*',''
$service = $serviceLine -replace '^\s*SERVICE_ROLE_KEY\s*=\s*',''

# Update API server .env
$apiEnv = Join-Path $projRoot 'AP3X-PumP2\api-server\.env'
$api = @()
if (Test-Path $apiEnv) { $api = Get-Content $apiEnv }
$api = $api | Where-Object { $_ -notmatch '^\s*SUPABASE_URL\s*=' -and $_ -notmatch '^\s*SUPABASE_SERVICE_ROLE_KEY\s*=' }
$api += 'SUPABASE_URL=http://*************:8082/rest/v1'
$api += ('SUPABASE_SERVICE_ROLE_KEY=' + $service)
Set-Content -Path $apiEnv -Value $api -NoNewline:$false

# Update frontend .env.local
$feEnv = Join-Path $projRoot '.env.local'
$fe = @()
if (Test-Path $feEnv) { $fe = Get-Content $feEnv }
$fe = $fe | Where-Object { $_ -notmatch '^\s*VITE_SUPABASE_REST_URL\s*=' -and $_ -notmatch '^\s*VITE_SUPABASE_ANON_KEY\s*=' }
$fe += 'VITE_SUPABASE_REST_URL=http://*************:8082/rest/v1'
$fe += ('VITE_SUPABASE_ANON_KEY=' + $anon)
Set-Content -Path $feEnv -Value $fe -NoNewline:$false

# Cleanup temp
Remove-Item $local -Force
Write-Host 'OK'
