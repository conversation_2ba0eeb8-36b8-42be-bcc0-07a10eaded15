"""
End-to-end workflow tests for Chad GPT Deep Agents.
Tests complete workflows from user input to final output.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from ..src.core.platform_agent_loop import PlatformAgentLoop
from ..domains.cryptocurrency.agent import CryptocurrencyAgent
from ..domains.research.agent import ResearchAgent
from ..domains.development.agent import DevelopmentAgent


class TestEndToEndWorkflows:
    """Test suite for complete end-to-end workflows."""

    @pytest.fixture
    async def platform_agent(self):
        """Create a test platform agent instance."""
        config = {
            "model_name": "test-model",
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        with patch.dict('os.environ', {'OPENROUTER_API_KEY': 'test-key'}):
            agent = PlatformAgentLoop(config)
            yield agent
            await agent.cleanup()

    @pytest.fixture
    async def crypto_agent(self, platform_agent):
        """Create a cryptocurrency analysis agent."""
        return CryptocurrencyAgent(platform_agent)

    @pytest.fixture
    async def research_agent(self, platform_agent):
        """Create a research specialist agent."""
        return ResearchAgent(platform_agent)

    @pytest.fixture
    async def development_agent(self, platform_agent):
        """Create a development assistant agent."""
        return DevelopmentAgent(platform_agent)

    @pytest.mark.asyncio
    async def test_comprehensive_token_analysis_workflow(self, crypto_agent):
        """Test complete token analysis workflow from start to finish."""
        token_address = "******************************************"
        chain = "ethereum"
        thread_id = "test_token_analysis_001"

        # Mock external API calls
        with patch.object(crypto_agent.market_analyzer, 'get_market_data') as mock_market_data, \
             patch.object(crypto_agent, '_get_basic_token_info') as mock_basic_info, \
             patch.object(crypto_agent, '_perform_onchain_analysis') as mock_onchain, \
             patch.object(crypto_agent, '_perform_technical_analysis') as mock_technical, \
             patch.object(crypto_agent, '_assess_risks') as mock_risks, \
             patch.object(crypto_agent, '_generate_recommendations') as mock_recommendations:

            # Setup mock responses
            mock_basic_info.return_value = {
                "address": token_address,
                "name": "Test Token",
                "symbol": "TEST",
                "decimals": 18,
                "total_supply": 1000000000
            }

            mock_market_data.return_value = {
                "price": 1.50,
                "market_cap": 1500000000,
                "volume_24h": 50000000,
                "change_24h": 5.2
            }

            mock_onchain.return_value = {
                "holder_count": 10000,
                "top_holders": [],
                "transaction_volume_24h": 100000,
                "liquidity_pools": []
            }

            mock_technical.return_value = {
                "trend": "bullish",
                "support_levels": [1.20, 1.00],
                "resistance_levels": [1.80, 2.00],
                "rsi": 65
            }

            mock_risks.return_value = {
                "overall_risk": "medium",
                "risk_factors": ["High volatility", "Limited trading history"]
            }

            mock_recommendations.return_value = {
                "recommendation": "BUY",
                "confidence": 0.75,
                "target_price": 2.00,
                "reasoning": ["Strong fundamentals", "Bullish technical indicators"]
            }

            # Execute workflow
            result = await crypto_agent.analyze_token(
                token_address=token_address,
                chain=chain,
                analysis_depth="comprehensive",
                thread_id=thread_id
            )

            # Verify workflow completion
            assert result["success"] is True
            assert result["token_address"] == token_address
            assert result["chain"] == chain
            assert "workflow_id" in result
            assert "analysis" in result

            # Verify analysis components
            analysis = result["analysis"]
            assert "basic_info" in analysis
            assert "market_data" in analysis
            assert "onchain_analysis" in analysis
            assert "technical_analysis" in analysis
            assert "risk_assessment" in analysis
            assert "recommendations" in analysis

            # Verify specific analysis results
            assert analysis["basic_info"]["symbol"] == "TEST"
            assert analysis["market_data"]["price"] == 1.50
            assert analysis["recommendations"]["recommendation"] == "BUY"

    @pytest.mark.asyncio
    async def test_market_research_workflow(self, research_agent):
        """Test complete market research workflow."""
        topic = "DeFi Protocol Trends"
        scope = "comprehensive"
        timeframe = "90d"
        thread_id = "test_research_001"

        # Mock research methods
        with patch.object(research_agent, '_analyze_research_topic') as mock_topic, \
             patch.object(research_agent, '_analyze_news_coverage') as mock_news, \
             patch.object(research_agent, '_analyze_social_sentiment') as mock_sentiment, \
             patch.object(research_agent, '_gather_industry_data') as mock_industry, \
             patch.object(research_agent, '_analyze_competitive_landscape') as mock_competitive, \
             patch.object(research_agent, '_analyze_trends_and_forecast') as mock_trends, \
             patch.object(research_agent, '_generate_research_insights') as mock_insights:

            # Setup mock responses
            mock_topic.return_value = {
                "primary_keywords": ["defi", "protocols", "trends"],
                "research_questions": ["What are the key DeFi trends?"],
                "scope_definition": "Analysis of DeFi protocol trends"
            }

            mock_news.return_value = {
                "total_articles": 250,
                "sentiment_distribution": {"positive": 0.6, "neutral": 0.3, "negative": 0.1},
                "key_themes": ["yield farming", "governance tokens", "cross-chain"]
            }

            mock_sentiment.return_value = {
                "overall_sentiment": "positive",
                "sentiment_score": 0.7,
                "volume_trend": "increasing"
            }

            mock_industry.return_value = {
                "market_size": "$100B",
                "growth_rate": "25% YoY",
                "key_players": ["Uniswap", "Aave", "Compound"]
            }

            mock_competitive.return_value = {
                "top_protocols": ["Uniswap", "Aave", "Compound"],
                "market_share": {"Uniswap": 0.3, "Aave": 0.2, "Compound": 0.15}
            }

            mock_trends.return_value = {
                "emerging_trends": ["Layer 2 adoption", "Real-world assets"],
                "declining_trends": ["Simple yield farming"]
            }

            mock_insights.return_value = {
                "key_insights": ["DeFi is maturing", "Focus on utility"],
                "recommendations": ["Invest in infrastructure", "Watch regulatory developments"]
            }

            # Execute workflow
            result = await research_agent.conduct_market_research(
                topic=topic,
                scope=scope,
                timeframe=timeframe,
                thread_id=thread_id
            )

            # Verify workflow completion
            assert result["success"] is True
            assert result["topic"] == topic
            assert result["scope"] == scope
            assert "workflow_id" in result
            assert "research_results" in result

            # Verify research components
            research_results = result["research_results"]
            assert "topic_analysis" in research_results
            assert "news_analysis" in research_results
            assert "sentiment_analysis" in research_results
            assert "industry_data" in research_results
            assert "competitive_analysis" in research_results
            assert "trend_analysis" in research_results
            assert "insights" in research_results

    @pytest.mark.asyncio
    async def test_widget_development_workflow(self, development_agent):
        """Test complete widget development workflow."""
        widget_name = "TokenTracker"
        widget_description = "Real-time token price tracking widget"
        features = ["price_display", "alerts", "portfolio_tracking"]
        data_sources = ["coingecko", "dexscreener"]
        thread_id = "test_widget_dev_001"

        # Mock development methods
        with patch.object(development_agent, '_analyze_widget_requirements') as mock_requirements, \
             patch.object(development_agent, '_design_widget_architecture') as mock_architecture, \
             patch.object(development_agent, '_generate_widget_component') as mock_component, \
             patch.object(development_agent, '_generate_widget_types') as mock_types, \
             patch.object(development_agent, '_generate_widget_styles') as mock_styles, \
             patch.object(development_agent, '_generate_widget_integration') as mock_integration, \
             patch.object(development_agent, '_generate_widget_tests') as mock_tests, \
             patch.object(development_agent, '_generate_widget_documentation') as mock_docs:

            # Setup mock responses
            mock_requirements.return_value = {
                "widget_name": widget_name,
                "features": features,
                "data_sources": data_sources,
                "technical_requirements": ["React 18", "TypeScript", "WebSocket"]
            }

            mock_architecture.return_value = {
                "components": ["TokenTracker", "TokenRow", "AddTokenInput"],
                "hooks": ["useTokenData", "useWebSocket"],
                "services": ["TokenService", "AlertService"]
            }

            mock_component.return_value = "// React component code here"
            mock_types.return_value = "// TypeScript definitions here"
            mock_styles.return_value = "// Tailwind CSS styles here"
            mock_integration.return_value = "// Integration code here"
            mock_tests.return_value = "// Test suite here"
            mock_docs.return_value = "# Widget Documentation"

            # Execute workflow
            result = await development_agent.create_custom_widget(
                widget_name=widget_name,
                widget_description=widget_description,
                features=features,
                data_sources=data_sources,
                thread_id=thread_id
            )

            # Verify workflow completion
            assert result["success"] is True
            assert result["widget_name"] == widget_name
            assert "workflow_id" in result
            assert "widget_results" in result

            # Verify development components
            widget_results = result["widget_results"]
            assert "requirements" in widget_results
            assert "architecture" in widget_results
            assert "generated_code" in widget_results
            assert "documentation" in widget_results

            # Verify generated code components
            generated_code = widget_results["generated_code"]
            assert "component" in generated_code
            assert "types" in generated_code
            assert "styles" in generated_code
            assert "integration" in generated_code
            assert "tests" in generated_code

    @pytest.mark.asyncio
    async def test_multi_agent_collaboration_workflow(self, platform_agent):
        """Test workflow involving multiple specialized agents."""
        thread_id = "test_multi_agent_001"
        
        # Create specialized agents
        crypto_agent = CryptocurrencyAgent(platform_agent)
        research_agent = ResearchAgent(platform_agent)
        development_agent = DevelopmentAgent(platform_agent)

        # Mock agent methods
        with patch.object(crypto_agent, 'analyze_token') as mock_crypto_analysis, \
             patch.object(research_agent, 'conduct_market_research') as mock_research, \
             patch.object(development_agent, 'create_custom_widget') as mock_widget_dev:

            # Setup mock responses
            mock_crypto_analysis.return_value = {
                "success": True,
                "analysis": {
                    "basic_info": {"symbol": "ETH", "name": "Ethereum"},
                    "recommendations": {"recommendation": "BUY", "confidence": 0.8}
                }
            }

            mock_research.return_value = {
                "success": True,
                "research_results": {
                    "insights": {"key_insights": ["Ethereum is leading smart contract platform"]}
                }
            }

            mock_widget_dev.return_value = {
                "success": True,
                "widget_results": {
                    "widget_name": "EthereumTracker",
                    "generated_code": {"component": "// ETH tracker component"}
                }
            }

            # Execute multi-agent workflow
            # Step 1: Research Ethereum ecosystem
            research_result = await research_agent.conduct_market_research(
                topic="Ethereum Ecosystem",
                scope="standard",
                thread_id=thread_id
            )

            # Step 2: Analyze Ethereum token
            crypto_result = await crypto_agent.analyze_token(
                token_address="******************************************",  # ETH
                chain="ethereum",
                analysis_depth="comprehensive",
                thread_id=thread_id
            )

            # Step 3: Create custom Ethereum tracking widget
            widget_result = await development_agent.create_custom_widget(
                widget_name="EthereumTracker",
                widget_description="Comprehensive Ethereum tracking widget",
                features=["price_tracking", "network_stats", "defi_metrics"],
                thread_id=thread_id
            )

            # Verify all steps completed successfully
            assert research_result["success"] is True
            assert crypto_result["success"] is True
            assert widget_result["success"] is True

            # Verify workflow coordination
            assert research_result["topic"] == "Ethereum Ecosystem"
            assert crypto_result["chain"] == "ethereum"
            assert widget_result["widget_name"] == "EthereumTracker"

    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, platform_agent):
        """Test workflow error handling and recovery mechanisms."""
        thread_id = "test_error_recovery_001"
        
        # Create agent with mocked failures
        crypto_agent = CryptocurrencyAgent(platform_agent)

        # Test partial failure and recovery
        with patch.object(crypto_agent, '_get_basic_token_info') as mock_basic_info, \
             patch.object(crypto_agent, '_perform_onchain_analysis') as mock_onchain:

            # First call fails, second succeeds
            mock_basic_info.side_effect = [
                Exception("API timeout"),
                {
                    "address": "0x1234...",
                    "name": "Test Token",
                    "symbol": "TEST"
                }
            ]

            mock_onchain.return_value = {
                "holder_count": 5000,
                "transaction_volume_24h": 10000
            }

            # Execute workflow with retry logic
            try:
                result = await crypto_agent.analyze_token(
                    token_address="******************************************",
                    chain="ethereum",
                    analysis_depth="quick",
                    thread_id=thread_id
                )
                
                # Should handle the error gracefully
                assert result is not None
                
            except Exception as e:
                # Verify error is handled appropriately
                assert "API timeout" in str(e) or result["success"] is False

    @pytest.mark.asyncio
    async def test_performance_under_load(self, platform_agent):
        """Test system performance under concurrent load."""
        num_concurrent_requests = 10
        thread_base = "test_performance"

        # Create multiple concurrent workflows
        tasks = []
        for i in range(num_concurrent_requests):
            thread_id = f"{thread_base}_{i}"
            
            # Mock agent execution for performance testing
            with patch.object(platform_agent.agent, 'astream') as mock_astream:
                async def mock_stream(*args, **kwargs):
                    await asyncio.sleep(0.1)  # Simulate processing time
                    yield {"agent": {"messages": [Mock(content=f"Response {i}")]}}
                
                mock_astream.side_effect = mock_stream

                task = asyncio.create_task(
                    self._process_test_message(platform_agent, thread_id, i)
                )
                tasks.append(task)

        # Execute all tasks concurrently
        start_time = asyncio.get_event_loop().time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = asyncio.get_event_loop().time()

        # Verify performance
        execution_time = end_time - start_time
        assert execution_time < 5.0  # Should complete within 5 seconds

        # Verify all requests completed
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == num_concurrent_requests

    async def _process_test_message(self, platform_agent, thread_id, request_id):
        """Helper method for performance testing."""
        message = f"Test message {request_id}"
        
        responses = []
        async for response in platform_agent.process_message(
            message=message,
            thread_id=thread_id,
            stream=True
        ):
            responses.append(response)
        
        return responses

    @pytest.mark.asyncio
    async def test_data_persistence_workflow(self, platform_agent):
        """Test data persistence across workflow steps."""
        thread_id = "test_persistence_001"
        
        # Step 1: Create and save analysis data
        analysis_data = {
            "token": "ETH",
            "analysis_type": "comprehensive",
            "results": {
                "price": 2000,
                "recommendation": "BUY",
                "confidence": 0.85
            },
            "timestamp": datetime.now().isoformat()
        }

        # Save data to file system
        write_result = await platform_agent.file_manager.write_file(
            path="analysis/eth_analysis.json",
            content=analysis_data
        )
        assert write_result["success"]

        # Step 2: Update conversation state
        await platform_agent.conversation_state.update_context(
            thread_id=thread_id,
            user_message="Analyze Ethereum",
            user_context={"analysis_saved": True}
        )

        # Step 3: Create widget with analysis data
        widget_id = await platform_agent.widget_manager.create_widget(
            widget_type="token_chart",
            thread_id=thread_id,
            initial_data={"token": "ETH", "analysis_available": True}
        )

        # Step 4: Verify data persistence
        # Check file was saved
        read_result = await platform_agent.file_manager.read_file(
            path="analysis/eth_analysis.json"
        )
        assert read_result["success"]
        saved_data = json.loads(read_result["content"])
        assert saved_data["token"] == "ETH"

        # Check conversation state
        conversation_state = await platform_agent.get_conversation_state(thread_id)
        assert conversation_state["thread_id"] == thread_id

        # Check widget state
        widget_state = await platform_agent.widget_manager.get_widget_state(widget_id)
        assert widget_state["data"]["token"] == "ETH"

    @pytest.mark.asyncio
    async def test_workflow_cancellation_and_cleanup(self, platform_agent):
        """Test workflow cancellation and proper cleanup."""
        thread_id = "test_cancellation_001"
        
        # Create a long-running workflow
        workflow_definition = {
            "name": "Long Running Analysis",
            "description": "Test workflow for cancellation",
            "tasks": [
                {
                    "name": "Step 1",
                    "description": "First step",
                    "task_type": "analysis",
                    "dependencies": []
                },
                {
                    "name": "Step 2", 
                    "description": "Second step",
                    "task_type": "processing",
                    "dependencies": ["step_1"]
                }
            ]
        }

        # Create workflow
        workflow_id = await platform_agent.create_workflow(
            workflow_definition=workflow_definition,
            thread_id=thread_id
        )

        # Start workflow
        await platform_agent.workflow_planner.start_workflow(workflow_id)

        # Verify workflow is active
        status = await platform_agent.get_workflow_status(workflow_id)
        assert status["status"] in ["created", "active"]

        # Cancel workflow (simulate cancellation)
        if workflow_id in platform_agent.active_workflows:
            workflow = platform_agent.active_workflows[workflow_id]
            workflow.status = platform_agent.workflow_planner.WorkflowStatus.CANCELLED

        # Verify cancellation
        final_status = await platform_agent.get_workflow_status(workflow_id)
        assert final_status["status"] == "cancelled"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
