"""
Platform integration tests for Chad GPT Deep Agents.
Tests the integration between the agent system and the React platform.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from ..src.core.platform_agent_loop import PlatformAgentLoop
from ..src.integrations.widget_manager import WidgetManager
from ..src.core.conversation_state import ConversationState


class TestPlatformIntegration:
    """Test suite for platform integration functionality."""

    @pytest.fixture
    async def platform_agent(self):
        """Create a test platform agent instance."""
        config = {
            "model_name": "test-model",
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        with patch.dict('os.environ', {'OPENROUTER_API_KEY': 'test-key'}):
            agent = PlatformAgentLoop(config)
            yield agent
            await agent.cleanup()

    @pytest.fixture
    async def widget_manager(self):
        """Create a test widget manager instance."""
        manager = WidgetManager(websocket_port=8003)  # Use different port for testing
        yield manager
        await manager.cleanup()

    @pytest.fixture
    async def conversation_state(self):
        """Create a test conversation state manager."""
        state = ConversationState(storage_path="test_workspace/conversations")
        yield state
        # Cleanup test data
        import shutil
        shutil.rmtree("test_workspace", ignore_errors=True)

    @pytest.mark.asyncio
    async def test_agent_initialization(self, platform_agent):
        """Test that the platform agent initializes correctly."""
        assert platform_agent is not None
        assert platform_agent.model_name == "test-model"
        assert platform_agent.temperature == 0.7
        assert platform_agent.max_tokens == 1000
        assert platform_agent.conversation_state is not None
        assert platform_agent.widget_manager is not None
        assert platform_agent.file_manager is not None

    @pytest.mark.asyncio
    async def test_conversation_state_management(self, conversation_state):
        """Test conversation state creation and management."""
        thread_id = "test_thread_001"
        user_message = "Test message"
        user_context = {"user_id": "test_user", "preferences": {"theme": "dark"}}

        # Update conversation context
        await conversation_state.update_context(thread_id, user_message, user_context)

        # Verify context was created
        state = await conversation_state.get_state(thread_id)
        assert state["thread_id"] == thread_id
        assert state["context"] is not None
        assert len(state["message_history"]) == 1
        assert state["message_history"][0]["content"] == user_message

    @pytest.mark.asyncio
    async def test_widget_creation_and_management(self, widget_manager):
        """Test widget creation and state management."""
        thread_id = "test_thread_001"
        widget_type = "token_chart"
        initial_data = {"token_address": "0x1234...", "chain": "ethereum"}

        # Create widget
        widget_id = await widget_manager.create_widget(
            widget_type=widget_type,
            thread_id=thread_id,
            initial_data=initial_data
        )

        assert widget_id is not None
        assert widget_id in widget_manager.active_widgets

        # Get widget state
        widget_state = await widget_manager.get_widget_state(widget_id)
        assert widget_state is not None
        assert widget_state["widget_type"] == widget_type
        assert widget_state["thread_id"] == thread_id
        assert widget_state["data"] == initial_data

        # Update widget data
        update_data = {"price": 100.50, "change_24h": 5.2}
        success = await widget_manager.update_widget(widget_id, update_data)
        assert success

        # Verify update
        updated_state = await widget_manager.get_widget_state(widget_id)
        assert updated_state["data"]["price"] == 100.50
        assert updated_state["data"]["change_24h"] == 5.2

    @pytest.mark.asyncio
    async def test_message_processing_flow(self, platform_agent):
        """Test the complete message processing flow."""
        thread_id = "test_thread_001"
        message = "Analyze Bitcoin and show me a chart"
        user_context = {"user_id": "test_user"}

        # Mock the agent execution to avoid actual LLM calls
        with patch.object(platform_agent.agent, 'astream') as mock_astream:
            mock_response = [
                {
                    "agent": {
                        "messages": [Mock(content="I'll analyze Bitcoin for you.")]
                    }
                },
                {
                    "tools": [
                        {
                            "name": "show_token_chart",
                            "args": {"token_address": "bitcoin", "chain": "bitcoin"}
                        }
                    ]
                },
                {
                    "tool_results": [
                        {
                            "widget_type": "token_chart",
                            "data": {"token": "bitcoin", "price": 45000}
                        }
                    ]
                }
            ]
            
            async def mock_stream(*args, **kwargs):
                for item in mock_response:
                    yield item
            
            mock_astream.side_effect = mock_stream

            # Process message
            responses = []
            async for response in platform_agent.process_message(
                message=message,
                thread_id=thread_id,
                user_context=user_context,
                stream=True
            ):
                responses.append(response)

            # Verify responses
            assert len(responses) > 0
            
            # Check for content response
            content_responses = [r for r in responses if r.get("type") == "content"]
            assert len(content_responses) > 0
            
            # Check for tool calls
            tool_responses = [r for r in responses if r.get("type") == "tool_call"]
            assert len(tool_responses) > 0

    @pytest.mark.asyncio
    async def test_widget_communication_protocol(self, widget_manager):
        """Test the widget communication protocol."""
        thread_id = "test_thread_001"
        widget_id = await widget_manager.create_widget(
            widget_type="pumpfun",
            thread_id=thread_id,
            initial_data={"category": "new"}
        )

        # Test message handling
        test_message = {
            "type": "widget_data_update",
            "widget_id": widget_id,
            "data": {"tokens": [{"symbol": "TEST", "price": 1.0}]}
        }

        # Simulate widget message processing
        await widget_manager._process_widget_message(None, test_message)

        # Verify data was updated
        widget_state = await widget_manager.get_widget_state(widget_id)
        assert "tokens" in widget_state["data"]

    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, platform_agent):
        """Test error handling and recovery mechanisms."""
        thread_id = "test_thread_001"
        invalid_message = None  # This should cause an error

        # Test error handling in message processing
        responses = []
        async for response in platform_agent.process_message(
            message=invalid_message,
            thread_id=thread_id,
            stream=True
        ):
            responses.append(response)

        # Should receive an error response
        error_responses = [r for r in responses if r.get("type") == "error"]
        assert len(error_responses) > 0

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, platform_agent):
        """Test concurrent message processing and widget operations."""
        thread_id = "test_thread_001"
        messages = [
            "Analyze Ethereum",
            "Show me Bitcoin chart",
            "Research DeFi trends"
        ]

        # Mock agent responses
        with patch.object(platform_agent.agent, 'astream') as mock_astream:
            async def mock_stream(*args, **kwargs):
                yield {"agent": {"messages": [Mock(content="Processing...")]}}
            
            mock_astream.side_effect = mock_stream

            # Process messages concurrently
            tasks = []
            for i, message in enumerate(messages):
                task = asyncio.create_task(
                    self._collect_responses(
                        platform_agent.process_message(
                            message=message,
                            thread_id=f"{thread_id}_{i}",
                            stream=True
                        )
                    )
                )
                tasks.append(task)

            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Verify all tasks completed successfully
            for result in results:
                assert not isinstance(result, Exception)
                assert len(result) > 0

    async def _collect_responses(self, response_stream):
        """Helper method to collect all responses from a stream."""
        responses = []
        async for response in response_stream:
            responses.append(response)
        return responses

    @pytest.mark.asyncio
    async def test_workflow_integration(self, platform_agent):
        """Test workflow creation and execution integration."""
        thread_id = "test_thread_001"
        
        # Create a test workflow
        workflow_definition = {
            "name": "Test Analysis Workflow",
            "description": "Test workflow for integration testing",
            "tasks": [
                {
                    "name": "Data Collection",
                    "description": "Collect test data",
                    "task_type": "data_collection",
                    "dependencies": []
                },
                {
                    "name": "Analysis",
                    "description": "Analyze collected data",
                    "task_type": "analysis",
                    "dependencies": ["data_collection"]
                }
            ]
        }

        # Create workflow
        workflow_id = await platform_agent.create_workflow(
            workflow_definition=workflow_definition,
            thread_id=thread_id
        )

        assert workflow_id is not None
        assert workflow_id in platform_agent.active_workflows

        # Get workflow status
        status = await platform_agent.get_workflow_status(workflow_id)
        assert status is not None
        assert status["workflow_id"] == workflow_id
        assert status["name"] == workflow_definition["name"]

    @pytest.mark.asyncio
    async def test_file_system_integration(self, platform_agent):
        """Test file system operations integration."""
        # Test file writing
        test_content = {"test": "data", "timestamp": datetime.now().isoformat()}
        write_result = await platform_agent.file_manager.write_file(
            path="test/integration_test.json",
            content=test_content
        )

        assert write_result["success"]
        assert "test/integration_test.json" in write_result["path"]

        # Test file reading
        read_result = await platform_agent.file_manager.read_file(
            path="test/integration_test.json"
        )

        assert read_result["success"]
        content = json.loads(read_result["content"])
        assert content["test"] == "data"

        # Test file listing
        list_result = await platform_agent.file_manager.list_files(
            path="test"
        )

        assert list_result["success"]
        assert len(list_result["files"]) > 0
        assert any(f["name"] == "integration_test.json" for f in list_result["files"])

    @pytest.mark.asyncio
    async def test_model_switching(self, platform_agent):
        """Test AI model switching functionality."""
        original_model = platform_agent.model_name
        new_model = "anthropic/claude-3-haiku"

        # Switch model
        await platform_agent.switch_model(new_model)

        # Verify model was switched
        assert platform_agent.model_name == new_model
        assert platform_agent.llm.model == new_model

        # Switch back
        await platform_agent.switch_model(original_model)
        assert platform_agent.model_name == original_model

    @pytest.mark.asyncio
    async def test_user_preferences_management(self, platform_agent):
        """Test user preferences storage and retrieval."""
        user_id = "test_user_001"
        preferences = {
            "theme": "dark",
            "default_model": "claude-3.5-sonnet",
            "widget_preferences": {
                "auto_refresh": True,
                "refresh_interval": 30
            }
        }

        # Update preferences
        await platform_agent.update_user_preferences(user_id, preferences)

        # Verify preferences were stored
        stored_prefs = await platform_agent.conversation_state.get_user_preferences(user_id)
        assert stored_prefs["theme"] == "dark"
        assert stored_prefs["default_model"] == "claude-3.5-sonnet"
        assert stored_prefs["widget_preferences"]["auto_refresh"] is True

    @pytest.mark.asyncio
    async def test_cleanup_and_resource_management(self, platform_agent):
        """Test proper cleanup and resource management."""
        # Create some resources
        thread_id = "test_thread_cleanup"
        widget_id = await platform_agent.widget_manager.create_widget(
            widget_type="test_widget",
            thread_id=thread_id,
            initial_data={"test": True}
        )

        # Create some files
        await platform_agent.file_manager.write_file(
            path="cleanup_test.txt",
            content="Test content for cleanup"
        )

        # Verify resources exist
        assert widget_id in platform_agent.widget_manager.active_widgets
        
        file_exists = await platform_agent.file_manager.read_file("cleanup_test.txt")
        assert file_exists["success"]

        # Perform cleanup
        await platform_agent.cleanup()

        # Verify cleanup was performed (widget connections closed, states saved, etc.)
        # Note: Some resources may still exist in memory but connections should be closed


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
