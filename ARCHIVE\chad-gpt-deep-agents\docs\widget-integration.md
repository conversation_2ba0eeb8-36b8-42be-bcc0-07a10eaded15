# Widget Integration and Development Guide

## Overview

This guide provides comprehensive instructions for developing, integrating, and customizing widgets within the Chad GPT Deep Agent platform. Widgets are interactive React components that enhance the conversational experience with rich visualizations and functionality.

## Widget Architecture

### Core Components

#### Widget Base Class
All widgets inherit from a base widget class that provides:
- Standardized lifecycle management
- Communication protocols with the agent system
- State management and persistence
- Error handling and recovery
- Responsive design patterns

#### Communication Layer
Widgets communicate with the agent system through:
- **WebSocket Connections**: Real-time bidirectional communication
- **State Synchronization**: Automatic state updates between agent and widget
- **Event System**: User interactions and widget events
- **Data Binding**: Automatic data updates from agent analysis

#### Integration Points
- **Agent Tools**: Tools that create and manage widgets
- **Conversation Context**: Widgets are tied to conversation threads
- **File System**: Widgets can save and load data
- **Platform APIs**: Access to external data sources

### Widget Types

#### Data Visualization Widgets
- **Token Charts**: Price charts with technical indicators
- **Market Data**: Real-time market information displays
- **Analytics Dashboards**: Comprehensive data analysis views
- **Comparison Charts**: Side-by-side comparisons

#### Interactive Widgets
- **Trading Interfaces**: Swap and trading functionality
- **Wallet Connections**: Blockchain wallet integration
- **Form Inputs**: Data collection and configuration
- **Control Panels**: Widget settings and preferences

#### Information Widgets
- **News Feeds**: Real-time news and updates
- **Social Sentiment**: Social media sentiment analysis
- **Research Reports**: Formatted analysis outputs
- **Documentation**: Help and reference materials

## Creating Custom Widgets

### Step 1: Widget Planning

Define your widget requirements:

```typescript
interface WidgetRequirements {
  name: string;
  description: string;
  features: string[];
  dataSources: string[];
  userInteractions: string[];
  updateFrequency: number;
  responsiveBreakpoints: string[];
}

const myWidgetRequirements: WidgetRequirements = {
  name: "TokenTracker",
  description: "Real-time token price tracking with alerts",
  features: [
    "Multi-token price display",
    "Price alerts and notifications",
    "Historical price charts",
    "Portfolio value calculation"
  ],
  dataSources: ["coingecko", "dexscreener", "jupiter"],
  userInteractions: [
    "Add/remove tokens",
    "Set price alerts",
    "Configure refresh rate",
    "Export data"
  ],
  updateFrequency: 30, // seconds
  responsiveBreakpoints: ["mobile", "tablet", "desktop"]
};
```

### Step 2: Component Structure

Create the React component following platform patterns:

```typescript
// TokenTracker.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { useWidgetCommunication } from '../hooks/useWidgetCommunication';
import { TokenTrackerProps, TokenData } from './types';

export const TokenTracker: React.FC<TokenTrackerProps> = ({
  widgetId,
  initialData,
  onUpdate,
  className = ""
}) => {
  // State management
  const [tokens, setTokens] = useState<TokenData[]>(initialData.tokens || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Widget communication hook
  const { sendMessage, onMessage } = useWidgetCommunication(widgetId);
  
  // Handle incoming messages from agent
  useEffect(() => {
    onMessage('data_update', (data) => {
      setTokens(data.tokens);
      setLoading(false);
    });
    
    onMessage('error', (error) => {
      setError(error.message);
      setLoading(false);
    });
  }, [onMessage]);
  
  // Add token functionality
  const addToken = useCallback(async (tokenAddress: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await sendMessage('add_token', { tokenAddress });
      // Agent will respond with updated data
    } catch (err) {
      setError('Failed to add token');
      setLoading(false);
    }
  }, [sendMessage]);
  
  // Remove token functionality
  const removeToken = useCallback(async (tokenAddress: string) => {
    setLoading(true);
    
    try {
      await sendMessage('remove_token', { tokenAddress });
    } catch (err) {
      setError('Failed to remove token');
      setLoading(false);
    }
  }, [sendMessage]);
  
  // Render component
  return (
    <div className={`bg-[#111] rounded-2xl p-6 shadow-lg shadow-black/10 ${className}`}>
      {/* Widget header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Token Tracker</h3>
        <div className="flex items-center gap-2">
          {loading && (
            <div className="animate-spin w-4 h-4 border-2 border-[#22c55e] border-t-transparent rounded-full" />
          )}
          <button
            onClick={() => sendMessage('refresh', {})}
            className="p-2 hover:bg-[#222] rounded-lg transition-colors"
          >
            <RefreshIcon className="w-4 h-4 text-[#888]" />
          </button>
        </div>
      </div>
      
      {/* Error display */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}
      
      {/* Token list */}
      <div className="space-y-3">
        {tokens.map((token) => (
          <TokenRow
            key={token.address}
            token={token}
            onRemove={() => removeToken(token.address)}
          />
        ))}
      </div>
      
      {/* Add token input */}
      <AddTokenInput onAdd={addToken} disabled={loading} />
    </div>
  );
};
```

### Step 3: Type Definitions

Define TypeScript interfaces for type safety:

```typescript
// types.ts
export interface TokenData {
  address: string;
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  lastUpdated: string;
}

export interface TokenTrackerProps {
  widgetId: string;
  initialData: {
    tokens: TokenData[];
    refreshRate: number;
    alertThresholds: Record<string, number>;
  };
  onUpdate?: (data: any) => void;
  className?: string;
}

export interface WidgetMessage {
  type: string;
  payload: any;
  timestamp: string;
}

export interface WidgetState {
  isActive: boolean;
  isMinimized: boolean;
  lastUpdate: string;
  errorState: string | null;
}
```

### Step 4: Agent Integration

Create agent tools for widget management:

```python
# token_tracker_tools.py
from langchain_core.tools import tool
from typing import Dict, Any, List

@tool
def create_token_tracker_widget(
    thread_id: str,
    initial_tokens: List[str] = None,
    refresh_rate: int = 30
) -> Dict[str, Any]:
    """
    Create a token tracker widget for monitoring cryptocurrency prices.
    
    Args:
        thread_id: Conversation thread identifier
        initial_tokens: List of token addresses to track initially
        refresh_rate: Update frequency in seconds
        
    Returns:
        Widget creation result
    """
    try:
        initial_data = {
            "tokens": [],
            "refresh_rate": refresh_rate,
            "alert_thresholds": {}
        }
        
        # Add initial tokens if provided
        if initial_tokens:
            for token_address in initial_tokens:
                token_data = get_token_data(token_address)
                initial_data["tokens"].append(token_data)
        
        # Create widget through widget manager
        widget_id = widget_manager.create_widget(
            widget_type="token_tracker",
            thread_id=thread_id,
            initial_data=initial_data
        )
        
        return {
            "success": True,
            "widget_id": widget_id,
            "widget_type": "token_tracker",
            "message": f"Created token tracker widget with {len(initial_tokens or [])} tokens"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to create token tracker widget"
        }

@tool
def add_token_to_tracker(
    widget_id: str,
    token_address: str
) -> Dict[str, Any]:
    """
    Add a token to an existing token tracker widget.
    
    Args:
        widget_id: Widget identifier
        token_address: Token contract address to add
        
    Returns:
        Operation result
    """
    try:
        # Get token data
        token_data = get_token_data(token_address)
        
        # Update widget
        widget_manager.update_widget(
            widget_id=widget_id,
            data={"add_token": token_data}
        )
        
        return {
            "success": True,
            "token_added": token_data,
            "message": f"Added {token_data['symbol']} to tracker"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Failed to add token {token_address}"
        }
```

### Step 5: WebSocket Communication

Implement real-time communication:

```typescript
// useWidgetCommunication.ts
import { useEffect, useCallback, useRef } from 'react';

export const useWidgetCommunication = (widgetId: string) => {
  const wsRef = useRef<WebSocket | null>(null);
  const handlersRef = useRef<Map<string, (data: any) => void>>(new Map());
  
  useEffect(() => {
    // Connect to widget WebSocket server
    const ws = new WebSocket(`ws://localhost:8002/widget/${widgetId}`);
    wsRef.current = ws;
    
    ws.onopen = () => {
      // Register widget with server
      ws.send(JSON.stringify({
        type: 'widget_register',
        widget_id: widgetId
      }));
    };
    
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        const handler = handlersRef.current.get(message.type);
        if (handler) {
          handler(message.payload || message);
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    return () => {
      ws.close();
    };
  }, [widgetId]);
  
  const sendMessage = useCallback(async (type: string, payload: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type,
        payload,
        widget_id: widgetId,
        timestamp: new Date().toISOString()
      }));
    } else {
      throw new Error('WebSocket not connected');
    }
  }, [widgetId]);
  
  const onMessage = useCallback((type: string, handler: (data: any) => void) => {
    handlersRef.current.set(type, handler);
  }, []);
  
  return { sendMessage, onMessage };
};
```

## Widget Styling Guidelines

### Design System Compliance

Follow the Chad GPT design system:

```scss
// Widget base styles
.widget-container {
  @apply bg-[#111] rounded-2xl shadow-lg shadow-black/10;
  @apply border border-[#181818];
  @apply transition-all duration-200;
  
  &:hover {
    @apply shadow-xl shadow-black/20;
    @apply scale-[1.02];
  }
}

.widget-header {
  @apply flex items-center justify-between p-6 pb-4;
  @apply border-b border-[#181818];
}

.widget-title {
  @apply text-lg font-semibold text-white tracking-tight;
}

.widget-content {
  @apply p-6 space-y-4;
}

.widget-button {
  @apply bg-[#22c55e] hover:bg-[#16a34a] text-black;
  @apply px-4 py-2 rounded-lg font-medium;
  @apply transition-all duration-200;
  @apply hover:scale-105 active:scale-95;
}

.widget-button-secondary {
  @apply bg-[#222] hover:bg-[#333] text-white;
  @apply px-4 py-2 rounded-lg font-medium;
  @apply transition-all duration-200;
}
```

### Responsive Design

Ensure widgets work across all screen sizes:

```typescript
// Responsive breakpoints
const breakpoints = {
  mobile: '(max-width: 768px)',
  tablet: '(min-width: 769px) and (max-width: 1024px)',
  desktop: '(min-width: 1025px)'
};

// Responsive hook
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  
  useEffect(() => {
    const checkScreenSize = () => {
      if (window.matchMedia(breakpoints.mobile).matches) {
        setScreenSize('mobile');
      } else if (window.matchMedia(breakpoints.tablet).matches) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  
  return screenSize;
};
```

## Testing Widgets

### Unit Testing

```typescript
// TokenTracker.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TokenTracker } from './TokenTracker';

const mockProps = {
  widgetId: 'test-widget-001',
  initialData: {
    tokens: [
      {
        address: '0x1234...',
        symbol: 'TEST',
        name: 'Test Token',
        price: 1.50,
        change24h: 5.2,
        volume24h: 1000000,
        marketCap: 15000000,
        lastUpdated: '2024-01-15T10:30:00Z'
      }
    ],
    refreshRate: 30,
    alertThresholds: {}
  }
};

describe('TokenTracker Widget', () => {
  test('renders token data correctly', () => {
    render(<TokenTracker {...mockProps} />);
    
    expect(screen.getByText('Test Token')).toBeInTheDocument();
    expect(screen.getByText('TEST')).toBeInTheDocument();
    expect(screen.getByText('$1.50')).toBeInTheDocument();
    expect(screen.getByText('+5.2%')).toBeInTheDocument();
  });
  
  test('handles token addition', async () => {
    const mockSendMessage = jest.fn();
    jest.mock('../hooks/useWidgetCommunication', () => ({
      useWidgetCommunication: () => ({
        sendMessage: mockSendMessage,
        onMessage: jest.fn()
      })
    }));
    
    render(<TokenTracker {...mockProps} />);
    
    const addButton = screen.getByText('Add Token');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(mockSendMessage).toHaveBeenCalledWith('add_token', {
        tokenAddress: expect.any(String)
      });
    });
  });
});
```

### Integration Testing

```typescript
// widget-integration.test.ts
import { createTestAgent } from '../test-utils/agent';
import { createTestWidget } from '../test-utils/widget';

describe('Widget Integration', () => {
  test('agent can create and communicate with widget', async () => {
    const agent = createTestAgent();
    const widgetManager = agent.widgetManager;
    
    // Create widget through agent
    const result = await agent.executeCommand(
      'create_token_tracker_widget',
      {
        thread_id: 'test-thread',
        initial_tokens: ['0x1234...']
      }
    );
    
    expect(result.success).toBe(true);
    expect(result.widget_id).toBeDefined();
    
    // Verify widget state
    const widgetState = await widgetManager.getWidgetState(result.widget_id);
    expect(widgetState.widget_type).toBe('token_tracker');
    expect(widgetState.data.tokens).toHaveLength(1);
  });
});
```

## Deployment and Distribution

### Widget Registration

Register your widget with the platform:

```typescript
// widget-registry.ts
import { TokenTracker } from './TokenTracker';

export const widgetRegistry = {
  token_tracker: {
    component: TokenTracker,
    name: 'Token Tracker',
    description: 'Real-time cryptocurrency price tracking',
    category: 'finance',
    tags: ['crypto', 'prices', 'tracking'],
    version: '1.0.0',
    author: 'Your Name',
    capabilities: [
      'real_time_data',
      'user_interaction',
      'data_export',
      'alerts'
    ],
    dataSources: ['coingecko', 'dexscreener'],
    permissions: ['network_access', 'local_storage']
  }
};
```

### Build and Package

```json
// package.json
{
  "name": "token-tracker-widget",
  "version": "1.0.0",
  "description": "Token tracking widget for Chad GPT",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc && vite build",
    "test": "jest",
    "lint": "eslint src/",
    "package": "npm run build && npm pack"
  },
  "peerDependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "typescript": "^5.0.0",
    "vite": "^5.0.0"
  }
}
```

## Best Practices

### Performance Optimization

1. **Lazy Loading**: Load widget components only when needed
2. **Memoization**: Use React.memo for expensive components
3. **Virtual Scrolling**: For large data sets
4. **Debounced Updates**: Prevent excessive re-renders
5. **Efficient WebSocket Usage**: Batch messages when possible

### Error Handling

1. **Graceful Degradation**: Provide fallbacks for failed operations
2. **User Feedback**: Clear error messages and recovery options
3. **Retry Logic**: Automatic retry for transient failures
4. **Logging**: Comprehensive error logging for debugging

### Security Considerations

1. **Input Validation**: Sanitize all user inputs
2. **XSS Prevention**: Escape dynamic content
3. **API Security**: Secure communication with external APIs
4. **Permission Model**: Request only necessary permissions

### Accessibility

1. **Keyboard Navigation**: Full keyboard accessibility
2. **Screen Reader Support**: Proper ARIA labels and roles
3. **Color Contrast**: Meet WCAG guidelines
4. **Focus Management**: Clear focus indicators

By following this guide, you can create powerful, integrated widgets that enhance the Chad GPT Deep Agent platform's capabilities while maintaining consistency with the overall user experience.
