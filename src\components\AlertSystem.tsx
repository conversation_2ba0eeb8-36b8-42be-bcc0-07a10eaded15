import { AlertCircle, CheckCircle2, Info, XCircle, X } from 'lucide-react';
import { useAlert } from '../contexts/AlertContext';
import clsx from 'clsx';

export function AlertSystem() {
  const { alerts, removeAlert } = useAlert();

  return (
    <div className="fixed bottom-8 right-8 z-50 space-y-4 max-w-md w-full">
      {alerts.map(alert => (
        <div
          key={alert.id}
          className={clsx(
            'p-4 rounded-xl shadow-lg animate-fade-in flex items-start gap-3 bg-[#111] border',
            {
              'border-[#22c55e] text-[#22c55e]': alert.type === 'success',
              'border-red-500 text-red-500': alert.type === 'error',
              'border-blue-500 text-blue-500': alert.type === 'info',
              'border-yellow-500 text-yellow-500': alert.type === 'warning',
            }
          )}
        >
          {alert.type === 'success' && <CheckCircle2 className="w-5 h-5 flex-shrink-0" />}
          {alert.type === 'error' && <XCircle className="w-5 h-5 flex-shrink-0" />}
          {alert.type === 'info' && <Info className="w-5 h-5 flex-shrink-0" />}
          {alert.type === 'warning' && <AlertCircle className="w-5 h-5 flex-shrink-0" />}
          
          <p className="text-sm flex-1">{alert.message}</p>
          
          <button
            onClick={() => removeAlert(alert.id)}
            className={clsx(
              'p-1 rounded-lg transition-all duration-200 hover:bg-white/10',
              'text-[#666] hover:text-white'
            )}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );
}