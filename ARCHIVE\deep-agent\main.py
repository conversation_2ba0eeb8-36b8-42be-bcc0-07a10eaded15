"""
Chad GPT Deep Agent - Main API Server
Unified agent system that serves as the cornerstone of the entire platform.
Replaces the previous LangGraph agent with comprehensive Deep Agent capabilities.
"""

import os
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, AsyncGenerator
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()
load_dotenv(Path("../.env"))  # Check parent directory

# Handle VITE_OPENROUTER_API_KEY -> OPENROUTER_API_KEY mapping
vite_api_key = os.getenv("VITE_OPENROUTER_API_KEY")
if vite_api_key and not os.getenv("OPENROUTER_API_KEY"):
    os.environ["OPENROUTER_API_KEY"] = vite_api_key

# Set default model if not specified
if not os.getenv("DEFAULT_MODEL"):
    os.environ["DEFAULT_MODEL"] = "z-ai/glm-4.5"

# Import the unified Deep Agent system
from core.unified_platform_agent import UnifiedPlatformAgent
from core.conversation_manager import ConversationManager

# Initialize FastAPI app
app = FastAPI(
    title="Chad GPT Deep Agent API",
    description="Unified Deep Agent system for Chad GPT platform",
    version="2.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global agent instance
unified_agent: Optional[UnifiedPlatformAgent] = None
conversation_manager: Optional[ConversationManager] = None


class ChatMessage(BaseModel):
    """Chat message model for API requests."""
    role: str
    content: str
    image: Optional[str] = None


class ChatRequest(BaseModel):
    """Chat request model."""
    messages: List[ChatMessage]
    threadId: Optional[str] = None
    stream: bool = True
    model: Optional[str] = None
    user_context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """Chat response model."""
    content: Optional[str] = None
    thinking: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    model_info: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the Deep Agent system on startup."""
    global unified_agent, conversation_manager, widget_registry
    
    print("🚀 Starting Chad GPT Deep Agent System...")
    
    try:
        # Initialize conversation manager
        conversation_manager = ConversationManager()

        # Initialize unified agent
        agent_config = {
            "model_name": os.getenv("DEFAULT_MODEL", "z-ai/glm-4.5"),
            "temperature": float(os.getenv("TEMPERATURE", "0.7")),
            "max_tokens": int(os.getenv("MAX_TOKENS", "4000")),
            "openrouter_api_key": os.getenv("OPENROUTER_API_KEY"),
            "conversation_manager": conversation_manager
        }
        
        unified_agent = UnifiedPlatformAgent(agent_config)
        
        print("✅ Deep Agent system initialized successfully!")
        print(f"   Model: {agent_config['model_name']}")
        print(f"   Temperature: {agent_config['temperature']}")
        print(f"   Max Tokens: {agent_config['max_tokens']}")
        
    except Exception as e:
        print(f"❌ Failed to initialize Deep Agent system: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global unified_agent, conversation_manager
    
    print("🧹 Shutting down Deep Agent system...")
    
    if unified_agent:
        await unified_agent.cleanup()
    
    if conversation_manager:
        await conversation_manager.cleanup()
    
    print("✅ Deep Agent system shutdown complete!")


@app.post("/api/chat")
async def chat_endpoint(request: ChatRequest):
    """
    Main chat endpoint that handles all conversations through the Deep Agent.
    Maintains backward compatibility with the previous LangGraph agent API.
    """
    print(f"🔍 Chat endpoint called with: {len(request.messages)} messages")

    if not unified_agent:
        print("❌ Deep Agent not initialized")
        raise HTTPException(status_code=500, detail="Deep Agent not initialized")

    try:
        # Extract the latest user message
        if not request.messages:
            raise HTTPException(status_code=400, detail="No messages provided")
        
        latest_message = request.messages[-1]
        if latest_message.role != "user":
            raise HTTPException(status_code=400, detail="Last message must be from user")
        
        # Prepare conversation context
        conversation_context = {
            "messages": [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "image": msg.image,
                    "timestamp": datetime.now().isoformat()
                }
                for msg in request.messages
            ],
            "user_context": request.user_context or {},
            "model": request.model,
            "thread_id": request.threadId or f"thread_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        # Process message through Deep Agent
        if request.stream:
            return StreamingResponse(
                stream_chat_response(
                    latest_message.content,
                    conversation_context,
                    latest_message.image
                ),
                media_type="application/json"
            )
        else:
            # Non-streaming response
            response = await process_single_response(
                latest_message.content,
                conversation_context,
                latest_message.image
            )
            return response
            
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def stream_chat_response(
    message: str,
    conversation_context: Dict[str, Any],
    image: Optional[str] = None
) -> AsyncGenerator[str, None]:
    """Stream chat responses from the Deep Agent."""
    try:
        # Check if this is a widget request that should use single response
        widget_type = unified_agent.widget_coordinator.should_handle_request(message)

        if widget_type:
            # For widget requests, use single response to get both content and function_call
            response = await unified_agent.process_message(
                message=message,
                thread_id=conversation_context["thread_id"],
                conversation_context=conversation_context,
                image=image
            )

            # Debug: Log the response structure
            print(f"🔧 Widget response structure: {response}")
            print(f"🔧 Content: {response.get('content')}")
            print(f"🔧 Function call: {response.get('function_call')}")

            # Stream the content first
            if response.get("content"):
                content_chunk = {
                    "content": response.get("content"),
                    "thinking": None,
                    "function_call": None,
                    "metadata": response.get("metadata", {})
                }
                print(f"🔧 Streaming content chunk: {content_chunk}")
                yield json.dumps(content_chunk) + "\n"

            # Then stream the function call if present
            if response.get("function_call"):
                function_chunk = {
                    "content": None,
                    "thinking": None,
                    "function_call": response.get("function_call"),
                    "metadata": {
                        "type": "function_call",
                        "timestamp": response.get("metadata", {}).get("timestamp")
                    }
                }
                print(f"🔧 Streaming function chunk: {function_chunk}")
                yield json.dumps(function_chunk) + "\n"
            return

        # For regular requests, use streaming
        async for response_chunk in unified_agent.process_message_stream(
            message=message,
            thread_id=conversation_context["thread_id"],
            conversation_context=conversation_context,
            image=image
        ):
            # Format response for frontend compatibility
            if response_chunk.get("type") == "content":
                yield json.dumps({
                    "content": response_chunk.get("content", ""),
                    "thinking": response_chunk.get("thinking"),
                    "function_call": response_chunk.get("function_call"),
                    "metadata": response_chunk.get("metadata", {})
                }) + "\n"
            
            elif response_chunk.get("type") == "function_call":
                # Handle widget function calls for frontend
                function_call = {
                    "name": response_chunk.get("function_name"),
                    "arguments": response_chunk.get("function_args", {})
                }
                
                yield json.dumps({
                    "content": None,
                    "function_call": function_call,
                    "metadata": {
                        "type": "function_call",
                        "timestamp": response_chunk.get("timestamp")
                    }
                }) + "\n"
            
            elif response_chunk.get("type") == "thinking":
                yield json.dumps({
                    "content": None,
                    "thinking": response_chunk.get("content"),
                    "metadata": {
                        "type": "thinking",
                        "timestamp": response_chunk.get("timestamp")
                    }
                }) + "\n"
            
            elif response_chunk.get("type") == "error":
                yield json.dumps({
                    "content": f"Error: {response_chunk.get('content', 'Unknown error')}",
                    "metadata": {
                        "type": "error",
                        "error": response_chunk.get("error"),
                        "timestamp": response_chunk.get("timestamp")
                    }
                }) + "\n"
                
    except Exception as e:
        print(f"❌ Streaming error: {e}")
        yield json.dumps({
            "content": f"I apologize, but I encountered an error: {str(e)}",
            "metadata": {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        }) + "\n"


async def process_single_response(
    message: str,
    conversation_context: Dict[str, Any],
    image: Optional[str] = None
) -> ChatResponse:
    """Process a single non-streaming response."""
    print(f"🔄 Processing message: {message[:100]}...")
    print(f"📋 Thread ID: {conversation_context['thread_id']}")

    try:
        # Process through unified agent
        print("🤖 Calling unified_agent.process_message...")
        response = await unified_agent.process_message(
            message=message,
            thread_id=conversation_context["thread_id"],
            conversation_context=conversation_context,
            image=image
        )
        print(f"✅ Got response: {response.get('content', '')[:100]}...")
        
        return ChatResponse(
            content=response.get("content"),
            thinking=response.get("thinking"),
            function_call=response.get("function_call"),
            model_info=response.get("model_info"),
            metadata=response.get("metadata", {})
        )
        
    except Exception as e:
        print(f"❌ Single response error: {e}")
        return ChatResponse(
            content=f"I apologize, but I encountered an error: {str(e)}",
            metadata={
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "agent_initialized": unified_agent is not None,
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0"
    }


@app.get("/api/agent/status")
async def agent_status():
    """Get detailed agent status."""
    if not unified_agent:
        return {"status": "not_initialized"}
    
    return await unified_agent.get_system_status()


@app.post("/api/agent/switch-model")
async def switch_model(request: Dict[str, str]):
    """Switch the active AI model."""
    if not unified_agent:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    model_name = request.get("model")
    if not model_name:
        raise HTTPException(status_code=400, detail="Model name required")
    
    try:
        await unified_agent.switch_model(model_name)
        return {"success": True, "model": model_name}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/conversations/{thread_id}")
async def get_conversation(thread_id: str):
    """Get conversation history for a thread."""
    if not conversation_manager:
        raise HTTPException(status_code=500, detail="Conversation manager not initialized")
    
    try:
        conversation = await conversation_manager.get_conversation(thread_id)
        return conversation
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/widgets/{thread_id}")
async def get_active_widgets(thread_id: str):
    """Get active widgets for a conversation thread."""
    if not unified_agent:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    try:
        widgets = await unified_agent.get_active_widgets(thread_id)
        return {"widgets": widgets}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/workflows")
async def create_workflow(request: Dict[str, Any]):
    """Create a new workflow."""
    if not unified_agent:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    try:
        workflow_id = await unified_agent.create_workflow(
            workflow_definition=request.get("definition", {}),
            thread_id=request.get("thread_id", "")
        )
        return {"workflow_id": workflow_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/workflows/{workflow_id}")
async def get_workflow_status(workflow_id: str):
    """Get workflow status."""
    if not unified_agent:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    try:
        status = await unified_agent.get_workflow_status(workflow_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/widget-context")
async def receive_widget_context(request: dict):
    """
    Receive widget context updates from the frontend.
    This provides the agent with awareness of current widget state.
    """
    try:
        widget_type = request.get("widgetType")
        widget_id = request.get("widgetId")
        thread_id = request.get("threadId")
        data = request.get("data", {})

        print(f"📡 Received widget context update:")
        print(f"   Widget Type: {widget_type}")
        print(f"   Widget ID: {widget_id}")
        print(f"   Thread ID: {thread_id}")
        print(f"   Data: {data}")

        # Store widget context in the unified agent with timestamp
        if hasattr(unified_agent, 'context_manager'):
            # Add timestamp and persistence info
            enhanced_context = {
                'widget_type': widget_type,
                'data': data,
                'created_at': request.get('timestamp', 'Unknown'),
                'last_updated': request.get('timestamp', 'Unknown'),
                'thread_id': thread_id
            }

            unified_agent.context_manager.update_widget_context(
                thread_id=thread_id,
                widget_id=widget_id,
                widget_type=widget_type,
                widget_data=enhanced_context
            )
            print(f"✅ Widget context stored in agent memory with timestamp")

        # 🧠 STORE WIDGET INTERACTION IN INTELLIGENT MEMORY
        from core.memory_manager import memory_manager
        await memory_manager.add_widget_memory(
            widget_type=widget_type,
            widget_data=data,
            thread_id=thread_id or "default"
        )

        return {"status": "success", "message": "Widget context received"}

    except Exception as e:
        print(f"❌ Error processing widget context: {e}")
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

    # Run the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,  # Same port as previous LangGraph agent
        reload=True,
        log_level="info"
    )
