import { createBrowserRouter } from 'react-router-dom';
import App from './App';
import Agents from './pages/Agents';
import Layout from './components/Layout';

function NotFound() {
  return (
    <div className="flex-1 flex flex-col items-center justify-center text-center p-8">
      <h1 className="text-4xl font-bold mb-4">404 - Page Not Found</h1>
      <p className="text-[#666] mb-6">The page you're looking for doesn't exist.</p>
      <a href="/" className="px-4 py-2 bg-[#22c55e] text-black rounded-lg hover:bg-[#16a34a] transition-colors">
        Go Home
      </a>
    </div>
  );
}

export const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    errorElement: <NotFound />,
    children: [
      {
        index: true,
        element: <App />,
      },
      {
        path: "agents",
        element: <Agents />,
      },
      {
        path: "*",
        element: <NotFound />,
      },
    ],
  },
]);