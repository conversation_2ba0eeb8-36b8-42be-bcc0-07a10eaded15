#!/usr/bin/env python3
"""
Simple test server to verify LangGraph agent connectivity.
"""

import requests
import json
import time

def test_health():
    """Test health endpoint."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        print(f"✅ Health Check: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health Check Failed: {e}")
        return False

def test_chat():
    """Test chat endpoint."""
    try:
        data = {
            "messages": [
                {"role": "user", "content": "Hello, test message"}
            ]
        }
        response = requests.post("http://localhost:8001/api/chat", json=data, timeout=30)
        print(f"✅ Chat Test: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        print(f"❌ Chat Test Failed: {e}")
        return False

def test_widget():
    """Test widget trigger."""
    try:
        data = {
            "messages": [
                {"role": "user", "content": "Show me pump.fun data"}
            ]
        }
        response = requests.post("http://localhost:8001/api/chat", json=data, timeout=30)
        print(f"✅ Widget Test: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if result.get('function_call'):
            print(f"🎯 Widget triggered: {result['function_call']['name']}")
        
        return True
    except Exception as e:
        print(f"❌ Widget Test Failed: {e}")
        return False

def main():
    print("🧪 Testing LangGraph Agent Connectivity")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("❌ Agent is not responding. Make sure it's running on port 8001.")
        return
    
    print()
    
    # Test basic chat
    print("Testing basic chat...")
    test_chat()
    
    print()
    
    # Test widget trigger
    print("Testing widget trigger...")
    test_widget()
    
    print()
    print("🎉 All tests completed!")

if __name__ == "__main__":
    main()
