"""
PumpFun Widget Specialist Agent
Handles all PumpFun widget interactions, token data, and user queries related to pump.fun
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI


class PumpFunWidgetAgent:
    """Specialized agent for PumpFun widget management and token analysis."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get("model_name", "z-ai/glm-4.5")
        self.api_key = config.get("openrouter_api_key")
        
        # Configure LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=0.7,
            max_tokens=2000
        )
        
        # Initialize tools
        self.tools = self._get_pumpfun_tools()

        # Create simple LLM without agent complexity
        # We'll handle tool calling manually to avoid validation issues
        
        # Widget state
        self.active_widgets = {}
        self.token_cache = {}
        
    def _get_system_prompt(self) -> str:
        """Get the PumpFun specialist system prompt."""
        return """You are the PumpFun Widget Specialist, an expert in pump.fun tokens and meme coin analysis.

## Your Expertise
- Deep knowledge of pump.fun ecosystem and token mechanics
- Real-time token data analysis and trend identification
- Meme coin market dynamics and community sentiment
- Token launch patterns and success indicators

## Your Responsibilities
1. **Widget Management**: Launch and manage PumpFun widgets when users want to explore tokens
2. **Token Analysis**: Provide insights on specific tokens, trends, and opportunities
3. **Data Context**: Maintain awareness of what tokens are currently displayed in widgets
4. **User Guidance**: Help users navigate pump.fun and understand token metrics

## When to Launch Widgets
ALWAYS call show_pumpfun_widget() when users:
- Ask "what's on pumpfun" or "show pumpfun"
- Want to "browse tokens" or "see new tokens"
- Ask about "meme tokens" or "latest tokens"
- Say "show me" in pumpfun context

## Response Style
- Be enthusiastic but concise about pump.fun opportunities
- Provide brief, engaging responses without over-explaining
- Keep responses short and to the point - the widget speaks for itself
- Use emojis sparingly (🚀💎🔥)
- NEVER include function call syntax like "show_pumpfun_widget()" in your response
- NEVER explain what the widget will show - let users discover it themselves

Remember: Keep responses brief and conversational. The widget provides all the details users need!"""

    def _get_pumpfun_tools(self) -> List:
        """Get PumpFun-specific tools."""
        
        @tool
        def show_pumpfun_widget() -> Dict[str, Any]:
            """
            Display the PumpFun interactive widget for browsing meme tokens.
            
            Use this when users want to:
            - Browse pump.fun tokens
            - See new token launches
            - Explore meme coin opportunities
            - Access pump.fun interface
            """
            return {
                "widget_type": "pumpfun",
                "description": "Displaying PumpFun widget for token browsing"
            }
        
        @tool
        def analyze_pumpfun_trends() -> Dict[str, Any]:
            """Analyze current trends on pump.fun platform."""
            return {
                "trends": [
                    "AI-themed tokens gaining momentum",
                    "Gaming meme coins showing strong community growth",
                    "Celebrity-inspired tokens trending",
                    "Utility-focused meme projects emerging"
                ],
                "hot_categories": ["AI", "Gaming", "DeFi", "Community"],
                "market_sentiment": "Bullish on innovative meme concepts"
            }
        
        @tool
        def get_token_insights(token_name: str) -> Dict[str, Any]:
            """Get insights about a specific token on pump.fun."""
            return {
                "token": token_name,
                "analysis": f"Analysis for {token_name} - checking community strength, holder distribution, and trading patterns",
                "recommendation": "Monitor for volume spikes and community engagement",
                "risk_level": "High (typical for new meme tokens)"
            }
        
        return [show_pumpfun_widget, analyze_pumpfun_trends, get_token_insights]
    
    async def process_request(
        self,
        message: str,
        thread_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a PumpFun-related request."""
        try:
            # Determine if we should show widget based on message content
            should_show_widget = self._should_show_widget(message)
            print(f"🎮 PumpFun Agent - should_show_widget: {should_show_widget} for message: '{message}'")

            # Generate response using direct LLM call
            system_prompt = self._get_system_prompt()

            response = await self.llm.ainvoke([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ])

            # Prepare function call if widget should be shown
            function_call = None
            if should_show_widget:
                function_call = {
                    "name": "showPumpFunWidget",
                    "arguments": {}
                }
                print(f"🎮 PumpFun Agent - Creating function_call: {function_call}")

            return {
                "content": response.content,
                "function_call": function_call,
                "specialist": "pumpfun",
                "metadata": {
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent_type": "pumpfun_specialist"
                }
            }

        except Exception as e:
            return {
                "content": f"I encountered an error analyzing PumpFun data: {str(e)}",
                "error": str(e),
                "specialist": "pumpfun"
            }

    def _should_show_widget(self, message: str) -> bool:
        """Determine if widget should be shown based on message content."""
        message_lower = message.lower()
        widget_triggers = [
            "show", "display", "open", "launch", "widget",
            "what's on", "browse", "explore", "see", "check", "pumpfun", "pump.fun"
        ]
        return any(trigger in message_lower for trigger in widget_triggers)
    
    def _extract_widget_call(self, messages) -> Optional[Dict[str, Any]]:
        """Extract widget calls from agent response."""
        for message in reversed(messages):
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    if tool_call.get('name') == 'show_pumpfun_widget':
                        return {
                            "name": "showPumpFunWidget",
                            "arguments": {}
                        }
        return None
