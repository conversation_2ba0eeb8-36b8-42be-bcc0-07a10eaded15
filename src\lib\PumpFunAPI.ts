/**
 * PumpFun API Client using Supabase MCP for AP3X-pump-tokens project
 * Tables: new_tokens, bonding_tokens, graduated_tokens
 */

export interface PumpFunTokenResponse {
  // Core token data from the JSONB data field
  tokenAddress?: string;
  mint?: string;
  name?: string;
  symbol?: string;
  description?: string;
  image_uri?: string;
  metadata_uri?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  show_name?: boolean;
  created_timestamp?: number;
  nsfw?: boolean;
  market_cap?: number;
  reply_count?: number;
  last_reply?: number;
  complete?: boolean;
  total_supply?: number;
  creator?: string;
  bonding_curve?: string;
  associated_bonding_curve?: string;
  usd_market_cap?: number;
  virtual_sol_reserves?: number;
  virtual_token_reserves?: number;
  real_sol_reserves?: number;
  real_token_reserves?: number;
  last_trade_timestamp?: number;
  king_of_the_hill_timestamp?: number | null;
  market_id?: string | null;
  inverted?: boolean | null;
  is_currently_live?: boolean;
  raydium_pool?: string | null;

  // Enriched data fields
  fdv?: number;
  logo?: string;
  dexId?: string;
  chainId?: string;
  decimals?: string;
  priceUsd?: string;
  liquidity?: string;
  marketCap?: number;
  volume_h1?: number;
  volume_h6?: number;
  volume_h24?: number;
  pairAddress?: string;
  priceNative?: string;
  txns_h1_buys?: number;
  txns_h1_sells?: number;
  txns_h24_buys?: number;
  txns_h24_sells?: number;
  activity_level?: string;
  is_trending_up?: boolean;
  priceChange_h1?: number;
  priceChange_h6?: number;
  priceChange_h24?: number;
  total_buys_24h?: number;
  total_sells_24h?: number;
  total_txns_24h?: number;
  buy_sell_ratio_24h?: string;
  bondingCurveProgress?: number;
  graduatedAt?: string;

  // Prefixed pumpfun fields
  pumpfun_nsfw?: boolean;
  pumpfun_creator?: string;
  pumpfun_twitter?: string;
  pumpfun_website?: string;
  pumpfun_telegram?: string;
  pumpfun_complete?: boolean;
  pumpfun_description?: string;
  pumpfun_metadata_uri?: string;
  pumpfun_raydium_pool?: string | null;
  pumpfun_bonding_curve?: string;
  pumpfun_created_timestamp?: number;
  pumpfun_is_currently_live?: boolean;
  pumpfun_virtual_sol_reserves?: number;
  pumpfun_virtual_token_reserves?: number;
  pumpfun_associated_bonding_curve?: string;

  // Additional fields
  [key: string]: any;
}

export type PumpFunCategory = 'new' | 'bonding' | 'graduated';

export interface PumpFunAllCategoriesResponse {
  'new': PumpFunTokenResponse[];
  'bonding': PumpFunTokenResponse[];
  'graduated': PumpFunTokenResponse[];
}

export class PumpFunAPI {
  private projectUrl = 'https://tmbhwnnmqbvavtfleidy.supabase.co';
  private apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtYmh3bm5tcWJ2YXZ0ZmxlaWR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ3MjEyMDAsImV4cCI6MjA3MDI5NzIwMH0.jz48XryhPkg0hZQfvDdwe-ls3WED6eyqiDpZTvykFMM';

  constructor() {}

  /**
   * Get recent tokens from a specific category
   */
  async getRecentTokensByCategory(category: PumpFunCategory, limit: number = 50): Promise<PumpFunTokenResponse[]> {
    try {
      // Map category to table name
      const tableMap: Record<PumpFunCategory, string> = {
        'new': 'new_tokens',
        'bonding': 'bonding_tokens',
        'graduated': 'graduated_tokens'
      };

      const tableName = tableMap[category];
      if (!tableName) {
        throw new Error(`Unknown category: ${category}`);
      }

      console.log(`🔗 Fetching ${category} tokens from Supabase table: ${tableName}`);

      // Make direct Supabase REST API call
      const response = await fetch(`${this.projectUrl}/rest/v1/${tableName}?select=token_address,data,inserted_at&order=inserted_at.desc&limit=${limit}`, {
        headers: {
          'apikey': this.apiKey,
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        }
      });

      if (!response.ok) {
        throw new Error(`Supabase API error ${response.status}: ${response.statusText}`);
      }

      const supabaseRows = await response.json();

      if (!Array.isArray(supabaseRows)) {
        throw new Error(`Invalid response format for ${category} tokens`);
      }

      console.log(`✅ Successfully fetched ${supabaseRows.length} ${category} tokens from Supabase`);

      // Log sample data for debugging
      if (supabaseRows.length > 0) {
        const sample = supabaseRows[0];
        console.log(`   Sample ${category} token:`, {
          address: sample.token_address,
          name: sample.data?.name,
          symbol: sample.data?.symbol,
          marketCap: sample.data?.marketCap || sample.data?.usd_market_cap,
          volume24h: sample.data?.volume_h24,
          bondingProgress: this.calculateBondingProgress(sample.data),
          isGraduated: sample.data?.pumpfun_complete || sample.data?.graduatedAt
        });
      }

      // Transform the Supabase response to our expected format
      return supabaseRows.map(row => this.transformSupabaseRow(row, category));
    } catch (error) {
      console.error(`Error fetching ${category} tokens:`, error);
      throw error;
    }
  }

  /**
   * Get recent tokens from all categories at once
   */
  async getRecentTokensAllCategories(limit: number = 50): Promise<PumpFunAllCategoriesResponse> {
    try {
      console.log('🚀 Fetching all PumpFun categories from Supabase...');

      const [newTokens, bondingTokens, graduatedTokens] = await Promise.all([
        this.getRecentTokensByCategory('new', limit),
        this.getRecentTokensByCategory('bonding', limit),
        this.getRecentTokensByCategory('graduated', limit)
      ]);

      console.log(`✅ Successfully fetched all categories: New(${newTokens.length}), Bonding(${bondingTokens.length}), Graduated(${graduatedTokens.length})`);

      return {
        'new': newTokens,
        'bonding': bondingTokens,
        'graduated': graduatedTokens
      };
    } catch (error) {
      console.error('Error fetching all categories:', error);
      throw error;
    }
  }

  /**
   * Calculate bonding curve progress
   */
  private calculateBondingProgress(data: any): number {
    // If token is graduated/complete, always return 100%
    if (data.pumpfun_complete || data._pumpfun_direct_original?.complete || data.graduatedAt) {
      return 100;
    }

    // If we have explicit bonding curve progress, use it
    if (data.bondingCurveProgress && data.bondingCurveProgress > 0) {
      return Math.min(data.bondingCurveProgress, 100);
    }

    // Try to calculate from virtual token reserves (PumpFun bonding curve logic)
    const virtualTokenReserves = data.pumpfun_virtual_token_reserves || data._pumpfun_direct_original?.virtual_token_reserves;
    if (virtualTokenReserves && !isNaN(virtualTokenReserves) && virtualTokenReserves > 0) {
      const INITIAL_VIRTUAL_TOKEN_RESERVES = 1_073_000_000 * 1_000_000; // 1.073B tokens with 6 decimals
      const TOKENS_TO_COLLECT = 793_100_000 * 1_000_000; // 793.1M tokens can be sold from curve

      const tokensCollected = INITIAL_VIRTUAL_TOKEN_RESERVES - virtualTokenReserves;
      const progress = (tokensCollected * 100) / TOKENS_TO_COLLECT;

      return Math.max(0, Math.min(progress, 100));
    }

    // Default to 0 if we can't calculate
    return 0;
  }

  /**
   * Transform Supabase row to PumpFunTokenResponse format
   */
  private transformSupabaseRow(row: any, category?: PumpFunCategory): PumpFunTokenResponse {
    const data = row.data || {};

    // Extract the main token address (prefer tokenAddress, fallback to mint)
    const tokenAddress = data.tokenAddress || data.mint || data._pumpfun_direct_original?.mint || row.token_address;

    return {
      // Core fields
      tokenAddress,
      mint: tokenAddress,
      name: data.name || data._pumpfun_direct_original?.name || '',
      symbol: data.symbol || data._pumpfun_direct_original?.symbol || '',
      description: data.pumpfun_description || data._pumpfun_direct_original?.description || '',
      image_uri: data._pumpfun_direct_original?.image_uri || data.logo || '',
      metadata_uri: data.pumpfun_metadata_uri || data._pumpfun_direct_original?.metadata_uri || '',
      twitter: data.pumpfun_twitter || data._pumpfun_direct_original?.twitter || '',
      telegram: data.pumpfun_telegram || data._pumpfun_direct_original?.telegram || '',
      website: data.pumpfun_website || data._pumpfun_direct_original?.website || '',

      // Market data
      market_cap: data.marketCap || data._pumpfun_direct_original?.market_cap || 0,
      usd_market_cap: data._pumpfun_direct_original?.usd_market_cap || data.marketCap || 0,
      priceUsd: data.priceUsd || '0',
      priceNative: data.priceNative || '0',

      // Trading data - prioritize volume_h24 since all tokens have it
      volume_h24: data.volume_h24 || 0,
      volume_24h: data.volume_h24 || 0, // Alias for compatibility
      total_buys_24h: data.total_buys_24h || 0,
      total_sells_24h: data.total_sells_24h || 0,
      total_txns_24h: data.total_txns_24h || 0,
      priceChange_h24: data.priceChange_h24 || 0,

      // PumpFun specific
      creator: data.pumpfun_creator || data._pumpfun_direct_original?.creator || '',
      bonding_curve: data.pumpfun_bonding_curve || data._pumpfun_direct_original?.bonding_curve || '',
      associated_bonding_curve: data.pumpfun_associated_bonding_curve || data._pumpfun_direct_original?.associated_bonding_curve || '',
      complete: category === 'graduated' || data.pumpfun_complete || data._pumpfun_direct_original?.complete || data.graduatedAt || false,
      nsfw: data.pumpfun_nsfw || data._pumpfun_direct_original?.nsfw || false,
      created_timestamp: data.pumpfun_created_timestamp || data._pumpfun_direct_original?.created_timestamp || 0,
      is_currently_live: data.pumpfun_is_currently_live || data._pumpfun_direct_original?.is_currently_live || false,
      virtual_sol_reserves: data.pumpfun_virtual_sol_reserves || data._pumpfun_direct_original?.virtual_sol_reserves || 0,
      virtual_token_reserves: data.pumpfun_virtual_token_reserves || data._pumpfun_direct_original?.virtual_token_reserves || 0,
      raydium_pool: data.pumpfun_raydium_pool || data._pumpfun_direct_original?.raydium_pool || null,

      // Bonding curve progress - 100% for graduated tokens, otherwise calculate or use existing
      bondingCurveProgress: this.calculateBondingProgress(data),

      // Graduation info (for graduated tokens)
      graduatedAt: data.graduatedAt || '',

      // Pass through all original data
      ...data
    };
  }



  // Test connection to Supabase
  async testConnection(): Promise<boolean> {
    try {
      console.log('🧪 Testing Supabase connection...');

      const response = await fetch(`${this.projectUrl}/rest/v1/new_tokens?select=count&limit=1`, {
        headers: {
          'apikey': this.apiKey,
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const isConnected = response.ok;
      console.log(`📡 Supabase connection test: ${isConnected ? '✅ Success' : '❌ Failed'}`);

      return isConnected;
    } catch (e) {
      console.error('❌ Supabase connection test failed:', e);
      return false;
    }
  }

  /**
   * Get new tokens (latest launches)
   */
  async getNewTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('new', limit);
  }

  /**
   * Get bonding tokens (tokens still in bonding curve)
   */
  async getBondingTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('bonding', limit);
  }

  /**
   * Get graduated tokens
   */
  async getGraduatedTokens(limit: number = 50): Promise<PumpFunTokenResponse[]> {
    return this.getRecentTokensByCategory('graduated', limit);
  }

  // Debug via Supabase
  async debugCategory(category: PumpFunCategory): Promise<any> {
    try {
      console.log(`🔍 Debug testing ${category} category...`);
      const tokens = await this.getRecentTokensByCategory(category, 5);

      const result = {
        success: true,
        sample: tokens.slice(0, 2),
        count: tokens.length,
        category
      };

      console.log(`📊 Debug result for ${category}:`, result);
      return result;
    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        sample: [],
        category
      };

      console.error(`❌ Debug failed for ${category}:`, result);
      return result;
    }
  }
}

// Export a default instance for convenience
export const pumpFunAPI = new PumpFunAPI();

// Export factory function (kept for compatibility)
export const createPumpFunAPI = () => new PumpFunAPI();
