"""
Enhanced Chad GPT Deep Agent with platform integration and conversation state management.
Implements the four pillars of Deep Agent architecture with React platform integration.
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from dotenv import load_dotenv

# Import enhanced tool modules
from .conversation_state import ConversationState
from .workflow_planning import WorkflowPlanner
from .platform_files import PlatformFileManager
from ..integrations.widget_manager import WidgetManager
from ..integrations.openrouter_client import OpenRouterClient
from ..prompts.system_chad_gpt import SYSTEM_PROMPT

# Load environment variables
load_dotenv()


class PlatformAgentLoop:
    """
    Enhanced Chad GPT Deep Agent with platform integration.
    Manages conversation state, widget communication, and multi-step workflows.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the platform agent with configuration."""
        self.config = config or {}
        
        # Initialize core components
        self.conversation_state = ConversationState()
        self.workflow_planner = WorkflowPlanner()
        self.file_manager = PlatformFileManager()
        self.widget_manager = WidgetManager()
        self.openrouter_client = OpenRouterClient()
        
        # Agent configuration
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.model_name = self.config.get("model_name", "anthropic/claude-3.5-sonnet")
        self.temperature = self.config.get("temperature", 0.7)
        self.max_tokens = self.config.get("max_tokens", 4000)
        
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is required")
        
        # Initialize LLM with OpenRouter
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Create checkpointer for conversation memory
        self.checkpointer = InMemorySaver()
        
        # Initialize tools
        self.tools = self._initialize_tools()
        
        # Create the enhanced REACT agent
        self.agent = create_react_agent(
            self.llm,
            tools=self.tools,
            checkpointer=self.checkpointer
        )
        
        # Active workflows and widget states
        self.active_workflows = {}
        self.widget_states = {}
        
    def _initialize_tools(self) -> List:
        """Initialize all tools for the deep agent."""
        from ..tools.planning_tools import get_planning_tools
        from ..tools.widget_tools import get_widget_tools
        from ..tools.file_tools import get_file_tools
        from ..tools.delegation_tools import get_delegation_tools
        from ..tools.platform_tools import get_platform_tools
        
        tools = []
        tools.extend(get_planning_tools(self.workflow_planner))
        tools.extend(get_widget_tools(self.widget_manager))
        tools.extend(get_file_tools(self.file_manager))
        tools.extend(get_delegation_tools())
        tools.extend(get_platform_tools(self))
        
        return tools
    
    async def process_message(
        self,
        message: str,
        thread_id: str,
        user_context: Optional[Dict[str, Any]] = None,
        stream: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a user message with streaming response and platform integration.
        
        Args:
            message: User input message
            thread_id: Conversation thread identifier
            user_context: Additional user context and preferences
            stream: Whether to stream the response
            
        Yields:
            Response chunks with content, thinking, and function calls
        """
        try:
            # Update conversation state
            await self.conversation_state.update_context(
                thread_id=thread_id,
                user_message=message,
                user_context=user_context or {}
            )
            
            # Prepare messages for agent
            messages = await self._prepare_messages(message, thread_id)
            
            # Configure agent execution
            config = {
                "configurable": {
                    "thread_id": thread_id,
                    "user_context": user_context or {}
                }
            }
            
            # Execute agent with streaming
            if stream:
                async for chunk in self._stream_agent_response(messages, config):
                    yield chunk
            else:
                result = await self._execute_agent(messages, config)
                yield result
                
        except Exception as e:
            yield {
                "type": "error",
                "content": f"Error processing message: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _prepare_messages(self, message: str, thread_id: str) -> List[BaseMessage]:
        """Prepare messages for agent execution with context."""
        messages = []
        
        # Add system prompt
        messages.append(HumanMessage(content=SYSTEM_PROMPT))
        
        # Add conversation history
        history = await self.conversation_state.get_conversation_history(thread_id)
        for msg in history[-10:]:  # Last 10 messages for context
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            elif msg["role"] == "assistant":
                messages.append(AIMessage(content=msg["content"]))
        
        # Add current message
        messages.append(HumanMessage(content=message))
        
        return messages
    
    async def _stream_agent_response(
        self,
        messages: List[BaseMessage],
        config: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream agent response with real-time updates."""
        try:
            # Execute agent with streaming
            async for chunk in self.agent.astream({"messages": messages}, config):
                
                # Process different chunk types
                if "agent" in chunk:
                    agent_chunk = chunk["agent"]
                    
                    # Handle thinking output
                    if "thinking" in agent_chunk:
                        yield {
                            "type": "thinking",
                            "content": agent_chunk["thinking"],
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    # Handle regular content
                    if "messages" in agent_chunk:
                        for message in agent_chunk["messages"]:
                            if hasattr(message, "content") and message.content:
                                yield {
                                    "type": "content",
                                    "content": message.content,
                                    "timestamp": datetime.now().isoformat()
                                }
                
                # Handle tool calls
                if "tools" in chunk:
                    for tool_call in chunk["tools"]:
                        yield {
                            "type": "tool_call",
                            "tool_name": tool_call.get("name"),
                            "tool_args": tool_call.get("args", {}),
                            "timestamp": datetime.now().isoformat()
                        }
                
                # Handle tool results
                if "tool_results" in chunk:
                    for result in chunk["tool_results"]:
                        # Check for widget updates
                        if result.get("widget_type"):
                            await self._handle_widget_update(result)
                        
                        yield {
                            "type": "tool_result",
                            "result": result,
                            "timestamp": datetime.now().isoformat()
                        }
                        
        except Exception as e:
            yield {
                "type": "error",
                "content": f"Streaming error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _execute_agent(
        self,
        messages: List[BaseMessage],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute agent without streaming."""
        try:
            result = await self.agent.ainvoke({"messages": messages}, config)
            
            # Process final result
            final_message = result["messages"][-1] if result["messages"] else None
            
            return {
                "type": "final_response",
                "content": final_message.content if final_message else "",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "type": "error",
                "content": f"Execution error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _handle_widget_update(self, tool_result: Dict[str, Any]):
        """Handle widget updates from tool results."""
        widget_type = tool_result.get("widget_type")
        widget_data = tool_result.get("data", {})
        
        if widget_type:
            # Update widget state
            widget_id = await self.widget_manager.update_widget(
                widget_type=widget_type,
                data=widget_data
            )
            
            # Store widget state
            self.widget_states[widget_id] = {
                "type": widget_type,
                "data": widget_data,
                "updated_at": datetime.now().isoformat()
            }
    
    async def get_conversation_state(self, thread_id: str) -> Dict[str, Any]:
        """Get current conversation state for a thread."""
        return await self.conversation_state.get_state(thread_id)
    
    async def update_user_preferences(
        self,
        user_id: str,
        preferences: Dict[str, Any]
    ):
        """Update user preferences."""
        await self.conversation_state.update_user_preferences(user_id, preferences)
    
    async def get_active_widgets(self, thread_id: str) -> List[Dict[str, Any]]:
        """Get active widgets for a conversation thread."""
        return await self.widget_manager.get_active_widgets(thread_id)
    
    async def create_workflow(
        self,
        workflow_definition: Dict[str, Any],
        thread_id: str
    ) -> str:
        """Create a new workflow for complex tasks."""
        workflow_id = await self.workflow_planner.create_workflow(
            definition=workflow_definition,
            thread_id=thread_id
        )
        
        self.active_workflows[workflow_id] = {
            "thread_id": thread_id,
            "definition": workflow_definition,
            "created_at": datetime.now().isoformat(),
            "status": "active"
        }
        
        return workflow_id
    
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get status of an active workflow."""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            status = await self.workflow_planner.get_workflow_status(workflow_id)
            
            return {
                "workflow_id": workflow_id,
                "status": status,
                "metadata": workflow
            }
        
        return {"error": "Workflow not found"}
    
    async def switch_model(self, model_name: str):
        """Switch the active AI model."""
        self.model_name = model_name
        
        # Reinitialize LLM with new model
        self.llm = ChatOpenAI(
            model=model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Recreate agent with new LLM
        self.agent = create_react_agent(
            self.llm,
            tools=self.tools,
            checkpointer=self.checkpointer
        )
    
    async def cleanup(self):
        """Cleanup resources and save state."""
        # Save conversation states
        await self.conversation_state.save_all_states()
        
        # Close widget connections
        await self.widget_manager.cleanup()
        
        # Save file manager state
        await self.file_manager.cleanup()


# Factory function for creating platform agents
def create_platform_agent(config: Optional[Dict[str, Any]] = None) -> PlatformAgentLoop:
    """Create a new platform agent instance."""
    return PlatformAgentLoop(config)


# Main execution for standalone usage
if __name__ == "__main__":
    async def main():
        agent = create_platform_agent()
        
        # Example usage
        async for response in agent.process_message(
            message="Analyze the latest trends in DeFi and show me a PumpFun widget",
            thread_id="test_thread_001"
        ):
            print(f"[{response['type']}] {response.get('content', response)}")
    
    asyncio.run(main())
