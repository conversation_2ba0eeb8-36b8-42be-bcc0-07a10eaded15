"""
Workflow Orchestrator - Manages complex multi-step workflows for the Deep Agent system.
Provides intelligent task orchestration, dependency management, and progress tracking.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import uuid


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


class WorkflowStatus(Enum):
    """Workflow execution status."""
    CREATED = "created"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowTask:
    """Represents a single task in a workflow."""
    id: str
    name: str
    description: str
    task_type: str
    dependencies: List[str]
    estimated_duration: Optional[int]  # minutes
    agent_type: Optional[str]
    parameters: Dict[str, Any]
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    result: Optional[Dict[str, Any]]
    error: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class Workflow:
    """Represents a complete workflow with multiple tasks."""
    id: str
    name: str
    description: str
    thread_id: str
    tasks: List[WorkflowTask]
    status: WorkflowStatus
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    progress: float  # 0.0 to 1.0
    metadata: Dict[str, Any]


class WorkflowOrchestrator:
    """
    Orchestrates complex workflows for the Deep Agent system.
    Manages task dependencies, execution order, and progress tracking.
    """

    def __init__(self, storage_path: str = "agent_workspace/workflows"):
        """Initialize workflow orchestrator."""
        self.storage_path = storage_path
        self.active_workflows: Dict[str, Workflow] = {}
        self.task_executors: Dict[str, callable] = {}
        
        # Workflow templates for common patterns
        self.workflow_templates = {
            "token_analysis": {
                "name": "Comprehensive Token Analysis",
                "description": "Complete analysis of a cryptocurrency token",
                "tasks": [
                    {
                        "name": "Basic Token Information",
                        "description": "Gather basic token metadata",
                        "task_type": "data_collection",
                        "agent_type": "crypto_analyst",
                        "estimated_duration": 5,
                        "dependencies": []
                    },
                    {
                        "name": "Market Data Analysis",
                        "description": "Analyze current market data",
                        "task_type": "market_analysis",
                        "agent_type": "crypto_analyst",
                        "estimated_duration": 10,
                        "dependencies": ["basic_info"]
                    },
                    {
                        "name": "Technical Analysis",
                        "description": "Perform technical analysis",
                        "task_type": "technical_analysis",
                        "agent_type": "crypto_analyst",
                        "estimated_duration": 15,
                        "dependencies": ["market_data"]
                    },
                    {
                        "name": "Risk Assessment",
                        "description": "Assess investment risks",
                        "task_type": "risk_analysis",
                        "agent_type": "crypto_analyst",
                        "estimated_duration": 10,
                        "dependencies": ["technical_analysis"]
                    },
                    {
                        "name": "Generate Recommendations",
                        "description": "Create investment recommendations",
                        "task_type": "synthesis",
                        "agent_type": "crypto_analyst",
                        "estimated_duration": 8,
                        "dependencies": ["risk_assessment"]
                    }
                ]
            },
            "market_research": {
                "name": "Comprehensive Market Research",
                "description": "Complete market research and analysis",
                "tasks": [
                    {
                        "name": "Research Planning",
                        "description": "Define research scope and methodology",
                        "task_type": "planning",
                        "agent_type": "research_specialist",
                        "estimated_duration": 10,
                        "dependencies": []
                    },
                    {
                        "name": "Data Collection",
                        "description": "Gather data from multiple sources",
                        "task_type": "data_collection",
                        "agent_type": "research_specialist",
                        "estimated_duration": 25,
                        "dependencies": ["research_planning"]
                    },
                    {
                        "name": "Competitive Analysis",
                        "description": "Analyze competitive landscape",
                        "task_type": "competitive_analysis",
                        "agent_type": "research_specialist",
                        "estimated_duration": 20,
                        "dependencies": ["data_collection"]
                    },
                    {
                        "name": "Trend Analysis",
                        "description": "Identify market trends",
                        "task_type": "trend_analysis",
                        "agent_type": "research_specialist",
                        "estimated_duration": 15,
                        "dependencies": ["data_collection"]
                    },
                    {
                        "name": "Synthesis and Insights",
                        "description": "Generate insights and recommendations",
                        "task_type": "synthesis",
                        "agent_type": "research_specialist",
                        "estimated_duration": 15,
                        "dependencies": ["competitive_analysis", "trend_analysis"]
                    }
                ]
            },
            "widget_development": {
                "name": "Custom Widget Development",
                "description": "Complete widget development workflow",
                "tasks": [
                    {
                        "name": "Requirements Analysis",
                        "description": "Analyze widget requirements",
                        "task_type": "analysis",
                        "agent_type": "development_assistant",
                        "estimated_duration": 15,
                        "dependencies": []
                    },
                    {
                        "name": "Architecture Design",
                        "description": "Design widget architecture",
                        "task_type": "design",
                        "agent_type": "development_assistant",
                        "estimated_duration": 20,
                        "dependencies": ["requirements_analysis"]
                    },
                    {
                        "name": "Component Development",
                        "description": "Generate React components",
                        "task_type": "code_generation",
                        "agent_type": "development_assistant",
                        "estimated_duration": 30,
                        "dependencies": ["architecture_design"]
                    },
                    {
                        "name": "Integration Code",
                        "description": "Create platform integration",
                        "task_type": "integration",
                        "agent_type": "development_assistant",
                        "estimated_duration": 20,
                        "dependencies": ["component_development"]
                    },
                    {
                        "name": "Testing and Documentation",
                        "description": "Create tests and documentation",
                        "task_type": "testing",
                        "agent_type": "development_assistant",
                        "estimated_duration": 25,
                        "dependencies": ["integration_code"]
                    }
                ]
            }
        }
        
        # Ensure storage directory exists
        import os
        os.makedirs(storage_path, exist_ok=True)
    
    async def create_workflow(
        self,
        definition: Dict[str, Any],
        thread_id: str,
        template_name: Optional[str] = None
    ) -> str:
        """Create a new workflow from definition or template."""
        workflow_id = str(uuid.uuid4())
        now = datetime.now()
        
        # Use template if specified
        if template_name and template_name in self.workflow_templates:
            template = self.workflow_templates[template_name]
            definition = {**template, **definition}
        
        # Create tasks
        tasks = []
        for i, task_def in enumerate(definition.get("tasks", [])):
            task = WorkflowTask(
                id=f"{workflow_id}_{i+1}",
                name=task_def["name"],
                description=task_def["description"],
                task_type=task_def.get("task_type", "general"),
                dependencies=task_def.get("dependencies", []),
                estimated_duration=task_def.get("estimated_duration"),
                agent_type=task_def.get("agent_type"),
                parameters=task_def.get("parameters", {}),
                status=TaskStatus.PENDING,
                created_at=now,
                started_at=None,
                completed_at=None,
                result=None,
                error=None,
                metadata=task_def.get("metadata", {})
            )
            tasks.append(task)
        
        # Create workflow
        workflow = Workflow(
            id=workflow_id,
            name=definition["name"],
            description=definition["description"],
            thread_id=thread_id,
            tasks=tasks,
            status=WorkflowStatus.CREATED,
            created_at=now,
            started_at=None,
            completed_at=None,
            progress=0.0,
            metadata=definition.get("metadata", {})
        )
        
        self.active_workflows[workflow_id] = workflow
        
        # Save workflow
        await self._save_workflow(workflow)
        
        # Auto-start workflow
        await self.start_workflow(workflow_id)
        
        return workflow_id
    
    async def start_workflow(self, workflow_id: str) -> bool:
        """Start execution of a workflow."""
        if workflow_id not in self.active_workflows:
            return False
        
        workflow = self.active_workflows[workflow_id]
        workflow.status = WorkflowStatus.ACTIVE
        workflow.started_at = datetime.now()
        
        # Start executing tasks
        asyncio.create_task(self._execute_workflow(workflow_id))
        
        return True
    
    async def _execute_workflow(self, workflow_id: str):
        """Execute workflow tasks based on dependencies."""
        workflow = self.active_workflows[workflow_id]
        
        try:
            while workflow.status == WorkflowStatus.ACTIVE:
                # Find ready tasks (dependencies completed)
                ready_tasks = self._get_ready_tasks(workflow)
                
                if not ready_tasks:
                    # Check if all tasks are completed
                    if all(task.status in [TaskStatus.COMPLETED, TaskStatus.SKIPPED] for task in workflow.tasks):
                        workflow.status = WorkflowStatus.COMPLETED
                        workflow.completed_at = datetime.now()
                        workflow.progress = 1.0
                        break
                    else:
                        # Wait for running tasks to complete
                        await asyncio.sleep(2)
                        continue
                
                # Execute ready tasks
                for task in ready_tasks:
                    asyncio.create_task(self._execute_task(workflow_id, task.id))
                
                # Update progress
                completed_tasks = len([t for t in workflow.tasks if t.status == TaskStatus.COMPLETED])
                workflow.progress = completed_tasks / len(workflow.tasks)
                
                await asyncio.sleep(1)
                
        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            workflow.metadata["error"] = str(e)
        
        # Save final state
        await self._save_workflow(workflow)
    
    def _get_ready_tasks(self, workflow: Workflow) -> List[WorkflowTask]:
        """Get tasks that are ready to execute (dependencies met)."""
        ready_tasks = []
        
        for task in workflow.tasks:
            if task.status != TaskStatus.PENDING:
                continue
            
            # Check if all dependencies are completed
            dependencies_met = True
            for dep_name in task.dependencies:
                dep_task = next(
                    (t for t in workflow.tasks if t.name.lower().replace(" ", "_") == dep_name.lower()),
                    None
                )
                if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                    dependencies_met = False
                    break
            
            if dependencies_met:
                ready_tasks.append(task)
        
        return ready_tasks
    
    async def _execute_task(self, workflow_id: str, task_id: str):
        """Execute a specific task."""
        workflow = self.active_workflows[workflow_id]
        task = next((t for t in workflow.tasks if t.id == task_id), None)
        
        if not task:
            return
        
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = datetime.now()
        
        try:
            # Execute task based on type and agent
            if task.agent_type in self.task_executors:
                result = await self.task_executors[task.agent_type](task)
            else:
                # Default execution - simulate task completion
                await asyncio.sleep(2)  # Simulate work
                result = {
                    "success": True,
                    "task_id": task.id,
                    "task_type": task.task_type,
                    "message": f"Completed {task.name}",
                    "timestamp": datetime.now().isoformat()
                }
            
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            
        except Exception as e:
            task.error = str(e)
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a workflow."""
        if workflow_id not in self.active_workflows:
            return None
        
        workflow = self.active_workflows[workflow_id]
        
        return {
            "workflow_id": workflow_id,
            "name": workflow.name,
            "description": workflow.description,
            "status": workflow.status.value,
            "progress": workflow.progress,
            "thread_id": workflow.thread_id,
            "created_at": workflow.created_at.isoformat(),
            "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
            "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
            "total_tasks": len(workflow.tasks),
            "completed_tasks": len([t for t in workflow.tasks if t.status == TaskStatus.COMPLETED]),
            "failed_tasks": len([t for t in workflow.tasks if t.status == TaskStatus.FAILED]),
            "tasks": [
                {
                    "id": task.id,
                    "name": task.name,
                    "description": task.description,
                    "status": task.status.value,
                    "agent_type": task.agent_type,
                    "estimated_duration": task.estimated_duration,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "error": task.error
                }
                for task in workflow.tasks
            ],
            "metadata": workflow.metadata
        }
    
    async def update_task_status(
        self,
        plan_id: str,
        task_id: str,
        status: str,
        notes: Optional[str] = None
    ):
        """Update task status (for compatibility with planning tools)."""
        # This method provides compatibility with the planning tools
        # In practice, task status is managed automatically by the orchestrator
        pass
    
    async def revise_workflow(
        self,
        workflow_id: str,
        new_tasks: List[str],
        reason: str
    ):
        """Revise a workflow by adding new tasks."""
        if workflow_id not in self.active_workflows:
            return
        
        workflow = self.active_workflows[workflow_id]
        
        # Add new tasks to the workflow
        for i, task_description in enumerate(new_tasks):
            task = WorkflowTask(
                id=f"{workflow_id}_rev_{i+1}",
                name=task_description,
                description=task_description,
                task_type="revision",
                dependencies=[],
                estimated_duration=10,
                agent_type=None,
                parameters={},
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                started_at=None,
                completed_at=None,
                result=None,
                error=None,
                metadata={"revision_reason": reason}
            )
            workflow.tasks.append(task)
        
        # Save updated workflow
        await self._save_workflow(workflow)
    
    def register_task_executor(self, agent_type: str, executor: callable):
        """Register a task executor for a specific agent type."""
        self.task_executors[agent_type] = executor
    
    async def _save_workflow(self, workflow: Workflow):
        """Save workflow to persistent storage."""
        workflow_data = asdict(workflow)
        
        # Convert datetime objects to ISO strings
        workflow_data["created_at"] = workflow.created_at.isoformat()
        workflow_data["started_at"] = workflow.started_at.isoformat() if workflow.started_at else None
        workflow_data["completed_at"] = workflow.completed_at.isoformat() if workflow.completed_at else None
        workflow_data["status"] = workflow.status.value
        
        for task_data in workflow_data["tasks"]:
            task_data["created_at"] = task_data["created_at"].isoformat() if task_data["created_at"] else None
            task_data["started_at"] = task_data["started_at"].isoformat() if task_data["started_at"] else None
            task_data["completed_at"] = task_data["completed_at"].isoformat() if task_data["completed_at"] else None
            task_data["status"] = task_data["status"].value if hasattr(task_data["status"], "value") else task_data["status"]
        
        file_path = f"{self.storage_path}/workflow_{workflow.id}.json"
        with open(file_path, 'w') as f:
            json.dump(workflow_data, f, indent=2)
    
    async def cleanup(self):
        """Cleanup orchestrator resources."""
        # Save all active workflows
        for workflow in self.active_workflows.values():
            await self._save_workflow(workflow)
