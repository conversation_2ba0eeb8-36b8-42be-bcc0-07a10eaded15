import React, { useState } from 'react';
import { ChevronDown, ChevronR<PERSON>, <PERSON>, <PERSON>, <PERSON>Off } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface ThinkingDisplayProps {
  thinking: string;
  modelInfo?: {
    model: string;
    supports_thinking: boolean;
  };
}

export function ThinkingDisplay({ thinking, modelInfo }: ThinkingDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showRaw, setShowRaw] = useState(false);

  if (!thinking || !thinking.trim()) {
    return null;
  }

  const modelName = modelInfo?.model || 'Unknown Model';
  const shortModelName = modelName.split('/').pop() || modelName;

  return (
    <div className="mb-4 border border-[#333] rounded-xl overflow-hidden bg-[#0A0A0A]">
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 hover:bg-[#111] transition-colors duration-200"
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Brain className="w-4 h-4 text-purple-400" />
            <span className="text-sm font-medium text-purple-400">
              Thinking Process
            </span>
          </div>
          <div className="text-xs text-[#666] bg-[#222] px-2 py-1 rounded-full">
            {shortModelName}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-[#666]">
            {isExpanded ? 'Hide' : 'Show'} reasoning
          </span>
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-[#666]" />
          ) : (
            <ChevronRight className="w-4 h-4 text-[#666]" />
          )}
        </div>
      </button>

      {/* Content */}
      {isExpanded && (
        <div className="border-t border-[#333]">
          {/* Controls */}
          <div className="flex items-center justify-between p-3 bg-[#111] border-b border-[#333]">
            <div className="text-xs text-[#888]">
              This shows the model's internal reasoning process
            </div>
            <button
              onClick={() => setShowRaw(!showRaw)}
              className="flex items-center gap-1 text-xs text-[#666] hover:text-white transition-colors"
            >
              {showRaw ? (
                <>
                  <Eye className="w-3 h-3" />
                  Formatted
                </>
              ) : (
                <>
                  <EyeOff className="w-3 h-3" />
                  Raw
                </>
              )}
            </button>
          </div>

          {/* Thinking Content */}
          <div className="p-4 max-h-96 overflow-y-auto">
            {showRaw ? (
              <pre className="text-xs text-[#ccc] whitespace-pre-wrap font-mono bg-[#000] p-3 rounded-lg border border-[#333]">
                {thinking}
              </pre>
            ) : (
              <div className="prose prose-invert prose-sm max-w-none">
                <div className="text-sm text-[#ccc] leading-relaxed">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      // Custom styling for thinking content
                      p: ({ children }) => (
                        <p className="mb-3 text-[#ccc] leading-relaxed">{children}</p>
                      ),
                      h1: ({ children }) => (
                        <h1 className="text-lg font-semibold text-purple-300 mb-3 border-b border-[#333] pb-2">
                          {children}
                        </h1>
                      ),
                      h2: ({ children }) => (
                        <h2 className="text-base font-semibold text-purple-300 mb-2 mt-4">
                          {children}
                        </h2>
                      ),
                      h3: ({ children }) => (
                        <h3 className="text-sm font-semibold text-purple-300 mb-2 mt-3">
                          {children}
                        </h3>
                      ),
                      ul: ({ children }) => (
                        <ul className="list-disc list-inside mb-3 space-y-1 text-[#ccc]">
                          {children}
                        </ul>
                      ),
                      ol: ({ children }) => (
                        <ol className="list-decimal list-inside mb-3 space-y-1 text-[#ccc]">
                          {children}
                        </ol>
                      ),
                      li: ({ children }) => (
                        <li className="text-[#ccc] leading-relaxed">{children}</li>
                      ),
                      code: ({ children, className }) => {
                        const isInline = !className;
                        if (isInline) {
                          return (
                            <code className="bg-[#222] text-purple-300 px-1 py-0.5 rounded text-xs font-mono">
                              {children}
                            </code>
                          );
                        }
                        return (
                          <code className="block bg-[#000] text-[#ccc] p-3 rounded-lg border border-[#333] text-xs font-mono whitespace-pre-wrap">
                            {children}
                          </code>
                        );
                      },
                      blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-purple-400 pl-4 italic text-[#aaa] my-3">
                          {children}
                        </blockquote>
                      ),
                      strong: ({ children }) => (
                        <strong className="font-semibold text-purple-300">{children}</strong>
                      ),
                      em: ({ children }) => (
                        <em className="italic text-purple-200">{children}</em>
                      ),
                    }}
                  >
                    {thinking}
                  </ReactMarkdown>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 bg-[#111] border-t border-[#333] text-xs text-[#666]">
            <div className="flex items-center justify-between">
              <span>
                💭 This reasoning was generated by {shortModelName}
              </span>
              <span>
                {thinking.length} characters
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
