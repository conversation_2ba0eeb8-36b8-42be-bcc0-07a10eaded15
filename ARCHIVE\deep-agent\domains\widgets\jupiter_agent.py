"""
Jupiter Widget Specialist Agent
Handles all Jupiter swap widget interactions, trading guidance, and DEX aggregation
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI


class JupiterWidgetAgent:
    """Specialized agent for Jupiter widget management and swap guidance."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get("model_name", "z-ai/glm-4.5")
        self.api_key = config.get("openrouter_api_key")
        
        # Configure LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=0.7,
            max_tokens=2000
        )
        
        # Initialize tools
        self.tools = self._get_jupiter_tools()

        # Create simple LLM without agent complexity
        # We'll handle tool calling manually to avoid validation issues
        
        # Widget state
        self.active_swaps = {}
        self.price_cache = {}
        
    def _get_system_prompt(self) -> str:
        """Get the Jupiter specialist system prompt."""
        return """You are the Jupiter Widget Specialist, an expert in Solana DEX aggregation and token swapping.

## Your Expertise
- Jupiter DEX aggregator mechanics and routing
- Solana token swapping and slippage optimization
- Real-time price comparison across DEXs
- Swap route analysis and best execution

## Your Responsibilities
1. **Widget Management**: Launch Jupiter widgets for token swapping
2. **Swap Guidance**: Provide trading advice and route optimization
3. **Price Analysis**: Compare prices across different DEXs
4. **Risk Assessment**: Warn about slippage, liquidity, and swap risks

## When to Launch Widgets
ALWAYS call show_jupiter_widget() when users:
- Want to "swap", "trade", or "exchange" tokens
- Ask about "Jupiter" or "DEX aggregator"
- Need to "buy" or "sell" specific tokens
- Want to compare swap routes

## Response Style
- Be helpful and focused on trading efficiency
- Provide clear swap guidance and route explanations
- Always launch the widget when appropriate
- Use trading terminology and focus on best execution
- Warn about risks like slippage and impermanent loss

Remember: You MUST actually call the show_jupiter_widget() function when users need to swap!"""

    def _get_jupiter_tools(self) -> List:
        """Get Jupiter-specific tools."""
        
        @tool
        def show_jupiter_widget() -> Dict[str, Any]:
            """
            Display the Jupiter widget for token swapping on Solana.
            
            Use this when users want to:
            - Swap tokens on Solana
            - Access Jupiter's DEX aggregator
            - Compare swap routes and prices
            - Execute token trades
            """
            return {
                "widget_type": "jupiter",
                "description": "Displaying Jupiter widget for token swapping"
            }
        
        @tool
        def analyze_swap_routes(from_token: str, to_token: str) -> Dict[str, Any]:
            """Analyze optimal swap routes between two tokens."""
            return {
                "from_token": from_token,
                "to_token": to_token,
                "best_route": f"Optimal route for {from_token} → {to_token}",
                "estimated_slippage": "0.1-0.5%",
                "recommended_slippage": "1%",
                "route_analysis": "Jupiter will find the best price across all Solana DEXs"
            }
        
        @tool
        def get_swap_guidance(swap_amount: str, token_pair: str) -> Dict[str, Any]:
            """Get guidance for a specific swap amount and token pair."""
            return {
                "swap_amount": swap_amount,
                "token_pair": token_pair,
                "guidance": f"For swapping {swap_amount} of {token_pair}",
                "tips": [
                    "Check slippage tolerance before swapping",
                    "Consider market conditions and liquidity",
                    "Use limit orders for large amounts"
                ]
            }
        
        return [show_jupiter_widget, analyze_swap_routes, get_swap_guidance]
    
    async def process_request(
        self,
        message: str,
        thread_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a Jupiter-related request."""
        try:
            # Determine if we should show widget based on message content
            should_show_widget = self._should_show_widget(message)

            # Generate response using direct LLM call
            system_prompt = self._get_system_prompt()

            response = await self.llm.ainvoke([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ])

            # Prepare function call if widget should be shown
            function_call = None
            if should_show_widget:
                function_call = {
                    "name": "showJupiterWidget",
                    "arguments": {}
                }

            return {
                "content": response.content,
                "function_call": function_call,
                "specialist": "jupiter",
                "metadata": {
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent_type": "jupiter_specialist"
                }
            }

        except Exception as e:
            return {
                "content": f"I encountered an error with Jupiter swap data: {str(e)}",
                "error": str(e),
                "specialist": "jupiter"
            }

    def _should_show_widget(self, message: str) -> bool:
        """Determine if widget should be shown based on message content."""
        message_lower = message.lower()
        widget_triggers = [
            "show", "display", "open", "launch", "widget",
            "swap", "trade", "jupiter", "let's see"
        ]
        return any(trigger in message_lower for trigger in widget_triggers)
    
    def _extract_widget_call(self, messages) -> Optional[Dict[str, Any]]:
        """Extract widget calls from agent response."""
        for message in reversed(messages):
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    if tool_call.get('name') == 'show_jupiter_widget':
                        return {
                            "name": "showJupiterWidget",
                            "arguments": {}
                        }
        return None
