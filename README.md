# Chad GPT

A modern AI chat interface with integrated widgets and tools.

## Features

- AI-powered chat interface
- Integrated widgets for:
  - PumpFun token trading
  - DexScreener analytics
  - Jupiter DEX
  - Phantom wallet
- Agent management system
- Dark theme UI

## Installation

```bash
npm install
```

## Development

### Starting the Main Application

```bash
npm run dev
```

### PumpFun Widget Data Source

The PumpFun widget now uses a direct Supabase API connection for reliable token data. No additional proxy servers are required.

## Build

```bash
npm run build
```

## Project Structure

- `/src/components` - React components
- `/src/pages` - Page components
- `/src/contexts` - React contexts for state management
- `/src/lib` - Utility functions and API clients
- `/src/types` - TypeScript type definitions

## Available Routes

- `/` - Main chat interface
- `/agents` - Agent management