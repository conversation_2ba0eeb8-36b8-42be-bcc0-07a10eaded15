import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Wallet, 
  TrendingUp, 
  Clock, 
  Image as ImageIcon, 
  Copy, 
  ExternalLink, 
  AlertCircle,
  RefreshCw,
  Eye,
  DollarSign,
  Activity
} from 'lucide-react';
import clsx from 'clsx';

interface WalletHolding {
  symbol: string;
  name: string;
  balance: number;
  value: number;
  price: number;
  change24h: number;
  icon?: string;
  mint?: string;
}

interface NFTHolding {
  name: string;
  collection: string;
  image: string;
  floorPrice?: number;
  lastSale?: number;
  mint: string;
}

interface WalletTransaction {
  signature: string;
  type: 'transfer' | 'swap' | 'nft' | 'defi';
  timestamp: number;
  amount?: number;
  token?: string;
  from?: string;
  to?: string;
  status: 'success' | 'failed';
}

interface ExternalWalletWidgetProps {
  isActive?: boolean;
}

export function ExternalWalletWidget({ isActive = true }: ExternalWalletWidgetProps) {
  const [walletAddress, setWalletAddress] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [activeTab, setActiveTab] = useState<'holdings' | 'nfts' | 'activity'>('holdings');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Mock data - will be replaced with real API calls later
  const [holdings, setHoldings] = useState<WalletHolding[]>([]);
  const [nfts, setNfts] = useState<NFTHolding[]>([]);
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [totalValue, setTotalValue] = useState(0);

  const handleSearch = async () => {
    if (!searchInput.trim()) return;
    
    setIsLoading(true);
    setError(null);
    setWalletAddress(searchInput.trim());
    
    try {
      // TODO: Replace with real API calls
      // For now, simulate loading with mock data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock holdings data
      const mockHoldings: WalletHolding[] = [
        {
          symbol: 'SOL',
          name: 'Solana',
          balance: 125.45,
          value: 12545.67,
          price: 100.02,
          change24h: 5.2,
          icon: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png'
        },
        {
          symbol: 'USDC',
          name: 'USD Coin',
          balance: 5000.00,
          value: 5000.00,
          price: 1.00,
          change24h: 0.1,
          icon: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png'
        },
        {
          symbol: 'BONK',
          name: 'Bonk',
          balance: 1000000,
          value: 234.56,
          price: 0.00023456,
          change24h: -12.3,
          icon: 'https://arweave.net/hQiPZOsRZXGXBJd_82PhVdlM_hACsT_q6wqwf5cSY7I'
        }
      ];

      // Mock NFT data
      const mockNFTs: NFTHolding[] = [
        {
          name: 'DeGod #1234',
          collection: 'DeGods',
          image: 'https://metadata.degods.com/g/1234.png',
          floorPrice: 15.5,
          lastSale: 18.2,
          mint: 'ABC123...'
        },
        {
          name: 'SMB #5678',
          collection: 'Solana Monkey Business',
          image: 'https://arweave.net/example.png',
          floorPrice: 2.1,
          lastSale: 2.5,
          mint: 'DEF456...'
        }
      ];

      // Mock transaction data
      const mockTransactions: WalletTransaction[] = [
        {
          signature: 'ABC123...DEF456',
          type: 'swap',
          timestamp: Date.now() - 3600000,
          amount: 100,
          token: 'SOL → USDC',
          status: 'success'
        },
        {
          signature: 'GHI789...JKL012',
          type: 'transfer',
          timestamp: Date.now() - 7200000,
          amount: 50,
          token: 'SOL',
          from: searchInput.trim(),
          to: 'XYZ789...',
          status: 'success'
        }
      ];

      setHoldings(mockHoldings);
      setNfts(mockNFTs);
      setTransactions(mockTransactions);
      setTotalValue(mockHoldings.reduce((sum, holding) => sum + holding.value, 0));
      
    } catch (err) {
      setError('Failed to fetch wallet data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(2)}K`;
    }
    return value.toLocaleString();
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'swap': return <TrendingUp size={16} className="text-blue-500" />;
      case 'transfer': return <ExternalLink size={16} className="text-green-500" />;
      case 'nft': return <ImageIcon size={16} className="text-purple-500" />;
      case 'defi': return <DollarSign size={16} className="text-yellow-500" />;
      default: return <Activity size={16} className="text-gray-500" />;
    }
  };

  // If no wallet is loaded, show search interface
  if (!walletAddress) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
        <div className="flex items-center gap-3 mb-6">
          <div className="relative">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <Eye className="w-4 h-4 text-white" />
            </div>
          </div>
          <h2 className="text-lg font-semibold">External Wallet Inspector</h2>
          <span className="text-xs bg-blue-500/10 text-blue-500 px-2 py-0.5 rounded-full font-medium">RESEARCH</span>
        </div>

        <div className="text-center py-12">
          <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-6">
            <Search className="w-8 h-8 text-[#666]" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Investigate Any Wallet</h3>
          <p className="text-sm text-[#666] mb-8 max-w-md mx-auto">
            Enter a wallet address to analyze holdings, NFTs, and transaction history. Perfect for copy trading and wallet research.
          </p>

          <div className="space-y-4">
            <div className="relative">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="Enter wallet address (e.g., 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU)"
                className="w-full bg-[#0A0A0A] rounded-xl p-4 text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 transition-all duration-200"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button
                onClick={handleSearch}
                disabled={!searchInput.trim() || isLoading}
                className="absolute right-2 top-2 bottom-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
              >
                {isLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <Search className="w-4 h-4" />
                )}
                {isLoading ? 'Analyzing...' : 'Analyze'}
              </button>
            </div>

            {error && (
              <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle size={16} />
                  {error}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Wallet analysis view
  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
      <div className="flex items-center gap-3 mb-6">
        <div className="relative">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
            <Eye className="w-4 h-4 text-white" />
          </div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-[#111]"></div>
        </div>
        <h2 className="text-lg font-semibold">Wallet Analysis</h2>
        <span className="text-xs bg-blue-500/10 text-blue-500 px-2 py-0.5 rounded-full font-medium">ACTIVE</span>
      </div>

      {/* Wallet Overview */}
      <div className="bg-[#181818] rounded-xl p-6 mb-6">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-semibold mb-1">{formatCurrency(totalValue)}</h3>
          <div className="flex items-center justify-center gap-2">
            <p className="text-sm text-[#666] font-mono">
              {formatAddress(walletAddress)}
            </p>
            <button
              onClick={() => navigator.clipboard.writeText(walletAddress)}
              className="p-1 hover:bg-[#222] rounded transition-colors"
            >
              <Copy size={12} />
            </button>
          </div>
        </div>

        <div className="flex items-center justify-center gap-4">
          <button
            onClick={() => setWalletAddress('')}
            className="flex items-center gap-2 bg-[#111] rounded-lg px-4 py-2 hover:bg-[#181818] transition-all duration-200"
          >
            <Search size={16} />
            <span className="text-sm">New Search</span>
          </button>
          <button
            onClick={handleSearch}
            disabled={isLoading}
            className="flex items-center gap-2 bg-[#111] rounded-lg px-4 py-2 hover:bg-[#181818] transition-all duration-200 disabled:opacity-50"
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            <span className="text-sm">{isLoading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-2 p-1 bg-[#0A0A0A] rounded-lg mb-6">
        <button
          onClick={() => setActiveTab('holdings')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'holdings' ? "bg-blue-500 text-white" : "text-[#666] hover:text-white"
          )}
        >
          <Wallet className="w-4 h-4 inline-block mr-2" />
          Holdings
        </button>
        <button
          onClick={() => setActiveTab('nfts')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'nfts' ? "bg-blue-500 text-white" : "text-[#666] hover:text-white"
          )}
        >
          <ImageIcon className="w-4 h-4 inline-block mr-2" />
          NFTs
        </button>
        <button
          onClick={() => setActiveTab('activity')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'activity' ? "bg-blue-500 text-white" : "text-[#666] hover:text-white"
          )}
        >
          <Clock className="w-4 h-4 inline-block mr-2" />
          Activity
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'holdings' && (
        <div className="space-y-4">
          {holdings.map((holding, index) => (
            <div key={index} className="bg-[#0A0A0A] rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <img
                    src={holding.icon || '/placeholder-token.png'}
                    alt={holding.symbol}
                    className="w-10 h-10 rounded-full"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder-token.png';
                    }}
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{holding.symbol}</span>
                      <span className="text-xs text-[#666]">{holding.name}</span>
                    </div>
                    <div className="text-sm text-[#666]">{formatNumber(holding.balance)}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{formatCurrency(holding.value)}</div>
                  <div className={clsx(
                    "text-sm",
                    holding.change24h >= 0 ? "text-green-500" : "text-red-500"
                  )}>
                    {holding.change24h >= 0 ? '+' : ''}{holding.change24h.toFixed(2)}%
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'nfts' && (
        <div className="space-y-4">
          {nfts.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-4">
                <ImageIcon className="w-6 h-6 text-[#666]" />
              </div>
              <h3 className="text-lg font-medium mb-2">No NFTs Found</h3>
              <p className="text-sm text-[#666]">
                This wallet doesn't hold any NFTs
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              {nfts.map((nft, index) => (
                <div key={index} className="bg-[#0A0A0A] rounded-xl overflow-hidden">
                  <img
                    src={nft.image}
                    alt={nft.name}
                    className="w-full h-32 object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder-nft.png';
                    }}
                  />
                  <div className="p-3">
                    <h4 className="font-medium text-sm mb-1">{nft.name}</h4>
                    <p className="text-xs text-[#666] mb-2">{nft.collection}</p>
                    {nft.floorPrice && (
                      <div className="text-xs">
                        <span className="text-[#666]">Floor: </span>
                        <span className="text-white">{nft.floorPrice} SOL</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'activity' && (
        <div className="space-y-4">
          {transactions.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-[#666]" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Recent Activity</h3>
              <p className="text-sm text-[#666]">
                No recent transactions found
              </p>
            </div>
          ) : (
            transactions.map((tx, index) => (
              <div key={index} className="bg-[#0A0A0A] rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getTransactionIcon(tx.type)}
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium capitalize">{tx.type}</span>
                        <span className={clsx(
                          "text-xs px-2 py-0.5 rounded-full",
                          tx.status === 'success' ? "bg-green-500/10 text-green-500" : "bg-red-500/10 text-red-500"
                        )}>
                          {tx.status}
                        </span>
                      </div>
                      <div className="text-sm text-[#666]">
                        {tx.token && `${tx.amount} ${tx.token}`}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-[#666]">
                      {new Date(tx.timestamp).toLocaleDateString()}
                    </div>
                    <button
                      onClick={() => navigator.clipboard.writeText(tx.signature)}
                      className="text-xs text-blue-500 hover:text-blue-400 flex items-center gap-1"
                    >
                      <Copy size={10} />
                      {formatAddress(tx.signature)}
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
}
