# Chad GPT Deep Agents

A comprehensive deep agent system for the Chad GPT conversational AI platform, implementing the four-pillar Deep Agent architecture with LangGraph and React integration.

## Overview

Chad GPT Deep Agents extends the existing Chad GPT platform with sophisticated agentic capabilities, enabling users to create specialized agents for complex multi-step workflows across different domains.

### Four Pillars of Deep Agent Architecture

1. **Planning Tools** - Multi-step workflow decomposition and task management
2. **Sub-Agent Delegation** - Specialized domain agents with focused expertise
3. **File-Backed Memory** - Persistent storage for conversation context and analysis
4. **Comprehensive System Prompts** - Detailed domain-specific prompts with platform integration

## Quick Start

### Prerequisites

- Node.js 18+ and npm/yarn
- Python 3.9+ with pip
- OpenRouter API key
- Supabase credentials (optional for enhanced features)

### Installation

1. **Install Frontend Dependencies**
```bash
npm install
```

2. **Install Agent Dependencies**
```bash
cd chad-gpt-deep-agents/src
pip install -r requirements.txt
```

3. **Configure Environment**
```bash
# Copy environment template
cp .env.example .env

# Add your API keys
VITE_OPENROUTER_API_KEY=your_openrouter_key
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key
```

### Running the System

1. **Start the Frontend**
```bash
npm run dev
```

2. **Start the Deep Agent Backend**
```bash
cd chad-gpt-deep-agents/src
python -m core.platform_agent_loop
```

3. **Access the Platform**
Open http://localhost:5173 in your browser

## Core Features

### Specialized Domain Agents

- **CRYPTO_ANALYST** - Comprehensive cryptocurrency and DeFi analysis
- **RESEARCH_SPECIALIST** - Information gathering and synthesis
- **DEVELOPER_ASSISTANT** - Code generation and technical support
- **SUPPORT_AGENT** - User guidance and platform assistance
- **WIDGET_BUILDER** - Custom widget development and integration

### Platform Integration

- **Widget Management** - Bidirectional communication with React widgets
- **Real-time Data** - WebSocket integration for live market feeds
- **Conversation State** - Persistent context across complex workflows
- **Multi-Model Support** - OpenRouter integration with model switching
- **Responsive Design** - Mobile-optimized interface with dark theme

### Advanced Capabilities

- **Multi-Step Planning** - Automatic workflow decomposition
- **Context Preservation** - Long-term memory across conversations
- **Error Recovery** - Graceful degradation and retry mechanisms
- **Performance Optimization** - Caching and efficient data handling
- **Security** - Input validation and secure API handling

## Architecture

```
chad-gpt-deep-agents/
├── docs/                    # Comprehensive documentation
├── src/                     # Core agent implementation
│   ├── core/               # Agent loop and state management
│   ├── integrations/       # Platform and API integrations
│   ├── widgets/            # Widget management and communication
│   └── prompts/            # Domain-specific system prompts
├── domains/                # Domain-specific implementations
│   ├── cryptocurrency/     # Crypto analysis workflows
│   ├── research/           # Research and information gathering
│   ├── development/        # Code generation and technical support
│   └── support/            # User guidance and platform help
├── tests/                  # Comprehensive testing suite
└── examples/               # Working examples and tutorials
```

## Usage Examples

### Creating a Crypto Analysis Agent

```python
from domains.cryptocurrency.agent import create_crypto_agent

# Create specialized crypto agent
agent = create_crypto_agent()

# Run comprehensive token analysis
result = agent.analyze_token("0x1234...abcd")
```

### Building Custom Widgets

```python
from src.widgets.custom_widget_builder import WidgetBuilder

# Create custom widget
builder = WidgetBuilder()
widget = builder.create_widget(
    widget_type="price_tracker",
    config={"tokens": ["BTC", "ETH"], "refresh_rate": 30}
)
```

### Multi-Domain Workflows

```python
from src.core.workflow_planning import WorkflowPlanner

# Plan complex multi-step workflow
planner = WorkflowPlanner()
workflow = planner.create_workflow([
    {"domain": "research", "task": "market_analysis"},
    {"domain": "crypto", "task": "token_evaluation"},
    {"domain": "development", "task": "widget_creation"}
])
```

## Documentation

- [Architecture Guide](docs/architecture.md) - System design and integration patterns
- [Tools Reference](docs/tools.md) - Complete API and tool documentation
- [User Guide](docs/user-experience.md) - Step-by-step usage instructions
- [Developer Guide](docs/widget-integration.md) - Extension and customization
- [Prompt Patterns](docs/prompts.md) - Conversational AI best practices

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

- Documentation: [docs/](docs/)
- Issues: GitHub Issues
- Discussions: GitHub Discussions
- Email: <EMAIL>
