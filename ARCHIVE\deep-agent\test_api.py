#!/usr/bin/env python3
"""Test script to debug the tool_call_id issue."""

import requests
import json

def test_api():
    url = "http://localhost:8001/api/chat"
    headers = {"Content-Type": "application/json"}
    
    data = {
        "messages": [
            {
                "role": "user", 
                "content": "what's up with BTC why is it dipping"
            }
        ],
        "threadId": "test123"
    }
    
    print("🔍 Sending request to Deep Agent...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api()
