"""
Sequential Thinking MCP integration tools for the Chad GPT Deep Agent.
These tools provide advanced reasoning capabilities through structured thinking processes.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional, Union
import json
import subprocess
import os
import time
from datetime import datetime


class SequentialThinkingMCP:
    """Interface to the Sequential Thinking MCP server."""
    
    def __init__(self):
        self.server_process = None
        self.server_running = False
        
    def start_server(self):
        """Start the Sequential Thinking MCP server."""
        try:
            # Start the MCP server using npx
            self.server_process = subprocess.Popen(
                ["npx", "-y", "@modelcontextprotocol/server-sequential-thinking"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True
            )
            
            # Give the server time to start
            time.sleep(2)
            
            if self.server_process.poll() is None:
                self.server_running = True
                print("✅ Sequential Thinking MCP server started successfully")
                return True
            else:
                print("❌ Failed to start Sequential Thinking MCP server")
                return False
                
        except Exception as e:
            print(f"❌ Error starting Sequential Thinking MCP server: {e}")
            return False
    
    def stop_server(self):
        """Stop the Sequential Thinking MCP server."""
        if self.server_process:
            self.server_process.terminate()
            self.server_process = None
            self.server_running = False
            print("🛑 Sequential Thinking MCP server stopped")
    
    def send_request(self, method: str, params: Dict = None) -> Dict[str, Any]:
        """Send a JSON-RPC request to the MCP server."""
        if not self.server_running:
            return {
                "success": False,
                "error": "Sequential Thinking MCP server is not running"
            }
        
        try:
            request = {
                "jsonrpc": "2.0",
                "id": int(time.time() * 1000),
                "method": method,
                "params": params or {}
            }
            
            request_json = json.dumps(request) + "\n"
            
            # Send request to server
            self.server_process.stdin.write(request_json)
            self.server_process.stdin.flush()
            
            # Read response
            response_line = self.server_process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                return {
                    "success": True,
                    "data": response,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "No response from MCP server"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error communicating with MCP server: {e}"
            }


# Global MCP instance
_mcp_instance = None

def get_mcp_instance():
    """Get or create the Sequential Thinking MCP instance."""
    global _mcp_instance
    if _mcp_instance is None:
        _mcp_instance = SequentialThinkingMCP()
        _mcp_instance.start_server()
    return _mcp_instance


@tool
def sequentialthinking_Sequential_thinking(
    thought: str,
    next_thought_needed: bool,
    thought_number: int,
    total_thoughts: int,
    is_revision: Optional[bool] = False,
    revises_thought: Optional[int] = None,
    branch_from_thought: Optional[int] = None,
    branch_id: Optional[str] = None,
    needs_more_thoughts: Optional[bool] = False
) -> Dict[str, Any]:
    """
    A detailed tool for dynamic and reflective problem-solving through thoughts.
    This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
    Each thought can build on, question, or revise previous insights as understanding deepens.

    When to use this tool:
    - Breaking down complex problems into steps
    - Planning and design with room for revision
    - Analysis that might need course correction
    - Problems where the full scope might not be clear initially
    - Problems that require a multi-step solution
    - Tasks that need to maintain context over multiple steps
    - Situations where irrelevant information needs to be filtered out

    Key features:
    - You can adjust total_thoughts up or down as you progress
    - You can question or revise previous thoughts
    - You can add more thoughts even after reaching what seemed like the end
    - You can express uncertainty and explore alternative approaches
    - Not every thought needs to build linearly - you can branch or backtrack
    - Generates a solution hypothesis
    - Verifies the hypothesis based on the Chain of Thought steps
    - Repeats the process until satisfied
    - Provides a correct answer

    Parameters explained:
    - thought: Your current thinking step, which can include:
    * Regular analytical steps
    * Revisions of previous thoughts
    * Questions about previous decisions
    * Realizations about needing more analysis
    * Changes in approach
    * Hypothesis generation
    * Hypothesis verification
    - next_thought_needed: True if you need more thinking, even if at what seemed like the end
    - thought_number: Current number in sequence (can go beyond initial total if needed)
    - total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)
    - is_revision: A boolean indicating if this thought revises previous thinking
    - revises_thought: If is_revision is true, which thought number is being reconsidered
    - branch_from_thought: If branching, which thought number is the branching point
    - branch_id: Identifier for the current branch (if any)
    - needs_more_thoughts: If reaching end but realizing more thoughts needed

    You should:
    1. Start with an initial estimate of needed thoughts, but be ready to adjust
    2. Feel free to question or revise previous thoughts
    3. Don't hesitate to add more thoughts if needed, even at the "end"
    4. Express uncertainty when present
    5. Mark thoughts that revise previous thinking or branch into new paths
    6. Ignore information that is irrelevant to the current step
    7. Generate a solution hypothesis when appropriate
    8. Verify the hypothesis based on the Chain of Thought steps
    9. Repeat the process until satisfied with the solution
    10. Provide a single, ideally correct answer as the final output
    11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached
    """
    
    mcp = get_mcp_instance()
    
    # Prepare parameters for the MCP server
    params = {
        "thought": thought,
        "nextThoughtNeeded": next_thought_needed,
        "thoughtNumber": thought_number,
        "totalThoughts": total_thoughts
    }
    
    # Add optional parameters if provided
    if is_revision is not None:
        params["isRevision"] = is_revision
    if revises_thought is not None:
        params["revisesThought"] = revises_thought
    if branch_from_thought is not None:
        params["branchFromThought"] = branch_from_thought
    if branch_id is not None:
        params["branchId"] = branch_id
    if needs_more_thoughts is not None:
        params["needsMoreThoughts"] = needs_more_thoughts
    
    # Send request to MCP server
    result = mcp.send_request("tools/call", {
        "name": "sequentialthinking_Sequential_thinking",
        "arguments": params
    })
    
    if result["success"]:
        return {
            "success": True,
            "thought_processed": thought,
            "thought_number": thought_number,
            "total_thoughts": total_thoughts,
            "next_thought_needed": next_thought_needed,
            "mcp_response": result["data"],
            "timestamp": datetime.now().isoformat()
        }
    else:
        # Fallback to local processing if MCP server is unavailable
        return {
            "success": True,
            "thought_processed": thought,
            "thought_number": thought_number,
            "total_thoughts": total_thoughts,
            "next_thought_needed": next_thought_needed,
            "fallback_mode": True,
            "note": "Processed locally - MCP server unavailable",
            "error": result.get("error"),
            "timestamp": datetime.now().isoformat()
        }


@tool
def start_sequential_thinking_session(problem_description: str, initial_thought_estimate: int = 5) -> Dict[str, Any]:
    """
    Start a new sequential thinking session for a complex problem.
    
    Args:
        problem_description: Description of the problem to solve
        initial_thought_estimate: Initial estimate of how many thoughts might be needed
        
    Returns:
        Dictionary with session information and first thought prompt
    """
    
    session_id = f"thinking_session_{int(time.time())}"
    
    return {
        "success": True,
        "session_id": session_id,
        "problem": problem_description,
        "initial_estimate": initial_thought_estimate,
        "next_step": "Call sequentialthinking_Sequential_thinking with your first thought",
        "guidance": {
            "start_with": "Begin by analyzing the problem and breaking it down",
            "remember": "You can revise thoughts, branch, and adjust total_thoughts as needed",
            "end_when": "You have a satisfactory solution and set next_thought_needed=False"
        },
        "timestamp": datetime.now().isoformat()
    }


# List of Sequential Thinking tools
SEQUENTIAL_THINKING_TOOLS = [
    sequentialthinking_Sequential_thinking,
    start_sequential_thinking_session
]

# Cleanup function
def cleanup_sequential_thinking():
    """Clean up the Sequential Thinking MCP server."""
    global _mcp_instance
    if _mcp_instance:
        _mcp_instance.stop_server()
        _mcp_instance = None

print("✅ Sequential Thinking MCP tools loaded")
