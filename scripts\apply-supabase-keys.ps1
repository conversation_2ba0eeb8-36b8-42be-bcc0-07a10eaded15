param(
  [string]$JwtSecret = 'your-super-secret-jwt-token-with-at-least-32-characters-long'
)
$ErrorActionPreference='Stop'
$projRoot = (Resolve-Path "$PSScriptRoot\..\").Path
$tmp = Join-Path $projRoot ".secrets-tmp"
New-Item -ItemType Directory -Force -Path $tmp | Out-Null

# Generate keys
$keysPath = Join-Path $tmp 'keys.json'
node (Join-Path $projRoot 'scripts/generate-supabase-keys.cjs') $JwtSecret $keysPath | Out-Null
if (!(Test-Path $keysPath)) { throw 'Failed to generate keys' }
$keys = Get-Content $keysPath | ConvertFrom-Json
$anon = $keys.anon
$service = $keys.service
if (-not $anon -or -not $service) { throw 'Keys missing in generated file' }

# Write API server .env
$apiEnv = Join-Path $projRoot 'AP3X-PumP2\api-server\.env'
$api = @()
if (Test-Path $apiEnv) { $api = Get-Content $apiEnv }
$api = $api | Where-Object { $_ -notmatch '^\s*SUPABASE_URL\s*=' -and $_ -notmatch '^\s*SUPABASE_SERVICE_ROLE_KEY\s*=' }
$api += 'SUPABASE_URL=http://*************:8082/rest/v1'
$api += ('SUPABASE_SERVICE_ROLE_KEY=' + $service)
Set-Content -Path $apiEnv -Value $api -NoNewline:$false

# Write frontend .env.local
$feEnv = Join-Path $projRoot '.env.local'
$fe = @()
if (Test-Path $feEnv) { $fe = Get-Content $feEnv }
$fe = $fe | Where-Object { $_ -notmatch '^\s*VITE_SUPABASE_REST_URL\s*=' -and $_ -notmatch '^\s*VITE_SUPABASE_ANON_KEY\s*=' }
$fe += 'VITE_SUPABASE_REST_URL=http://*************:8082/rest/v1'
$fe += ('VITE_SUPABASE_ANON_KEY=' + $anon)
Set-Content -Path $feEnv -Value $fe -NoNewline:$false

# Cleanup
Remove-Item $keysPath -Force
Write-Host 'OK'
