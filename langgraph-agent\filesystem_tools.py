"""
File system tools for the Chad GPT Deep Agent.
These tools provide persistent storage and workspace management.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional
import os
import json
from pathlib import Path
from datetime import datetime


# Base directory for agent workspace - COMPLETELY ISOLATED from application code
# This ensures agent files are never intermingled with the application
WORKSPACE_DIR = Path(__file__).parent.parent / "AGENT_WORKSPACE"
WORKSPACE_DIR.mkdir(exist_ok=True)

# Create organized subdirectories for different types of content
(WORKSPACE_DIR / "analysis").mkdir(exist_ok=True)
(WORKSPACE_DIR / "reports").mkdir(exist_ok=True)
(WORKSPACE_DIR / "research").mkdir(exist_ok=True)
(WORKSPACE_DIR / "plans").mkdir(exist_ok=True)
(WORKSPACE_DIR / "data").mkdir(exist_ok=True)
(WORKSPACE_DIR / "temp").mkdir(exist_ok=True)


@tool
def write_file(path: str, content: str, overwrite: bool = True) -> Dict[str, Any]:
    """
    Write content to a file in the agent workspace.
    
    Args:
        path: Relative path within the workspace (e.g., "analysis/report.md")
        content: Content to write to the file
        overwrite: Whether to overwrite existing files (default: True)
        
    Returns:
        Dictionary confirming the file write operation
    """
    try:
        full_path = WORKSPACE_DIR / path
        
        # Create parent directories if they don't exist
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Check if file exists and overwrite is False
        if full_path.exists() and not overwrite:
            return {
                "success": False,
                "error": f"File {path} already exists and overwrite=False",
                "path": str(full_path)
            }
        
        # Write the file
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        file_size = full_path.stat().st_size
        
        return {
            "success": True,
            "path": path,
            "full_path": str(full_path),
            "size_bytes": file_size,
            "message": f"Successfully wrote {file_size} bytes to {path}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "path": path
        }


@tool
def read_file(path: str) -> Dict[str, Any]:
    """
    Read content from a file in the agent workspace.
    
    Args:
        path: Relative path within the workspace
        
    Returns:
        Dictionary containing the file content and metadata
    """
    try:
        full_path = WORKSPACE_DIR / path
        
        if not full_path.exists():
            return {
                "success": False,
                "error": f"File {path} does not exist",
                "path": path
            }
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        file_stats = full_path.stat()
        
        return {
            "success": True,
            "path": path,
            "content": content,
            "size_bytes": file_stats.st_size,
            "modified_at": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            "message": f"Successfully read {file_stats.st_size} bytes from {path}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "path": path
        }


@tool
def list_files(directory: str = "") -> Dict[str, Any]:
    """
    List files and directories in the agent workspace.
    
    Args:
        directory: Subdirectory to list (empty string for root workspace)
        
    Returns:
        Dictionary containing the directory listing
    """
    try:
        target_dir = WORKSPACE_DIR / directory if directory else WORKSPACE_DIR
        
        if not target_dir.exists():
            return {
                "success": False,
                "error": f"Directory {directory} does not exist",
                "directory": directory
            }
        
        items = []
        for item in target_dir.iterdir():
            item_info = {
                "name": item.name,
                "type": "directory" if item.is_dir() else "file",
                "path": str(item.relative_to(WORKSPACE_DIR)),
                "size_bytes": item.stat().st_size if item.is_file() else None,
                "modified_at": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
            }
            items.append(item_info)
        
        # Sort by type (directories first) then by name
        items.sort(key=lambda x: (x["type"] == "file", x["name"]))
        
        return {
            "success": True,
            "directory": directory or ".",
            "items": items,
            "total_items": len(items),
            "message": f"Listed {len(items)} items in {directory or 'workspace root'}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "directory": directory
        }


@tool
def edit_file(path: str, instructions: str) -> Dict[str, Any]:
    """
    Edit an existing file based on natural language instructions.
    
    Args:
        path: Relative path to the file to edit
        instructions: Natural language instructions for how to modify the file
        
    Returns:
        Dictionary confirming the edit operation
    """
    try:
        # First, read the existing file
        read_result = read_file(path)
        if not read_result["success"]:
            return read_result
        
        current_content = read_result["content"]
        
        # For now, we'll create a simple edit log
        # In a real implementation, this would use an LLM to apply the instructions
        edit_log = {
            "timestamp": datetime.now().isoformat(),
            "instructions": instructions,
            "original_size": len(current_content),
            "action": "edit_requested"
        }
        
        # Create a backup
        backup_path = f"{path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_result = write_file(backup_path, current_content)
        
        return {
            "success": True,
            "path": path,
            "backup_created": backup_result["success"],
            "backup_path": backup_path if backup_result["success"] else None,
            "edit_log": edit_log,
            "message": f"Edit instructions recorded for {path}. Backup created at {backup_path}",
            "note": "This is a placeholder - actual content editing would require LLM integration"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "path": path
        }


@tool
def get_workspace_info() -> Dict[str, Any]:
    """
    Get information about the agent workspace structure and organization.

    Returns:
        Dictionary containing workspace information and organization
    """
    try:
        workspace_info = {
            "workspace_path": str(WORKSPACE_DIR.absolute()),
            "total_size_bytes": 0,
            "organization": {
                "analysis/": "Crypto and DeFi analysis files",
                "reports/": "Comprehensive research reports",
                "research/": "Market research and data files",
                "plans/": "Task plans and project management",
                "data/": "Raw data and datasets",
                "temp/": "Temporary files and working documents"
            },
            "directory_stats": {}
        }

        # Calculate directory statistics
        for subdir in ["analysis", "reports", "research", "plans", "data", "temp"]:
            subdir_path = WORKSPACE_DIR / subdir
            if subdir_path.exists():
                files = list(subdir_path.glob("*"))
                total_size = sum(f.stat().st_size for f in files if f.is_file())
                workspace_info["directory_stats"][subdir] = {
                    "file_count": len([f for f in files if f.is_file()]),
                    "total_size_bytes": total_size
                }
                workspace_info["total_size_bytes"] += total_size

        return {
            "success": True,
            "workspace": workspace_info,
            "message": f"Workspace located at: {workspace_info['workspace_path']}"
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@tool
def delete_file(path: str) -> Dict[str, Any]:
    """
    Delete a file from the agent workspace.
    
    Args:
        path: Relative path to the file to delete
        
    Returns:
        Dictionary confirming the deletion
    """
    try:
        full_path = WORKSPACE_DIR / path
        
        if not full_path.exists():
            return {
                "success": False,
                "error": f"File {path} does not exist",
                "path": path
            }
        
        if full_path.is_dir():
            return {
                "success": False,
                "error": f"{path} is a directory, not a file",
                "path": path
            }
        
        file_size = full_path.stat().st_size
        full_path.unlink()
        
        return {
            "success": True,
            "path": path,
            "size_bytes": file_size,
            "message": f"Successfully deleted {path} ({file_size} bytes)"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "path": path
        }


# List of all file system tools
FILESYSTEM_TOOLS = [
    write_file,
    read_file,
    list_files,
    edit_file,
    delete_file,
    get_workspace_info
]
