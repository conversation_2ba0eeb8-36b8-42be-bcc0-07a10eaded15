"""
Enhanced Moral<PERSON> Tools - Integrates existing Moralis functionality with Deep Agent capabilities.
Maintains backward compatibility while adding advanced blockchain data analysis.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional, Union
import requests
import json
import os
from datetime import datetime


def get_moralis_tools():
    """Get enhanced Moralis tools with Deep Agent integration."""
    
    # Moralis API configuration
    MORALIS_API_KEY = os.getenv("MORALIS_API_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImI1YmVhMTc1LWU5MmEtNGU0ZS1hOTgxLWU3ZjJlNTk5NjYyMCIsIm9yZ0lkIjoiNDA1NzgzIiwidXNlcklkIjoiNDE2OTY2IiwidHlwZUlkIjoiZTYyZmNjOGMtMTZlMi00ZmY1LWJjMjItNzVlYTBhZTRjNDk2IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MjQ1MTYzOTcsImV4cCI6NDg4MDI3NjM5N30.73TV_J52D11itXTgHvc1wr_kavMbyHAcxCya1TNEoeI")
    MORALIS_BASE_URL = "https://deep-index.moralis.io/api/v2.2"
    SOLANA_BASE_URL = "https://solana-gateway.moralis.io"
    
    def get_headers():
        return {
            "X-API-Key": MORALIS_API_KEY,
            "Content-Type": "application/json"
        }
    
    def make_moralis_request(url: str, params: Dict = None, method: str = "GET") -> Dict[str, Any]:
        """Make a request to Moralis API with proper error handling."""
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=get_headers(), params=params or {})
            else:
                response = requests.post(url, headers=get_headers(), json=params or {})

            if response.status_code == 200:
                return {
                    "success": True,
                    "data": response.json(),
                    "timestamp": datetime.now().isoformat(),
                    "deep_agent_enhanced": True
                }
            else:
                return {
                    "success": False,
                    "error": f"API request failed with status {response.status_code}",
                    "message": response.text,
                    "status_code": response.status_code
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Request failed: {str(e)}"
            }
    
    @tool
    def get_wallet_token_balances(
        address: str,
        chain: str = "eth",
        exclude_spam: bool = True,
        exclude_unverified_contracts: bool = True
    ) -> Dict[str, Any]:
        """
        Get token balances for a wallet address with enhanced analysis.
        
        Args:
            address: Wallet address to analyze
            chain: Blockchain (eth, bsc, polygon, avalanche, fantom, cronos, palm, arbitrum)
            exclude_spam: Whether to exclude spam tokens
            exclude_unverified_contracts: Whether to exclude unverified contracts
            
        Returns:
            Enhanced wallet token balance data with Deep Agent analysis
        """
        url = f"{MORALIS_BASE_URL}/{address}/erc20"
        params = {
            "chain": chain,
            "exclude_spam": exclude_spam,
            "exclude_unverified_contracts": exclude_unverified_contracts
        }
        
        result = make_moralis_request(url, params)
        
        if result["success"]:
            # Enhanced analysis
            tokens = result["data"]
            
            # Calculate portfolio metrics
            total_tokens = len(tokens)
            verified_tokens = len([t for t in tokens if t.get("verified_contract", False)])
            
            # Add portfolio analysis
            result["portfolio_analysis"] = {
                "total_tokens": total_tokens,
                "verified_tokens": verified_tokens,
                "verification_rate": (verified_tokens / total_tokens * 100) if total_tokens > 0 else 0,
                "largest_holdings": sorted(tokens, key=lambda x: float(x.get("balance_formatted", 0)), reverse=True)[:5]
            }
            
            result["function_call"] = {
                "name": "get_wallet_token_balances",
                "arguments": {"address": address, "chain": chain}
            }
        
        return result
    
    @tool
    def get_token_metadata(
        addresses: Union[str, List[str]],
        chain: str = "eth"
    ) -> Dict[str, Any]:
        """
        Get comprehensive metadata for one or more tokens.
        
        Args:
            addresses: Token contract address(es)
            chain: Blockchain network
            
        Returns:
            Enhanced token metadata with Deep Agent insights
        """
        if isinstance(addresses, str):
            addresses = [addresses]
        
        url = f"{MORALIS_BASE_URL}/erc20/metadata"
        params = {
            "chain": chain,
            "addresses": addresses
        }
        
        result = make_moralis_request(url, params)
        
        if result["success"]:
            tokens = result["data"]
            
            # Enhanced metadata analysis
            result["metadata_analysis"] = {
                "tokens_analyzed": len(tokens),
                "verified_contracts": len([t for t in tokens if t.get("verified_contract", False)]),
                "unique_symbols": len(set(t.get("symbol", "") for t in tokens)),
                "contract_types": list(set(t.get("contract_type", "") for t in tokens))
            }
            
            result["function_call"] = {
                "name": "get_token_metadata",
                "arguments": {"addresses": addresses, "chain": chain}
            }
        
        return result
    
    @tool
    def get_token_price(
        address: str,
        chain: str = "eth",
        exchange: Optional[str] = None,
        to_block: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get current token price with enhanced market analysis.
        
        Args:
            address: Token contract address
            chain: Blockchain network
            exchange: Specific exchange to query
            to_block: Block number for historical price
            
        Returns:
            Enhanced token price data with market insights
        """
        url = f"{MORALIS_BASE_URL}/erc20/{address}/price"
        params = {
            "chain": chain,
            "exchange": exchange,
            "to_block": to_block
        }
        
        result = make_moralis_request(url, params)
        
        if result["success"]:
            price_data = result["data"]
            
            # Enhanced price analysis
            usd_price = float(price_data.get("usdPrice", 0))
            
            result["price_analysis"] = {
                "usd_price": usd_price,
                "price_category": "micro" if usd_price < 0.01 else "small" if usd_price < 1 else "medium" if usd_price < 100 else "large",
                "exchange_used": price_data.get("exchangeName", "Unknown"),
                "price_source": "real_time" if not to_block else "historical",
                "last_updated": result["timestamp"]
            }
            
            result["function_call"] = {
                "name": "get_token_price",
                "arguments": {"address": address, "chain": chain}
            }
        
        return result
    
    @tool
    def get_wallet_nft_collections(
        address: str,
        chain: str = "eth",
        limit: int = 100,
        exclude_spam: bool = True
    ) -> Dict[str, Any]:
        """
        Get NFT collections owned by a wallet with enhanced analysis.
        
        Args:
            address: Wallet address
            chain: Blockchain network
            limit: Maximum number of collections to return
            exclude_spam: Whether to exclude spam NFTs
            
        Returns:
            Enhanced NFT collection data with Deep Agent insights
        """
        url = f"{MORALIS_BASE_URL}/{address}/nft/collections"
        params = {
            "chain": chain,
            "limit": limit,
            "exclude_spam": exclude_spam
        }
        
        result = make_moralis_request(url, params)
        
        if result["success"]:
            collections = result["data"].get("result", [])
            
            # Enhanced NFT analysis
            total_collections = len(collections)
            verified_collections = len([c for c in collections if c.get("verified_collection", False)])
            
            result["nft_analysis"] = {
                "total_collections": total_collections,
                "verified_collections": verified_collections,
                "verification_rate": (verified_collections / total_collections * 100) if total_collections > 0 else 0,
                "collection_diversity": len(set(c.get("contract_type", "") for c in collections)),
                "top_collections": sorted(collections, key=lambda x: int(x.get("amount", 0)), reverse=True)[:5]
            }
            
            result["function_call"] = {
                "name": "get_wallet_nft_collections",
                "arguments": {"address": address, "chain": chain}
            }
        
        return result
    
    @tool
    def get_defi_positions(
        address: str,
        chain: str = "eth",
        protocols: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get DeFi positions for a wallet address with enhanced analysis.
        
        Args:
            address: Wallet address
            chain: Blockchain network
            protocols: Specific protocols to query
            
        Returns:
            Enhanced DeFi position data with yield analysis
        """
        url = f"{MORALIS_BASE_URL}/wallets/{address}/defi/positions"
        params = {
            "chain": chain
        }
        
        if protocols:
            params["protocols"] = protocols
        
        result = make_moralis_request(url, params)
        
        if result["success"]:
            positions = result["data"]
            
            # Enhanced DeFi analysis
            total_positions = len(positions)
            total_value = sum(float(p.get("usd_value", 0)) for p in positions)
            
            protocol_distribution = {}
            for position in positions:
                protocol = position.get("protocol_name", "Unknown")
                protocol_distribution[protocol] = protocol_distribution.get(protocol, 0) + float(position.get("usd_value", 0))
            
            result["defi_analysis"] = {
                "total_positions": total_positions,
                "total_value_usd": total_value,
                "protocol_count": len(protocol_distribution),
                "protocol_distribution": protocol_distribution,
                "largest_position": max(positions, key=lambda x: float(x.get("usd_value", 0))) if positions else None,
                "diversification_score": len(protocol_distribution) / max(total_positions, 1) * 10
            }
            
            result["function_call"] = {
                "name": "get_defi_positions",
                "arguments": {"address": address, "chain": chain}
            }
        
        return result
    
    @tool
    def get_solana_token_balance(
        address: str,
        network: str = "mainnet"
    ) -> Dict[str, Any]:
        """
        Get Solana token balances with enhanced analysis.
        
        Args:
            address: Solana wallet address
            network: Solana network (mainnet, devnet, testnet)
            
        Returns:
            Enhanced Solana token balance data
        """
        url = f"{SOLANA_BASE_URL}/account/{network}/{address}/tokens"
        
        result = make_moralis_request(url)
        
        if result["success"]:
            tokens = result["data"]
            
            # Enhanced Solana analysis
            total_tokens = len(tokens)
            sol_balance = next((float(t.get("amount", 0)) for t in tokens if t.get("mint") == "So11111111111111111111111111111111111111112"), 0)
            
            result["solana_analysis"] = {
                "total_tokens": total_tokens,
                "sol_balance": sol_balance,
                "spl_tokens": total_tokens - (1 if sol_balance > 0 else 0),
                "network": network,
                "largest_holdings": sorted(tokens, key=lambda x: float(x.get("amount", 0)), reverse=True)[:5]
            }
            
            result["function_call"] = {
                "name": "get_solana_token_balance",
                "arguments": {"address": address, "network": network}
            }
        
        return result
    
    @tool
    def analyze_wallet_activity(
        address: str,
        chain: str = "eth",
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Analyze wallet activity patterns with Deep Agent insights.
        
        Args:
            address: Wallet address to analyze
            chain: Blockchain network
            days: Number of days to analyze
            
        Returns:
            Comprehensive wallet activity analysis
        """
        # Get transaction history
        url = f"{MORALIS_BASE_URL}/{address}"
        params = {
            "chain": chain,
            "limit": 100
        }
        
        result = make_moralis_request(url, params)
        
        if result["success"]:
            transactions = result["data"].get("result", [])
            
            # Analyze activity patterns
            total_transactions = len(transactions)
            unique_contracts = len(set(tx.get("to_address", "") for tx in transactions))
            
            # Calculate activity metrics
            activity_score = min(total_transactions / 10, 10)  # Scale 0-10
            diversity_score = min(unique_contracts / 5, 10)   # Scale 0-10
            
            result["activity_analysis"] = {
                "total_transactions": total_transactions,
                "unique_contracts": unique_contracts,
                "activity_score": round(activity_score, 1),
                "diversity_score": round(diversity_score, 1),
                "wallet_type": "active_trader" if activity_score > 7 else "moderate_user" if activity_score > 3 else "light_user",
                "analysis_period": f"{days} days",
                "last_activity": transactions[0].get("block_timestamp") if transactions else None
            }
            
            result["function_call"] = {
                "name": "analyze_wallet_activity",
                "arguments": {"address": address, "chain": chain, "days": days}
            }
        
        return result
    
    return [
        get_wallet_token_balances,
        get_token_metadata,
        get_token_price,
        get_wallet_nft_collections,
        get_defi_positions,
        get_solana_token_balance,
        analyze_wallet_activity
    ]
