"""
Cryptocurrency Domain Agent - Specialized agent for crypto analysis and DeFi operations.
Provides expert-level cryptocurrency analysis, token evaluation, and market insights.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import aiohttp


class CryptocurrencyAgent:
    """
    Specialized agent for cryptocurrency analysis and DeFi operations.
    Provides comprehensive token analysis, portfolio evaluation, and market insights.
    """

    def __init__(self, unified_agent):
        """Initialize cryptocurrency agent."""
        self.unified_agent = unified_agent
        self.name = "Cryptocurrency Analyst"
        self.description = "Expert in cryptocurrency analysis, DeFi protocols, and market research"
        
        # Analysis capabilities
        self.capabilities = [
            "token_fundamental_analysis",
            "technical_analysis", 
            "on_chain_analysis",
            "defi_protocol_evaluation",
            "portfolio_optimization",
            "risk_assessment",
            "market_trend_analysis",
            "yield_farming_opportunities"
        ]
        
        # Data sources and APIs
        self.data_sources = {
            "coingecko": "https://api.coingecko.com/api/v3",
            "dexscreener": "https://api.dexscreener.com/latest",
            "jupiter": "https://quote-api.jup.ag/v6",
            "pump_fun": "https://frontend-api-v3.pump.fun",
            "moralis": "https://deep-index-moralis.moralis.io/api/v2.2"
        }
        
        # Analysis templates
        self.analysis_templates = {
            "token_analysis": {
                "sections": [
                    "basic_information",
                    "market_data",
                    "technical_indicators",
                    "on_chain_metrics",
                    "risk_assessment",
                    "recommendations"
                ]
            },
            "portfolio_analysis": {
                "sections": [
                    "portfolio_overview",
                    "asset_allocation",
                    "performance_metrics",
                    "risk_analysis",
                    "optimization_suggestions"
                ]
            }
        }
    
    async def analyze_token(
        self,
        token_address: str,
        chain: str = "solana",
        analysis_depth: str = "comprehensive",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Perform comprehensive token analysis."""
        try:
            analysis_id = f"token_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Initialize analysis structure
            analysis = {
                "analysis_id": analysis_id,
                "token_address": token_address,
                "chain": chain,
                "analysis_depth": analysis_depth,
                "timestamp": datetime.now().isoformat(),
                "status": "in_progress",
                "sections": {}
            }
            
            # Create widget to display token chart
            if hasattr(self.unified_agent, 'widget_coordinator'):
                widget_id = await self.unified_agent.widget_coordinator.create_widget(
                    widget_type="token_chart",
                    thread_id=thread_id or "default",
                    initial_data={
                        "token_address": token_address,
                        "chain": chain,
                        "chart_provider": "gmgn" if chain == "solana" else "dexscreener"
                    }
                )
                analysis["widget_id"] = widget_id
            
            # Gather basic token information
            basic_info = await self._get_basic_token_info(token_address, chain)
            analysis["sections"]["basic_information"] = basic_info
            
            # Get market data
            market_data = await self._get_market_data(token_address, chain)
            analysis["sections"]["market_data"] = market_data
            
            # Perform technical analysis if comprehensive
            if analysis_depth in ["standard", "comprehensive"]:
                technical_analysis = await self._perform_technical_analysis(token_address, chain)
                analysis["sections"]["technical_analysis"] = technical_analysis
            
            # Perform on-chain analysis if comprehensive
            if analysis_depth == "comprehensive":
                onchain_analysis = await self._perform_onchain_analysis(token_address, chain)
                analysis["sections"]["onchain_analysis"] = onchain_analysis
            
            # Generate risk assessment
            risk_assessment = await self._assess_token_risk(analysis["sections"])
            analysis["sections"]["risk_assessment"] = risk_assessment
            
            # Generate recommendations
            recommendations = await self._generate_token_recommendations(analysis["sections"])
            analysis["sections"]["recommendations"] = recommendations
            
            analysis["status"] = "completed"
            
            # Save analysis report
            if hasattr(self.unified_agent, 'file_system_manager'):
                await self.unified_agent.file_system_manager.write_file(
                    path=f"analysis/crypto/token_analysis_{analysis_id}.json",
                    content=analysis
                )
            
            return {
                "success": True,
                "analysis": analysis,
                "summary": self._generate_analysis_summary(analysis),
                "widget_created": "widget_id" in analysis
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to analyze token {token_address}: {str(e)}"
            }
    
    async def portfolio_analysis(
        self,
        wallet_address: str,
        chain: str = "solana",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze a cryptocurrency portfolio."""
        try:
            analysis_id = f"portfolio_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Get portfolio data
            portfolio_data = await self._get_portfolio_data(wallet_address, chain)
            
            if not portfolio_data.get("success"):
                return {
                    "success": False,
                    "error": "Failed to retrieve portfolio data",
                    "message": f"Could not analyze portfolio for {wallet_address}"
                }
            
            # Analyze portfolio composition
            composition_analysis = await self._analyze_portfolio_composition(portfolio_data["holdings"])
            
            # Calculate performance metrics
            performance_metrics = await self._calculate_portfolio_performance(portfolio_data["holdings"])
            
            # Assess portfolio risk
            risk_analysis = await self._assess_portfolio_risk(portfolio_data["holdings"])
            
            # Generate optimization suggestions
            optimization_suggestions = await self._generate_portfolio_optimization(
                portfolio_data["holdings"],
                composition_analysis,
                risk_analysis
            )
            
            portfolio_analysis = {
                "analysis_id": analysis_id,
                "wallet_address": wallet_address,
                "chain": chain,
                "timestamp": datetime.now().isoformat(),
                "portfolio_overview": {
                    "total_value_usd": portfolio_data.get("total_value_usd", 0),
                    "token_count": len(portfolio_data.get("holdings", [])),
                    "largest_holding": composition_analysis.get("largest_holding"),
                    "diversification_score": composition_analysis.get("diversification_score")
                },
                "composition_analysis": composition_analysis,
                "performance_metrics": performance_metrics,
                "risk_analysis": risk_analysis,
                "optimization_suggestions": optimization_suggestions
            }
            
            # Save analysis
            if hasattr(self.unified_agent, 'file_system_manager'):
                await self.unified_agent.file_system_manager.write_file(
                    path=f"analysis/crypto/portfolio_analysis_{analysis_id}.json",
                    content=portfolio_analysis
                )
            
            return {
                "success": True,
                "analysis": portfolio_analysis,
                "summary": self._generate_portfolio_summary(portfolio_analysis)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to analyze portfolio {wallet_address}: {str(e)}"
            }
    
    async def analyze_pumpfun_trends(
        self,
        category: str = "new",
        limit: int = 20,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze trending tokens on PumpFun."""
        try:
            # Create PumpFun widget
            if hasattr(self.unified_agent, 'widget_coordinator'):
                widget_id = await self.unified_agent.widget_coordinator.create_widget(
                    widget_type="pumpfun",
                    thread_id=thread_id or "default",
                    initial_data={"category": category}
                )
            
            # Get trending tokens data
            trending_data = await self._get_pumpfun_trending(category, limit)
            
            if not trending_data.get("success"):
                return {
                    "success": False,
                    "error": "Failed to retrieve PumpFun data",
                    "message": f"Could not analyze {category} tokens on PumpFun"
                }
            
            # Analyze trends
            trend_analysis = await self._analyze_token_trends(trending_data["tokens"])
            
            # Generate insights
            insights = await self._generate_trend_insights(trend_analysis, category)
            
            analysis = {
                "category": category,
                "timestamp": datetime.now().isoformat(),
                "token_count": len(trending_data["tokens"]),
                "trend_analysis": trend_analysis,
                "insights": insights,
                "top_performers": trend_analysis.get("top_performers", []),
                "risk_warnings": trend_analysis.get("risk_warnings", [])
            }
            
            return {
                "success": True,
                "analysis": analysis,
                "widget_created": True,
                "summary": f"Analyzed {len(trending_data['tokens'])} {category} tokens on PumpFun"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to analyze PumpFun trends: {str(e)}"
            }
    
    async def _get_basic_token_info(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Get basic token information."""
        # Simulate token info retrieval
        return {
            "name": "Sample Token",
            "symbol": "SAMPLE",
            "address": token_address,
            "chain": chain,
            "decimals": 9,
            "total_supply": "1000000000",
            "description": "Sample token for analysis",
            "website": "https://example.com",
            "social_links": {
                "twitter": "https://twitter.com/sample",
                "telegram": "https://t.me/sample"
            }
        }
    
    async def _get_market_data(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Get current market data for token."""
        # Simulate market data
        return {
            "price_usd": 0.001234,
            "market_cap": 1234567,
            "volume_24h": 123456,
            "price_change_24h": 5.67,
            "price_change_7d": -2.34,
            "liquidity": 500000,
            "holders": 1500,
            "last_updated": datetime.now().isoformat()
        }
    
    async def _perform_technical_analysis(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Perform technical analysis on token price data."""
        return {
            "trend": "bullish",
            "support_levels": [0.001100, 0.001000],
            "resistance_levels": [0.001400, 0.001500],
            "rsi": 65.4,
            "moving_averages": {
                "ma_20": 0.001180,
                "ma_50": 0.001150,
                "ma_200": 0.001100
            },
            "volume_analysis": "above_average",
            "momentum": "positive"
        }
    
    async def _perform_onchain_analysis(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Perform on-chain analysis."""
        return {
            "holder_distribution": {
                "top_10_percentage": 45.2,
                "whale_concentration": "moderate"
            },
            "transaction_analysis": {
                "avg_transaction_size": 1000,
                "transaction_frequency": "high",
                "unique_traders_24h": 250
            },
            "liquidity_analysis": {
                "liquidity_depth": "good",
                "price_impact_1_percent": 0.05
            }
        }
    
    async def _assess_token_risk(self, sections: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall token risk."""
        return {
            "overall_risk": "medium",
            "risk_factors": [
                "High volatility",
                "Limited liquidity",
                "New project"
            ],
            "risk_score": 6.5,
            "risk_mitigation": [
                "Start with small position",
                "Monitor liquidity closely",
                "Set stop losses"
            ]
        }
    
    async def _generate_token_recommendations(self, sections: Dict[str, Any]) -> Dict[str, Any]:
        """Generate investment recommendations."""
        return {
            "recommendation": "HOLD",
            "confidence": 7.5,
            "target_price": 0.0015,
            "stop_loss": 0.0010,
            "position_size": "2-3% of portfolio",
            "time_horizon": "2-4 weeks",
            "key_catalysts": [
                "Upcoming product launch",
                "Partnership announcements",
                "Market sentiment improvement"
            ]
        }
    
    def _generate_analysis_summary(self, analysis: Dict[str, Any]) -> str:
        """Generate a human-readable summary of the analysis."""
        basic_info = analysis["sections"].get("basic_information", {})
        market_data = analysis["sections"].get("market_data", {})
        recommendations = analysis["sections"].get("recommendations", {})
        
        price_usd = market_data.get('price_usd', 0)
        price_change = market_data.get('price_change_24h', 0)
        market_cap = market_data.get('market_cap', 0)
        target_price = recommendations.get('target_price', 0)
        risk_level = analysis['sections'].get('risk_assessment', {}).get('overall_risk', 'Unknown')

        return f"""
Token Analysis Summary for {basic_info.get('name', 'Unknown')} ({basic_info.get('symbol', 'N/A')}):

💰 Current Price: ${price_usd:.6f}
📈 24h Change: {price_change:+.2f}%
💎 Market Cap: ${market_cap:,.0f}
📊 Recommendation: {recommendations.get('recommendation', 'N/A')}
🎯 Target Price: ${target_price:.6f}
⚠️ Risk Level: {risk_level}

Key insights and detailed analysis have been saved to your workspace.
        """.strip()
    
    def _generate_portfolio_summary(self, analysis: Dict[str, Any]) -> str:
        """Generate a human-readable portfolio summary."""
        overview = analysis.get("portfolio_overview", {})
        
        total_value = overview.get('total_value_usd', 0)
        diversification_score = overview.get('diversification_score', 0)

        return f"""
Portfolio Analysis Summary:

💰 Total Value: ${total_value:,.2f}
🪙 Token Count: {overview.get('token_count', 0)}
📊 Diversification Score: {diversification_score:.1f}/10
🏆 Largest Holding: {overview.get('largest_holding', 'N/A')}

Detailed analysis and optimization suggestions have been saved to your workspace.
        """.strip()
    
    async def _get_portfolio_data(self, wallet_address: str, chain: str) -> Dict[str, Any]:
        """Get portfolio data for wallet."""
        # Simulate portfolio data
        return {
            "success": True,
            "total_value_usd": 12345.67,
            "holdings": [
                {"symbol": "SOL", "amount": 10.5, "value_usd": 2100},
                {"symbol": "USDC", "amount": 5000, "value_usd": 5000},
                {"symbol": "RAY", "amount": 1000, "value_usd": 1500}
            ]
        }
    
    async def _analyze_portfolio_composition(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze portfolio composition."""
        return {
            "diversification_score": 7.5,
            "largest_holding": "USDC (40.5%)",
            "asset_allocation": {
                "stablecoins": 40.5,
                "major_tokens": 35.0,
                "altcoins": 24.5
            }
        }
    
    async def _calculate_portfolio_performance(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate portfolio performance metrics."""
        return {
            "total_return_24h": 2.34,
            "total_return_7d": -1.23,
            "total_return_30d": 15.67,
            "sharpe_ratio": 1.45,
            "max_drawdown": -8.9
        }
    
    async def _assess_portfolio_risk(self, holdings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess portfolio risk."""
        return {
            "risk_score": 6.5,
            "volatility": "moderate",
            "correlation_risk": "low",
            "liquidity_risk": "low"
        }
    
    async def _generate_portfolio_optimization(
        self,
        holdings: List[Dict[str, Any]],
        composition: Dict[str, Any],
        risk: Dict[str, Any]
    ) -> List[str]:
        """Generate portfolio optimization suggestions."""
        return [
            "Consider reducing stablecoin allocation to 30%",
            "Add exposure to DeFi protocols for yield generation",
            "Implement stop-loss orders for volatile positions",
            "Rebalance monthly to maintain target allocation"
        ]
    
    async def _get_pumpfun_trending(self, category: str, limit: int) -> Dict[str, Any]:
        """Get trending tokens from PumpFun."""
        # Simulate PumpFun data
        return {
            "success": True,
            "tokens": [
                {
                    "name": f"Token {i}",
                    "symbol": f"TK{i}",
                    "address": f"address_{i}",
                    "price": 0.001 * (i + 1),
                    "volume_24h": 10000 * (i + 1),
                    "price_change_24h": (-10 + i * 5)
                }
                for i in range(limit)
            ]
        }
    
    async def _analyze_token_trends(self, tokens: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze trends in token data."""
        return {
            "top_performers": tokens[:3],
            "average_volume": sum(t["volume_24h"] for t in tokens) / len(tokens),
            "bullish_tokens": len([t for t in tokens if t["price_change_24h"] > 0]),
            "risk_warnings": [
                "High volatility detected in several tokens",
                "Low liquidity warning for new tokens"
            ]
        }
    
    async def _generate_trend_insights(self, trend_analysis: Dict[str, Any], category: str) -> List[str]:
        """Generate insights from trend analysis."""
        return [
            f"Market sentiment for {category} tokens is mixed",
            f"{trend_analysis['bullish_tokens']} tokens showing positive momentum",
            "Exercise caution with new token launches",
            "Consider dollar-cost averaging for volatile positions"
        ]
