"""
Platform-specific tools for Chad GPT Deep Agents.
These tools provide integration with the React frontend and platform features.
"""

from langchain_core.tools import tool
from typing import Dict, List, Any, Optional
import json


def get_platform_tools(agent_instance):
    """Get platform-specific tools with agent instance binding."""
    
    @tool
    def create_widget(
        widget_type: str,
        thread_id: str,
        initial_data: Optional[str] = None,
        metadata: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new widget instance in the conversation.
        
        Args:
            widget_type: Type of widget to create (pumpfun, token_chart, jupiter, phantom, dexscreener)
            thread_id: Conversation thread identifier
            initial_data: JSON string of initial widget data
            metadata: JSON string of widget metadata
            
        Returns:
            Dictionary with widget creation result and widget_id
        """
        try:
            # Parse JSON data if provided
            data = json.loads(initial_data) if initial_data else {}
            meta = json.loads(metadata) if metadata else {}
            
            # Create widget through widget manager
            import asyncio
            widget_id = asyncio.run(agent_instance.widget_manager.create_widget(
                widget_type=widget_type,
                thread_id=thread_id,
                initial_data=data,
                metadata=meta
            ))
            
            return {
                "success": True,
                "widget_id": widget_id,
                "widget_type": widget_type,
                "message": f"Created {widget_type} widget with ID {widget_id}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create {widget_type} widget"
            }
    
    @tool
    def update_widget_data(
        widget_id: str,
        data: str,
        metadata: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update data for an existing widget.
        
        Args:
            widget_id: Unique identifier of the widget to update
            data: JSON string of new data to update
            metadata: JSON string of metadata to update
            
        Returns:
            Dictionary with update result
        """
        try:
            # Parse JSON data
            update_data = json.loads(data)
            update_meta = json.loads(metadata) if metadata else None
            
            # Update widget through widget manager
            import asyncio
            success = asyncio.run(agent_instance.widget_manager.update_widget(
                widget_id=widget_id,
                data=update_data,
                metadata=update_meta
            ))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "message": f"Updated widget {widget_id} successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Widget {widget_id} not found or inactive"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update widget {widget_id}"
            }
    
    @tool
    def get_widget_state(widget_id: str) -> Dict[str, Any]:
        """
        Get current state of a widget.
        
        Args:
            widget_id: Unique identifier of the widget
            
        Returns:
            Dictionary with widget state information
        """
        try:
            import asyncio
            state = asyncio.run(agent_instance.widget_manager.get_widget_state(widget_id))
            
            if state:
                return {
                    "success": True,
                    "widget_state": state
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Widget {widget_id} not found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get widget state for {widget_id}"
            }
    
    @tool
    def get_active_widgets(thread_id: str) -> Dict[str, Any]:
        """
        Get all active widgets for a conversation thread.
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            Dictionary with list of active widgets
        """
        try:
            import asyncio
            widgets = asyncio.run(agent_instance.widget_manager.get_active_widgets(thread_id))
            
            return {
                "success": True,
                "thread_id": thread_id,
                "active_widgets": widgets,
                "widget_count": len(widgets)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get active widgets for thread {thread_id}"
            }
    
    @tool
    def close_widget(widget_id: str) -> Dict[str, Any]:
        """
        Close and remove a widget from the conversation.
        
        Args:
            widget_id: Unique identifier of the widget to close
            
        Returns:
            Dictionary with close operation result
        """
        try:
            import asyncio
            success = asyncio.run(agent_instance.widget_manager.close_widget(widget_id))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "message": f"Closed widget {widget_id} successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Widget {widget_id} not found or already closed"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to close widget {widget_id}"
            }
    
    @tool
    def switch_ai_model(model_name: str) -> Dict[str, Any]:
        """
        Switch the active AI model for the conversation.
        
        Args:
            model_name: Name of the model to switch to (e.g., 'anthropic/claude-3.5-sonnet')
            
        Returns:
            Dictionary with model switch result
        """
        try:
            import asyncio
            asyncio.run(agent_instance.switch_model(model_name))
            
            return {
                "success": True,
                "new_model": model_name,
                "message": f"Switched to model {model_name} successfully"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to switch to model {model_name}"
            }
    
    @tool
    def get_conversation_state(thread_id: str) -> Dict[str, Any]:
        """
        Get current conversation state for a thread.
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            Dictionary with conversation state information
        """
        try:
            import asyncio
            state = asyncio.run(agent_instance.get_conversation_state(thread_id))
            
            return {
                "success": True,
                "conversation_state": state
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get conversation state for thread {thread_id}"
            }
    
    @tool
    def update_user_preferences(
        user_id: str,
        preferences: str
    ) -> Dict[str, Any]:
        """
        Update user preferences for the platform.
        
        Args:
            user_id: Unique identifier of the user
            preferences: JSON string of preferences to update
            
        Returns:
            Dictionary with update result
        """
        try:
            # Parse preferences JSON
            prefs = json.loads(preferences)
            
            import asyncio
            asyncio.run(agent_instance.update_user_preferences(user_id, prefs))
            
            return {
                "success": True,
                "user_id": user_id,
                "message": "User preferences updated successfully"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update preferences for user {user_id}"
            }
    
    @tool
    def broadcast_to_thread(
        thread_id: str,
        message: str
    ) -> Dict[str, Any]:
        """
        Broadcast a message to all widgets in a conversation thread.
        
        Args:
            thread_id: Conversation thread identifier
            message: JSON string of message to broadcast
            
        Returns:
            Dictionary with broadcast result
        """
        try:
            # Parse message JSON
            msg = json.loads(message)
            
            import asyncio
            asyncio.run(agent_instance.widget_manager.broadcast_to_thread(thread_id, msg))
            
            return {
                "success": True,
                "thread_id": thread_id,
                "message": "Message broadcasted to all widgets in thread"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to broadcast message to thread {thread_id}"
            }
    
    @tool
    def get_widget_capabilities(widget_type: str) -> Dict[str, Any]:
        """
        Get capabilities and configuration for a widget type.
        
        Args:
            widget_type: Type of widget to query capabilities for
            
        Returns:
            Dictionary with widget capabilities and configuration
        """
        try:
            import asyncio
            capabilities = asyncio.run(agent_instance.widget_manager.get_widget_capabilities(widget_type))
            
            return {
                "success": True,
                "widget_type": widget_type,
                "capabilities": capabilities
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get capabilities for widget type {widget_type}"
            }
    
    return [
        create_widget,
        update_widget_data,
        get_widget_state,
        get_active_widgets,
        close_widget,
        switch_ai_model,
        get_conversation_state,
        update_user_preferences,
        broadcast_to_thread,
        get_widget_capabilities
    ]
