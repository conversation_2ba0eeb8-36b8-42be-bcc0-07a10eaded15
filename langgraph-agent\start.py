#!/usr/bin/env python3
"""
Startup script for Chad GPT LangGraph Agent.
"""

import subprocess
import sys
import os


def install_requirements():
    """Install Python requirements."""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        sys.exit(1)


def start_server():
    """Start the FastAPI server."""
    print("Starting Chad GPT LangGraph Agent server...")
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 Chad GPT LangGraph Agent Startup")
    print("=" * 40)
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("❌ .env file not found. Please create it with your OpenRouter API key.")
        sys.exit(1)
    
    # Install requirements if needed
    if "--install" in sys.argv or not os.path.exists("venv"):
        install_requirements()
    
    # Start the server
    start_server()
