/**
 * Chat History Bar Component
 * 
 * A slide-out chat history bar that appears on left edge hover and covers
 * the left sidebar, extending from off-screen and sliding over existing content.
 */

import React, { useState, useEffect } from 'react';
import { MessageSquare, Plus, Clock, Search, X, Archive } from 'lucide-react';
import clsx from 'clsx';
import type { ChatThread } from '../types';

interface ChatHistoryBarProps {
  currentThreadId: string | null;
  onThreadSelect: (threadId: string) => void;
  onNewChat: () => void;
  getChatThreads: () => ChatThread[];
}

export function ChatHistoryBar({
  currentThreadId,
  onThreadSelect,
  onNewChat,
  getChatThreads
}: ChatHistoryBarProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [hovering, setHovering] = useState(false);

  // Show/hide based on hover
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    if (hovering) {
      setIsVisible(true);
    } else {
      timeout = setTimeout(() => {
        setIsVisible(false);
      }, 300); // Delay before hiding
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [hovering]);

  // Filter threads based on search
  const filteredThreads = getChatThreads().filter(thread =>
    thread.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    thread.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group threads by date
  const groupedThreads = filteredThreads.reduce((groups, thread) => {
    const date = new Date(thread.lastActivity);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    let groupKey: string;
    if (date.toDateString() === today.toDateString()) {
      groupKey = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = 'Yesterday';
    } else if (date > new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)) {
      groupKey = 'This Week';
    } else if (date > new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)) {
      groupKey = 'This Month';
    } else {
      groupKey = 'Older';
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(thread);
    return groups;
  }, {} as Record<string, ChatThread[]>);

  return (
    <>
      {/* Hover trigger area - invisible strip on left edge */}
      <div
        className={clsx(
          "fixed left-0 top-0 w-6 h-full z-[9998] cursor-pointer transition-all duration-200",
          hovering ? "bg-[#22c55e]/5 border-r border-[#22c55e]/20" : "bg-transparent"
        )}
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
        title="Hover to show chat history"
      />

      {/* Chat History Bar - Covers the Layout's left sidebar */}
      <div
        className={clsx(
          "fixed left-0 top-0 h-full w-80 bg-[#0A0A0A] border-r border-[#181818] z-[9999] transition-transform duration-300 ease-out",
          "shadow-2xl shadow-black/50 backdrop-blur-sm",
          isVisible ? "translate-x-0" : "-translate-x-full"
        )}
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      >
        {/* Header */}
        <div className="p-4 border-b border-[#181818] bg-[#0A0A0A]">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <MessageSquare size={20} className="text-[#22c55e]" />
              <h2 className="text-lg font-semibold text-white">Chat History</h2>
            </div>
            <button
              onClick={() => setIsVisible(false)}
              className="p-1 text-[#666] hover:text-white transition-colors rounded"
            >
              <X size={16} />
            </button>
          </div>

          {/* New Chat Button */}
          <button
            onClick={() => {
              onNewChat();
              setIsVisible(false);
            }}
            className="w-full px-4 py-3 bg-[#22c55e] text-black rounded-lg font-medium hover:bg-[#16a34a] transition-all duration-200 hover:scale-105 active:scale-95 flex items-center justify-center gap-2"
          >
            <Plus size={16} />
            New Chat
          </button>

          {/* Search */}
          <div className="mt-3 relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#666]" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[#111] border border-[#222] rounded-lg text-white placeholder-[#666] focus:outline-none focus:border-[#22c55e]/50 transition-colors text-sm"
            />
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {Object.entries(groupedThreads).map(([groupName, threads]) => (
            <div key={groupName} className="p-4">
              {/* Group Header */}
              <div className="flex items-center gap-2 mb-3 px-1">
                <Clock size={12} className="text-[#666]" />
                <span className="text-xs font-medium text-[#666] uppercase tracking-wide">
                  {groupName}
                </span>
                <div className="flex-1 h-px bg-[#222]" />
              </div>

              {/* Threads in Group */}
              {threads.map((thread) => (
                <button
                  key={thread.id}
                  onClick={() => {
                    onThreadSelect(thread.id);
                    setIsVisible(false);
                  }}
                  className={clsx(
                    "w-full text-left p-3 rounded-lg mb-2 transition-all duration-200 group",
                    currentThreadId === thread.id
                      ? "bg-gradient-to-r from-[#22c55e]/10 to-transparent border border-[#22c55e]/20 text-white shadow-lg shadow-black/10"
                      : "bg-[#111] hover:bg-[#181818] text-[#888] hover:text-white hover:shadow-lg hover:shadow-black/10 hover:scale-105 active:scale-95"
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate mb-1">
                        {thread.title}
                      </div>
                      <div className="text-xs opacity-60 truncate mb-2">
                        {thread.lastMessage}
                      </div>
                      <div className="flex items-center gap-2 text-xs opacity-40">
                        <span>{new Date(thread.lastActivity).toLocaleDateString()}</span>
                        <span>•</span>
                        <span>{new Date(thread.lastActivity).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                      </div>
                    </div>
                    
                    {/* Thread indicator */}
                    <div className={clsx(
                      "w-2 h-2 rounded-full ml-2 mt-1 transition-colors",
                      currentThreadId === thread.id ? "bg-[#22c55e]" : "bg-[#333] group-hover:bg-[#666]"
                    )} />
                  </div>
                </button>
              ))}
            </div>
          ))}

          {/* Empty State */}
          {filteredThreads.length === 0 && (
            <div className="p-8 text-center">
              {searchQuery ? (
                <div className="text-[#666] text-sm">
                  <Search size={24} className="mx-auto mb-2 opacity-50" />
                  No conversations found for "{searchQuery}"
                </div>
              ) : (
                <div className="text-[#666] text-sm">
                  <Archive size={24} className="mx-auto mb-2 opacity-50" />
                  No chat history yet.<br />
                  Start a new conversation!
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-[#181818] bg-[#0A0A0A]">
          <div className="text-xs text-[#666] text-center">
            {filteredThreads.length} conversation{filteredThreads.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Backdrop when visible - only covers area to the right of the chat bar */}
      {isVisible && (
        <div
          className="fixed top-0 left-80 right-0 bottom-0 bg-black/10 z-[9997] backdrop-blur-sm"
          onClick={() => setIsVisible(false)}
        />
      )}
    </>
  );
}
