#!/bin/bash
set -e
cd ~/ap3x-pump-ingestor/AP3X-PumP2/api-server

# Kill any process on port 3000
sudo netstat -tlnp | grep :3000 || true
sudo pkill -f simple-server || true
pm2 delete ap3x-pump-ingestor || true

# Create package.json with ESM support
echo '{ "type": "module" }' > package.json

# Start the server
PORT=3000 pm2 start simple-server.js --name ap3x-pump-ingestor
pm2 save
sleep 3

# Check logs
pm2 logs ap3x-pump-ingestor --lines 10 --nostream

# Test API
echo "Testing API..."
curl -s http://localhost:3000/api/health | head -20
