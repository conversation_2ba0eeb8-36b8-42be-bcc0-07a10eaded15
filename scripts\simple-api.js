import express from 'express';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 4000;

app.use(cors());
app.use(express.json());

// Mock data for testing
const mockTokens = [
  {
    mint: "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr",
    name: "dogwifhat",
    symbol: "WIF",
    image_uri: "https://cf-ipfs.com/ipfs/QmNdE9qh5SfdLmSBqD6etHf6zRAUiPjuBHtKUjvQHboFbt",
    market_cap: **********,
    volume_24h: 150000000,
    holder_count: 125000,
    created_timestamp: Date.now() - 86400000
  },
  {
    mint: "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
    name: "<PERSON><PERSON>",
    symbol: "BONK",
    image_uri: "https://cf-ipfs.com/ipfs/QmcQbsMWAJHvvfQN8hxrnmZCpuHMf5R6NtzeNENDD6LdCZ",
    market_cap: **********,
    volume_24h: 89000000,
    holder_count: 98000,
    created_timestamp: Date.now() - 172800000
  }
];

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: Date.now() });
});

// Category endpoints
app.get('/api/pumpfun/category/:name', (req, res) => {
  const { name } = req.params;
  const { limit = 50 } = req.query;
  
  res.json({
    success: true,
    data: {
      tokens: mockTokens.slice(0, parseInt(limit))
    },
    message: `Mock data for ${name}`,
    timestamp: Date.now()
  });
});

// All categories
app.get('/api/pumpfun/categories', (req, res) => {
  const { limit = 50 } = req.query;
  const tokens = mockTokens.slice(0, parseInt(limit));
  
  res.json({
    success: true,
    data: {
      'for-you': tokens,
      'runners': tokens,
      'featured': tokens,
      'graduated': tokens
    },
    message: 'Mock categories data',
    timestamp: Date.now()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Mock API Server running on port ${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
});
