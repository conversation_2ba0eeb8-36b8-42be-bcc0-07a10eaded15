import { useState } from 'react';
import { ArrowLeft, ArrowRight, Repeat, Clock, Target, Search, ChevronDown, Wallet } from 'lucide-react';
import clsx from 'clsx';

type Tab = 'swap' | 'dca' | 'limits';

interface Token {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logoURI: string;
}

const DEMO_TOKENS: Token[] = [
  {
    symbol: 'SOL',
    name: '<PERSON><PERSON>',
    address: 'So11111111111111111111111111111111111111112',
    decimals: 9,
    logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png'
  },
  {
    symbol: 'USDC',
    name: 'USD Coin',
    address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    decimals: 6,
    logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png'
  },
  {
    symbol: 'CHAD',
    name: 'Chad GPT',
    address: 'EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA',
    decimals: 5,
    logoURI: 'https://pump.mypinata.cloud/ipfs/QmY2HUM8HMPytN2cJDr9XxpGDzoCmp9WNszFpeB8N15NR9?img-width=800&img-dpr=2&img-onerror=redirect'
  },
  {
    symbol: 'JUP',
    name: 'Jupiter',
    address: 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
    decimals: 6,
    logoURI: 'https://jup.ag/favicon.ico'
  }
];

export function JupiterWidget() {
  const [activeTab, setActiveTab] = useState<Tab>('swap');
  const [fromToken, setFromToken] = useState<Token>(DEMO_TOKENS[0]);
  const [toToken, setToToken] = useState<Token>(DEMO_TOKENS[1]);
  const [fromAmount, setFromAmount] = useState('1');
  const [showTokenSelect, setShowTokenSelect] = useState<'from' | 'to' | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredTokens = DEMO_TOKENS.filter(token =>
    token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
    token.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleTokenSelect = (token: Token) => {
    if (showTokenSelect === 'from') {
      setFromToken(token);
    } else {
      setToToken(token);
    }
    setShowTokenSelect(null);
  };

  const handleSwapTokens = () => {
    const temp = fromToken;
    setFromToken(toToken);
    setToToken(temp);
  };

  if (showTokenSelect) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={() => setShowTokenSelect(null)}
            className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 group-hover:translate-x-[-2px] transition-transform" />
          </button>
          <h2 className="text-lg font-semibold">Select Token</h2>
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-[#666] w-4 h-4" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-[#0A0A0A] rounded-xl py-3 pl-11 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-[#222] placeholder-[#666] transition-all duration-200"
            placeholder="Search by token name or symbol..."
          />
        </div>

        <div className="space-y-2">
          {filteredTokens.map((token) => (
            <button
              key={token.address}
              onClick={() => handleTokenSelect(token)}
              className="w-full bg-[#0A0A0A] rounded-xl p-4 hover:ring-1 hover:ring-[#22c55e] transition-all duration-200 group"
            >
              <div className="flex items-center gap-3">
                <img
                  src={token.logoURI}
                  alt={token.name}
                  className="w-8 h-8 rounded-full"
                />
                <div className="text-left">
                  <div className="font-medium">{token.symbol}</div>
                  <div className="text-sm text-[#666]">{token.name}</div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
      <div className="flex items-center gap-3 mb-6">
        <div className="relative">
          <img 
            src="https://jup.ag/favicon.ico" 
            alt="Jupiter" 
            className="w-8 h-8 rounded-lg"
          />
        </div>
        <h2 className="text-lg font-semibold">Jupiter Terminal</h2>
        <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">DEMO</span>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-2 p-1 bg-[#0A0A0A] rounded-lg mb-6">
        <button
          onClick={() => setActiveTab('swap')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'swap' ? "bg-[#22c55e] text-black" : "text-[#666] hover:text-white"
          )}
        >
          <Repeat className="w-4 h-4 inline-block mr-2" />
          Swap
        </button>
        <button
          onClick={() => setActiveTab('dca')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'dca' ? "bg-[#22c55e] text-black" : "text-[#666] hover:text-white"
          )}
        >
          <Clock className="w-4 h-4 inline-block mr-2" />
          DCA
        </button>
        <button
          onClick={() => setActiveTab('limits')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'limits' ? "bg-[#22c55e] text-black" : "text-[#666] hover:text-white"
          )}
        >
          <Target className="w-4 h-4 inline-block mr-2" />
          Limits
        </button>
      </div>

      {/* Swap Interface */}
      <div className="space-y-3">
        {/* From Token */}
        <div className="bg-[#0A0A0A] rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-[#666]">From</span>
            <span className="text-sm text-[#666]">Balance: 0.00</span>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowTokenSelect('from')}
              className="flex items-center gap-2 bg-[#111] rounded-lg px-3 py-2 hover:ring-1 hover:ring-[#22c55e] transition-all duration-200"
            >
              <img
                src={fromToken.logoURI}
                alt={fromToken.name}
                className="w-6 h-6 rounded-full"
              />
              <span className="font-medium">{fromToken.symbol}</span>
              <ChevronDown size={16} className="text-[#666]" />
            </button>
            <input
              type="text"
              value={fromAmount}
              onChange={(e) => setFromAmount(e.target.value)}
              className="flex-1 bg-transparent text-right text-xl font-medium focus:outline-none"
              placeholder="0.00"
            />
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <button
            onClick={handleSwapTokens}
            className="bg-[#111] p-2 rounded-lg hover:bg-[#181818] transition-all duration-200"
          >
            <ArrowRight size={16} className="text-[#666]" />
          </button>
        </div>

        {/* To Token */}
        <div className="bg-[#0A0A0A] rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-[#666]">To (Estimated)</span>
            <span className="text-sm text-[#666]">Balance: 0.00</span>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowTokenSelect('to')}
              className="flex items-center gap-2 bg-[#111] rounded-lg px-3 py-2 hover:ring-1 hover:ring-[#22c55e] transition-all duration-200"
            >
              <img
                src={toToken.logoURI}
                alt={toToken.name}
                className="w-6 h-6 rounded-full"
              />
              <span className="font-medium">{toToken.symbol}</span>
              <ChevronDown size={16} className="text-[#666]" />
            </button>
            <input
              type="text"
              value="0.00"
              disabled
              className="flex-1 bg-transparent text-right text-xl font-medium text-[#666]"
              placeholder="0.00"
            />
          </div>
        </div>

        {/* Route Info */}
        <div className="bg-[#0A0A0A] rounded-xl p-4 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-[#666]">Price Impact</span>
            <span className="text-[#22c55e]">{"<0.01%"}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-[#666]">Minimum Received</span>
            <span>0.00 {toToken.symbol}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-[#666]">Route</span>
            <span>Jupiter Swap</span>
          </div>
        </div>

        {/* Connect Wallet Button */}
        <button className="w-full bg-[#22c55e] text-black rounded-xl py-3 font-medium hover:bg-[#22c55e]/90 transition-all duration-200 flex items-center justify-center gap-2">
          <Wallet size={16} />
          Connect Wallet
        </button>
      </div>
    </div>
  );
}