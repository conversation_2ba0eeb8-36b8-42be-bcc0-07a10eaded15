"""
Extended Moralis tools including Solana, DeFi, Analytics, and Discovery tools.
This file contains the remaining comprehensive Moralis API endpoints.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional
from moralis_tools import make_moralis_request, MORALIS_BASE_URL, SOLANA_BASE_URL

# =============================================================================
# EVM MARKET DATA & DISCOVERY TOOLS
# =============================================================================

@tool
def evm_gettoperc20tokensbymarketcap(chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """List the top ERC20 tokens ranked by market cap."""
    url = f"{MORALIS_BASE_URL}/market-data/erc20s/top-tokens"
    params = {"chain": chain, "limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["chain"] = chain
    return result

@tool
def evm_gettoperc20tokensbypricemovers(chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Get top ERC20 tokens by price movements (winners and losers)."""
    url = f"{MORALIS_BASE_URL}/market-data/erc20s/price-movers"
    params = {"chain": chain, "limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["chain"] = chain
    return result

@tool
def evm_gettopnftcollectionsbymarketcap(chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Get top NFT collections by their current market cap."""
    url = f"{MORALIS_BASE_URL}/market-data/nfts/top-collections"
    params = {"chain": chain, "limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["chain"] = chain
    return result

@tool
def evm_gethottestnftcollectionsbytradingvolume(chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Get NFT collections by their 24 hour trading volume."""
    url = f"{MORALIS_BASE_URL}/market-data/nfts/hottest-collections"
    params = {"chain": chain, "limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["chain"] = chain
    return result

@tool
def evm_gettopcryptocurrenciesbymarketcap(limit: int = 100) -> Dict[str, Any]:
    """Get cryptocurrencies by their market cap."""
    url = f"{MORALIS_BASE_URL}/market-data/global/market-cap"
    params = {"limit": limit}
    
    return make_moralis_request(url, params)

@tool
def evm_gettopcryptocurrenciesbytradingvolume(limit: int = 100) -> Dict[str, Any]:
    """Get cryptocurrencies with the highest 24 hour trading volume."""
    url = f"{MORALIS_BASE_URL}/market-data/global/volume"
    params = {"limit": limit}
    
    return make_moralis_request(url, params)

# =============================================================================
# EVM DEFI TOOLS
# =============================================================================

@tool
def evm_getdefisummary(wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Summarize a wallet's DeFi activity, including total USD value, unclaimed rewards and active protocols."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/defi/summary"
    params = {"chain": chain}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getdefipositionsbyprotocol(wallet_address: str, protocol: str, chain: str = "eth") -> Dict[str, Any]:
    """Fetch detailed DeFi positions for a given wallet and protocol."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/defi/{protocol}/positions"
    params = {"chain": chain}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["protocol"] = protocol
        result["chain"] = chain
    return result

@tool
def evm_getdefipositionssummary(wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Get a concise overview of a wallet's DeFi positions across all protocols."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/defi/positions"
    params = {"chain": chain}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

# =============================================================================
# EVM ANALYTICS TOOLS
# =============================================================================

@tool
def evm_getwalletactivechains(wallet_address: str, chains: Optional[List[str]] = None) -> Dict[str, Any]:
    """List the blockchain networks a wallet is active on, including their first and last seen timestamps."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/chains"
    params = {}
    if chains:
        params["chains"] = chains
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
    return result

@tool
def evm_getwalletstats(wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Retrieve key statistics for a wallet, such as total transaction count and activity."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/stats"
    params = {"chain": chain}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getnftcollectionstats(token_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Get summary stats by NFT collection including total NFT transfers, total tokens and total owners."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/stats"
    params = {"chain": chain}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnftfloorpricebycontract(token_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Get floor price for a given collection. Refreshes every 30 minutes."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/floor-price"
    params = {"chain": chain}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnfthistoricalfloorpricebycontract(token_address: str, chain: str = "eth", days: int = 30) -> Dict[str, Any]:
    """Get timeseries historical floor prices for a given NFT collection."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/floor-price/historical"
    params = {"chain": chain, "days": days}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
        result["days"] = days
    return result

# =============================================================================
# SOLANA TOOLS
# =============================================================================

@tool
def solana_getaggregatedtokenpairstats(token_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Get aggregated statistics across supported pairs of a token."""
    url = f"{SOLANA_BASE_URL}/token/{network}/{token_address}/pairs/stats"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["token_address"] = token_address
        result["network"] = network
    return result

@tool
def solana_getcandlesticks(pair_address: str, timeframe: str = "1h", network: str = "mainnet") -> Dict[str, Any]:
    """Gets the candlesticks for a specific pair address."""
    url = f"{SOLANA_BASE_URL}/pairs/{network}/{pair_address}/ohlcv"
    params = {"timeframe": timeframe}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["pair_address"] = pair_address
        result["network"] = network
        result["timeframe"] = timeframe
    return result

@tool
def solana_getnftmetadata(token_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Gets the contract level metadata (mint, standard, name, symbol, metaplex) for the given contract."""
    url = f"{SOLANA_BASE_URL}/nft/{network}/{token_address}/metadata"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["token_address"] = token_address
        result["network"] = network
    return result

@tool
def solana_getnfts(wallet_address: str, network: str = "mainnet", limit: int = 100) -> Dict[str, Any]:
    """Gets NFTs owned by the given address."""
    url = f"{SOLANA_BASE_URL}/account/{network}/{wallet_address}/nft"
    params = {"limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["network"] = network
    return result

@tool
def solana_getpairstats(pair_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Gets the stats for a specific pair address."""
    url = f"{SOLANA_BASE_URL}/pairs/{network}/{pair_address}/stats"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["pair_address"] = pair_address
        result["network"] = network
    return result

@tool
def solana_getportfolio(wallet_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Gets all the native and token balances of the given address."""
    url = f"{SOLANA_BASE_URL}/account/{network}/{wallet_address}/portfolio"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["network"] = network
    return result

@tool
def solana_getsnipersbypairaddress(pair_address: str, network: str = "mainnet", blocks_after_creation: int = 10) -> Dict[str, Any]:
    """Get all snipers."""
    url = f"{SOLANA_BASE_URL}/pairs/{network}/{pair_address}/snipers"
    params = {"blocks_after_creation": blocks_after_creation}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["pair_address"] = pair_address
        result["network"] = network
    return result

@tool
def solana_getspl(wallet_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Gets token balances owned by the given address."""
    url = f"{SOLANA_BASE_URL}/account/{network}/{wallet_address}/tokens"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["network"] = network
    return result

@tool
def solana_getswapsbypairaddress(pair_address: str, network: str = "mainnet", limit: int = 100) -> Dict[str, Any]:
    """Get all swap related transactions (buy, sell, add liquidity & remove liquidity) for a specific pair address."""
    url = f"{SOLANA_BASE_URL}/pairs/{network}/{pair_address}/trades"
    params = {"limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["pair_address"] = pair_address
        result["network"] = network
    return result

@tool
def solana_getswapsbytokenaddress(token_address: str, network: str = "mainnet", limit: int = 100) -> Dict[str, Any]:
    """Get all swap related transactions (buy, sell) for a specific token address."""
    url = f"{SOLANA_BASE_URL}/token/{network}/{token_address}/trades"
    params = {"limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["network"] = network
    return result

@tool
def solana_getswapsbywalletaddress(wallet_address: str, network: str = "mainnet", limit: int = 100) -> Dict[str, Any]:
    """Get all swap related transactions (buy, sell) for a specific wallet address."""
    url = f"{SOLANA_BASE_URL}/account/{network}/{wallet_address}/trades"
    params = {"limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["network"] = network
    return result

@tool
def solana_gettokenmetadata(token_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Get the global token metadata for a given network and contract (mint, standard, name, symbol, metaplex)."""
    url = f"{SOLANA_BASE_URL}/token/{network}/{token_address}/metadata"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["token_address"] = token_address
        result["network"] = network
    return result

@tool
def solana_gettokenpairs(token_address: str, network: str = "mainnet", limit: int = 100) -> Dict[str, Any]:
    """Get the supported pairs for a specific token address."""
    url = f"{SOLANA_BASE_URL}/token/{network}/{token_address}/pairs"
    params = {"limit": limit}
    
    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["network"] = network
    return result

@tool
def solana_gettokenprice(token_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Gets the token price (usd and native) for a given contract address and network."""
    url = f"{SOLANA_BASE_URL}/token/{network}/{token_address}/price"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["token_address"] = token_address
        result["network"] = network
    return result

@tool
def solana_getmultipletokenprices(token_addresses: List[str], network: str = "mainnet") -> Dict[str, Any]:
    """Gets the token price (usd and native) for multiple tokens."""
    url = f"{SOLANA_BASE_URL}/token/{network}/prices"
    params = {"token_addresses": token_addresses[:100]}  # Limit to 100
    
    return make_moralis_request(url, params, method="POST")

@tool
def solana_balance(wallet_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """Gets native balance owned by the given address."""
    url = f"{SOLANA_BASE_URL}/account/{network}/{wallet_address}/balance"
    
    result = make_moralis_request(url)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["network"] = network
    return result
