# Chad GPT Deep Agent Environment Configuration

# OpenAI Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration (Optional)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Groq Configuration (Optional)
GROQ_API_KEY=your_groq_api_key_here

# Mem0 Intelligent Memory Configuration
# Vector Database for Mem0 (Qdrant)
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=  # Optional for local Qdrant

# Server Configuration
HOST=localhost
PORT=8000
DEBUG=true

# Database Configuration
REDIS_URL=redis://localhost:6379
SQLITE_DB_PATH=./data/chad_gpt.db

# Blockchain API Configuration
MORALIS_API_KEY=your_moralis_api_key_here
WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/your_project_id

# Solana Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
HELIUS_API_KEY=your_helius_api_key_here

# Security
SECRET_KEY=your_secret_key_here
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
