# Chad GPT Deep Agent System Requirements
# Core framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-dotenv==1.0.0

# LangChain and LangGraph for agent orchestration
langchain==0.1.0
langchain-openai==0.0.2
langchain-core==0.1.0
langgraph==0.0.20
langchain-community==0.0.10

# Async and HTTP libraries
aiohttp==3.9.1
aiofiles==23.2.0
httpx==0.25.2
requests==2.31.0
websockets==12.0

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
python-dateutil==2.8.2

# Database and storage
redis==5.0.1
sqlite3  # Built-in Python module

# Intelligent Memory Layer
mem0ai==1.1.0

# Blockchain and crypto APIs
web3==6.12.0
solana==0.30.2
moralis==0.1.35

# File handling and utilities
pathlib  # Built-in Python module
json  # Built-in Python module
uuid  # Built-in Python module
hashlib  # Built-in Python module
mimetypes  # Built-in Python module
shutil  # Built-in Python module

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# Optional: Enhanced features
rich==13.7.0  # For better console output
typer==0.9.0  # For CLI interface
