/**
 * React Hook for Widget Context Management
 * 
 * Provides automatic widget registration, data synchronization,
 * and context sharing with the agent system.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { agentIntegration, WidgetContext } from '../agentIntegration';

export type WidgetType = 'pumpfun' | 'dexscreener' | 'phantom' | 'jupiter';

export interface UseWidgetContextOptions {
  widgetType: WidgetType;
  threadId: string;
  autoRegister?: boolean;
  syncInterval?: number;
}

export interface UseWidgetContextReturn {
  // Widget registration
  widgetId: string;
  isRegistered: boolean;
  register: () => void;
  deactivate: () => void;
  
  // Data management
  widgetData: Record<string, any>;
  updateData: (data: Record<string, any>) => void;
  setData: (data: Record<string, any>) => void;
  
  // Context sharing
  sendContextToAgent: () => Promise<void>;
  getActiveWidgets: () => WidgetContext[];
  
  // Status
  lastUpdated: Date | null;
  isActive: boolean;
}

export function useWidgetContext(options: UseWidgetContextOptions): UseWidgetContextReturn {
  const { 
    widgetType, 
    threadId, 
    autoRegister = true, 
    syncInterval = 5000 
  } = options;
  
  // Generate stable widget ID
  const widgetIdRef = useRef<string>(`${widgetType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const widgetId = widgetIdRef.current;
  
  // State management
  const [isRegistered, setIsRegistered] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [widgetData, setWidgetDataState] = useState<Record<string, any>>({});
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // Sync timer ref
  const syncTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Register widget with agent
  const register = useCallback(() => {
    if (isRegistered) return;
    
    try {
      agentIntegration.registerWidget({
        widgetId,
        widgetType,
        threadId,
        data: widgetData
      });
      
      setIsRegistered(true);
      setIsActive(true);
      setLastUpdated(new Date());
      
      console.log(`✅ Widget registered: ${widgetType} (${widgetId})`);
      
    } catch (error) {
      console.error('Failed to register widget:', error);
    }
  }, [widgetId, widgetType, threadId, widgetData, isRegistered]);
  
  // Deactivate widget
  const deactivate = useCallback(() => {
    if (!isRegistered) return;
    
    try {
      agentIntegration.deactivateWidget(widgetId);
      setIsActive(false);
      
      // Clear sync timer
      if (syncTimerRef.current) {
        clearInterval(syncTimerRef.current);
        syncTimerRef.current = null;
      }
      
      console.log(`🔄 Widget deactivated: ${widgetType} (${widgetId})`);
      
    } catch (error) {
      console.error('Failed to deactivate widget:', error);
    }
  }, [widgetId, widgetType, isRegistered]);
  
  // Update widget data (merge with existing)
  const updateData = useCallback((newData: Record<string, any>) => {
    setWidgetDataState(prevData => {
      const updatedData = { ...prevData, ...newData };
      
      // Send to agent if registered
      if (isRegistered && isActive) {
        agentIntegration.updateWidgetData(widgetId, newData);
      }
      
      setLastUpdated(new Date());
      return updatedData;
    });
  }, [widgetId, isRegistered, isActive]);
  
  // Set widget data (replace existing)
  const setData = useCallback((newData: Record<string, any>) => {
    setWidgetDataState(newData);
    
    // Send to agent if registered
    if (isRegistered && isActive) {
      agentIntegration.updateWidgetData(widgetId, newData);
    }
    
    setLastUpdated(new Date());
  }, [widgetId, isRegistered, isActive]);
  
  // Manually send context to agent
  const sendContextToAgent = useCallback(async () => {
    if (!isRegistered || !isActive) {
      throw new Error('Widget must be registered and active to send context');
    }
    
    try {
      agentIntegration.updateWidgetData(widgetId, widgetData);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to send widget context to agent:', error);
      throw error;
    }
  }, [widgetId, widgetData, isRegistered, isActive]);
  
  // Get all active widgets
  const getActiveWidgets = useCallback(() => {
    return agentIntegration.getActiveWidgets(threadId);
  }, [threadId]);
  
  // Auto-register on mount
  useEffect(() => {
    if (autoRegister) {
      register();
    }
    
    // Cleanup on unmount
    return () => {
      if (isRegistered) {
        deactivate();
      }
    };
  }, [autoRegister, register, deactivate, isRegistered]);
  
  // Setup periodic sync
  useEffect(() => {
    if (!isRegistered || !isActive || syncInterval <= 0) return;
    
    syncTimerRef.current = setInterval(() => {
      if (Object.keys(widgetData).length > 0) {
        agentIntegration.updateWidgetData(widgetId, widgetData);
        setLastUpdated(new Date());
      }
    }, syncInterval);
    
    return () => {
      if (syncTimerRef.current) {
        clearInterval(syncTimerRef.current);
        syncTimerRef.current = null;
      }
    };
  }, [widgetId, widgetData, isRegistered, isActive, syncInterval]);
  
  // Listen for widget events
  useEffect(() => {
    const handleWidgetRegistered = (widget: WidgetContext) => {
      if (widget.widgetId === widgetId) {
        setIsRegistered(true);
        setIsActive(true);
      }
    };
    
    const handleWidgetDeactivated = (widget: WidgetContext) => {
      if (widget.widgetId === widgetId) {
        setIsActive(false);
      }
    };
    
    agentIntegration.on('widgetRegistered', handleWidgetRegistered);
    agentIntegration.on('widgetDeactivated', handleWidgetDeactivated);
    
    return () => {
      agentIntegration.off('widgetRegistered', handleWidgetRegistered);
      agentIntegration.off('widgetDeactivated', handleWidgetDeactivated);
    };
  }, [widgetId]);
  
  return {
    // Widget registration
    widgetId,
    isRegistered,
    register,
    deactivate,
    
    // Data management
    widgetData,
    updateData,
    setData,
    
    // Context sharing
    sendContextToAgent,
    getActiveWidgets,
    
    // Status
    lastUpdated,
    isActive
  };
}

// ============================================================================
// SPECIALIZED WIDGET HOOKS
// ============================================================================

export function usePumpFunWidget(threadId: string, options: Partial<UseWidgetContextOptions> = {}) {
  return useWidgetContext({
    widgetType: 'pumpfun',
    threadId,
    ...options
  });
}

export function useDexScreenerWidget(threadId: string, options: Partial<UseWidgetContextOptions> = {}) {
  return useWidgetContext({
    widgetType: 'dexscreener',
    threadId,
    ...options
  });
}

export function usePhantomWidget(threadId: string, options: Partial<UseWidgetContextOptions> = {}) {
  return useWidgetContext({
    widgetType: 'phantom',
    threadId,
    ...options
  });
}

export function useJupiterWidget(threadId: string, options: Partial<UseWidgetContextOptions> = {}) {
  return useWidgetContext({
    widgetType: 'jupiter',
    threadId,
    ...options
  });
}
