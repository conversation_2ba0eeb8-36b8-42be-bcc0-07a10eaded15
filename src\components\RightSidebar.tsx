import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Activity, TrendingUp, Wallet, Settings, BarChart3, Zap, X, Rocket } from 'lucide-react';
import clsx from 'clsx';

interface RightSidebarProps {
  className?: string;
  onClose?: () => void;
  onCreateWidget?: (widgetType: string) => void;
}

export function RightSidebar({ className, onClose, onCreateWidget }: RightSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Debug: Log when component mounts and props
  React.useEffect(() => {
    console.log('🔧 RightSidebar mounted');
    console.log('🔧 RightSidebar onCreateWidget prop:', !!onCreateWidget);
  }, [onCreateWidget]);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleQuickAction = (action: string) => {
    console.log(`🔧 RightSidebar: Quick action clicked: ${action}`);
    console.log(`🔧 RightSidebar: onCreateWidget function available:`, !!onCreateWidget);

    // Map actions to widget types
    const widgetMap: { [key: string]: string } = {
      'wallet': 'phantom',
      'charts': 'dexscreener',
      'swap': 'jupiter',
      'tokens': 'pumpfun'
    };

    const widgetType = widgetMap[action];
    console.log(`🔧 RightSidebar: Mapped widget type: ${widgetType}`);

    if (widgetType && onCreateWidget) {
      console.log(`🔧 RightSidebar: Calling onCreateWidget with: ${widgetType}`);
      onCreateWidget(widgetType);
      console.log(`🔧 RightSidebar: onCreateWidget call completed`);
    } else {
      console.error(`🔧 RightSidebar: Cannot create widget - widgetType: ${widgetType}, onCreateWidget: ${!!onCreateWidget}`);
    }
  };

  return (
    <div className={clsx(
      'bg-[#0A0A0A] border-l border-[#181818] transition-all duration-300 ease-in-out flex flex-col',
      'hidden lg:flex', // Hide on mobile/tablet, show on desktop
      isCollapsed ? 'w-16' : 'w-80',
      className
    )}>
      {/* Header with toggle and close buttons */}
      <div className="flex items-center justify-between p-4 border-b border-[#181818]">
        {!isCollapsed && (
          <h2 className="text-white font-semibold text-lg">Analytics</h2>
        )}
        <div className="flex items-center gap-1">
          <button
            onClick={toggleSidebar}
            className="p-2 hover:bg-[#222] rounded-lg transition-colors duration-200 text-[#888] hover:text-white"
            aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
          </button>
          {!isCollapsed && onClose && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-[#222] rounded-lg transition-colors duration-200 text-[#888] hover:text-white"
              aria-label="Close sidebar"
            >
              <X size={18} />
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {!isCollapsed ? (
          <div className="p-4 space-y-6">
            {/* Market Overview */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-[#666] font-medium tracking-wide uppercase">
                <div className="w-1 h-1 bg-[#22c55e] rounded-full"></div>
                Market Overview
              </div>
              
              <div className="space-y-3">
                <div className="bg-[#111] rounded-lg p-4 hover:bg-[#181818] transition-all duration-200 hover:scale-105 cursor-pointer">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-[#888] text-sm">SOL Price</span>
                    <TrendingUp size={16} className="text-[#22c55e]" />
                  </div>
                  <div className="text-white font-semibold text-lg">$198.45</div>
                  <div className="text-[#22c55e] text-xs font-medium">+2.34% (24h)</div>
                </div>

                <div className="bg-[#111] rounded-lg p-4 hover:bg-[#181818] transition-all duration-200 hover:scale-105 cursor-pointer">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-[#888] text-sm">Market Cap</span>
                    <BarChart3 size={16} className="text-[#888]" />
                  </div>
                  <div className="text-white font-semibold text-lg">$94.2B</div>
                  <div className="text-[#888] text-xs">24h Volume: $2.1B</div>
                </div>
              </div>
            </div>

            {/* Active Positions */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-[#666] font-medium tracking-wide uppercase">
                <div className="w-1 h-1 bg-[#22c55e] rounded-full"></div>
                Active Positions
              </div>
              
              <div className="space-y-2">
                <div className="bg-[#111] rounded-lg p-3 hover:bg-[#181818] transition-colors">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-white text-sm font-medium">SOL</span>
                    <span className="text-[#22c55e] text-xs">+5.2%</span>
                  </div>
                  <div className="text-[#888] text-xs">12.5 SOL • $2,480</div>
                </div>

                <div className="bg-[#111] rounded-lg p-3 hover:bg-[#181818] transition-colors">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-white text-sm font-medium">USDC</span>
                    <span className="text-[#888] text-xs">0.0%</span>
                  </div>
                  <div className="text-[#888] text-xs">1,250 USDC • $1,250</div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-[#666] font-medium tracking-wide uppercase">
                <div className="w-1 h-1 bg-[#22c55e] rounded-full"></div>
                Recent Activity
              </div>
              
              <div className="space-y-2">
                <div className="bg-[#111] rounded-lg p-3 hover:bg-[#181818] transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-[#22c55e] rounded-lg flex items-center justify-center">
                      <Zap size={14} className="text-black" />
                    </div>
                    <div className="flex-1">
                      <div className="text-white text-sm">Swap Completed</div>
                      <div className="text-[#888] text-xs">1 SOL → 198 USDC</div>
                    </div>
                  </div>
                </div>

                <div className="bg-[#111] rounded-lg p-3 hover:bg-[#181818] transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-[#333] rounded-lg flex items-center justify-center">
                      <Activity size={14} className="text-[#888]" />
                    </div>
                    <div className="flex-1">
                      <div className="text-white text-sm">Token Analysis</div>
                      <div className="text-[#888] text-xs">BONK price alert</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-[#666] font-medium tracking-wide uppercase">
                <div className="w-1 h-1 bg-[#22c55e] rounded-full"></div>
                Quick Actions (TEST - Click buttons!)
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => {
                    console.log('🔧 PumpFun button clicked!');
                    handleQuickAction('tokens');
                  }}
                  className="bg-[#111] hover:bg-[#181818] rounded-lg p-3 transition-all duration-200 group hover:scale-105 active:scale-95"
                >
                  <Rocket size={16} className="text-[#888] group-hover:text-white mb-1" />
                  <div className="text-xs text-[#888] group-hover:text-white">PumpFun</div>
                </button>

                <button
                  onClick={() => {
                    console.log('🔧 Charts button clicked!');
                    handleQuickAction('charts');
                  }}
                  className="bg-[#111] hover:bg-[#181818] rounded-lg p-3 transition-all duration-200 group hover:scale-105 active:scale-95"
                >
                  <TrendingUp size={16} className="text-[#888] group-hover:text-white mb-1" />
                  <div className="text-xs text-[#888] group-hover:text-white">Charts</div>
                </button>

                <button
                  onClick={() => handleQuickAction('swap')}
                  className="bg-[#111] hover:bg-[#181818] rounded-lg p-3 transition-all duration-200 group hover:scale-105 active:scale-95"
                >
                  <Zap size={16} className="text-[#888] group-hover:text-white mb-1" />
                  <div className="text-xs text-[#888] group-hover:text-white">Swap</div>
                </button>

                <button
                  onClick={() => handleQuickAction('wallet')}
                  className="bg-[#111] hover:bg-[#181818] rounded-lg p-3 transition-all duration-200 group hover:scale-105 active:scale-95"
                >
                  <Wallet size={16} className="text-[#888] group-hover:text-white mb-1" />
                  <div className="text-xs text-[#888] group-hover:text-white">Wallet</div>
                </button>
              </div>
            </div>
          </div>
        ) : (
          // Collapsed state - show only icons
          <div className="p-2 space-y-4">
            <button className="w-full p-3 hover:bg-[#222] rounded-lg transition-colors group flex items-center justify-center">
              <TrendingUp size={20} className="text-[#888] group-hover:text-white" />
            </button>
            
            <button className="w-full p-3 hover:bg-[#222] rounded-lg transition-colors group flex items-center justify-center">
              <Wallet size={20} className="text-[#888] group-hover:text-white" />
            </button>
            
            <button className="w-full p-3 hover:bg-[#222] rounded-lg transition-colors group flex items-center justify-center">
              <Activity size={20} className="text-[#888] group-hover:text-white" />
            </button>
            
            <button className="w-full p-3 hover:bg-[#222] rounded-lg transition-colors group flex items-center justify-center">
              <Settings size={20} className="text-[#888] group-hover:text-white" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
