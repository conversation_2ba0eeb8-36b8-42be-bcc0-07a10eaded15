import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { RouterProvider } from 'react-router-dom'
import { router } from './router'
import { SettingsProvider } from './contexts/SettingsContext'
import { MessagesProvider } from './contexts/MessagesContext'
import { AlertProvider } from './contexts/AlertContext'
import { AlertSystem } from './components/AlertSystem'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SettingsProvider>
      <AlertProvider>
        <MessagesProvider>
          <RouterProvider router={router} />
          <AlertSystem />
        </MessagesProvider>
      </AlertProvider>
    </SettingsProvider>
  </React.StrictMode>
)