"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { PumpFunWidget } from '../../src/components/PumpFunWidget'

// NOTE: Archived on 2025-08-09. This experimental component wrapped PumpFunWidget with mock realtime data.
// It is kept here for reference but is not part of the active application. Restore by moving back to src/components.

interface Trade {
  mint: string
  name: string
  symbol: string
  image_uri: string
  usd_market_cap: number
  market_cap: number
  sol_amount: number
  is_buy: boolean
  user: string
  creator: string
  creator_username: string
  token_amount: number
  total_supply: number
  timestamp: number
  received_time?: number
  virtual_sol_reserves: number
  virtual_token_reserves: number
  signature: string
  created_timestamp?: number
  website?: string | null
  twitter?: string | null
  telegram?: string | null
  king_of_the_hill_timestamp?: number | null
  description?: string | null
  [key: string]: any
}

interface TokenData {
  mint: string
  name: string
  symbol: string
  image_uri: string
  usd_market_cap: number
  market_cap: number
  total_volume: number
  buy_volume: number
  sell_volume: number
  unique_traders: string[]
  unique_trader_count: number
  trades: Trade[]
  last_trade_time: number
  creator: string
  creator_username: string
  total_supply: number
  virtual_sol_reserves: number
  virtual_token_reserves: number
  buy_sell_ratio: number
  created_timestamp?: number
  website?: string | null
  twitter?: string | null
  telegram?: string | null
  king_of_the_hill_timestamp?: string | null
  description?: string | null
}

interface RealtimeTokenData {
  mint: string
  volume: number
  trader_count: number
  buy_sell_ratio: number
  last_trade_time: number
}

interface PumpFunWithRealtimeDataProps {
  isActive: boolean
  enableRealtimeIntegration?: boolean
  timeRange?: string
  solPrice?: number
  minTradeAmountFilter?: number
}

export function PumpFunWithRealtimeData({
  isActive,
  enableRealtimeIntegration = false,
  timeRange = '60',
  solPrice = 240,
  minTradeAmountFilter = 10
}: PumpFunWithRealtimeDataProps) {
  const [allTrades, setAllTrades] = useState<Trade[]>([])
  const [tokens, setTokens] = useState<Map<string, TokenData>>(new Map())

  const realtimeData = useMemo(() => {
    if (!enableRealtimeIntegration) return undefined
    const realtimeMap = new Map<string, RealtimeTokenData>()
    tokens.forEach((tokenData) => {
      realtimeMap.set(tokenData.mint, {
        mint: tokenData.mint,
        volume: tokenData.total_volume,
        trader_count: tokenData.unique_trader_count,
        buy_sell_ratio: tokenData.buy_sell_ratio,
        last_trade_time: tokenData.last_trade_time
      })
    })
    return realtimeMap
  }, [tokens, enableRealtimeIntegration])

  useEffect(() => {
    if (enableRealtimeIntegration && tokens.size === 0) {
      const mockTokens = new Map<string, TokenData>()
      mockTokens.set('mock-mint-1', {
        mint: 'mock-mint-1', name: 'Mock Token 1', symbol: 'MOCK1', image_uri: '',
        usd_market_cap: 1_000_000, market_cap: 1_000_000, total_volume: 50.5,
        buy_volume: 30.3, sell_volume: 20.2, unique_traders: ['t1','t2','t3'],
        unique_trader_count: 3, trades: [], last_trade_time: Date.now()/1000,
        creator: 'mock-creator', creator_username: 'MockCreator', total_supply: 1_000_000_000,
        virtual_sol_reserves: 30, virtual_token_reserves: 1_073_000_000, buy_sell_ratio: 0.6
      })
      setTokens(mockTokens)
    }
  }, [enableRealtimeIntegration, tokens.size])

  return (
    <div className="relative">
      <PumpFunWidget isActive={isActive} realtimeData={realtimeData} enableRealtimeIntegration={enableRealtimeIntegration} />
    </div>
  )
}

export default PumpFunWithRealtimeData

