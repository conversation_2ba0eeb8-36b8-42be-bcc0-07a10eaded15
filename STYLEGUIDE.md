# Chad GPT Style Guide

## Overview
This style guide defines the design system and styling rules for Chad GPT to maintain visual consistency across all components and features.

## Color Palette

### Primary Colors
- **Background**: `#000000` (Pure Black)
- **Surface**: `#0A0A0A` (Sidebar background)
- **Card/Panel**: `#111111` (`bg-[#111]`)
- **Elevated Surface**: `#181818` (`bg-[#181818]`)
- **Border**: `#181818` (`border-[#181818]`)
- **Accent**: `#22c55e` (Green - Primary action color)

### Text Colors
- **Primary Text**: `#ffffff` (White)
- **Secondary Text**: `#888888` (`text-[#888]`)
- **Muted Text**: `#666666` (`text-[#666]`)
- **Placeholder**: `#666666` with opacity transitions

### Semantic Colors
- **Success**: `#22c55e` (Green)
- **Error**: `#ef4444` (Red)
- **Warning**: `#f59e0b` (Yellow)
- **Info**: `#3b82f6` (Blue)

## Typography

### Font Family
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
```

### Font Weights
- **Regular**: `font-normal` (400)
- **Medium**: `font-medium` (500)
- **Semibold**: `font-semibold` (600)

### Font Sizes
- **Hero Text**: `text-[64px]` (Main welcome message)
- **Large**: `text-lg` (Modal titles)
- **Base**: `text-sm` (Default UI text)
- **Small**: `text-xs` (Labels, metadata)

### Text Styling
- **Letter Spacing**: `tracking-[-0.02em]` for hero text, `tracking-tight` for titles
- **Line Height**: `leading-relaxed` for content areas
- **Text Rendering**: `antialiased` for smooth rendering

## Layout & Spacing

### Container Widths
- **Sidebar**: `w-80` (320px fixed width)
- **Modal**: `max-w-md` (448px max width)
- **Content**: Fluid with `px-8` horizontal padding

### Padding Scale
- **Small**: `p-2` (8px)
- **Medium**: `p-4` (16px)
- **Large**: `p-6` (24px)
- **Extra Large**: `p-8` (32px)

### Margin Scale
- **Small**: `gap-2` (8px)
- **Medium**: `gap-4` (16px)
- **Large**: `gap-6` (24px)

### Border Radius
- **Small**: `rounded-lg` (8px) - Buttons, small cards
- **Medium**: `rounded-xl` (12px) - Avatars, medium cards
- **Large**: `rounded-2xl` (16px) - Main panels, modals

## Component Patterns

### Buttons

#### Primary Button (Active State)
```css
bg-white text-black hover:bg-gray-100
```

#### Secondary Button
```css
bg-[#222] hover:bg-[#333] text-white
```

#### Icon Button
```css
hover:bg-[#222] p-2 rounded-lg transition-all duration-200
```

#### Interactive Button with Scale
```css
hover:scale-105 active:scale-95 transition-all duration-200
```

### Cards & Panels

#### Main Card
```css
bg-[#111] rounded-2xl shadow-lg shadow-black/10
```

#### Interactive Card
```css
bg-[#111] hover:bg-[#181818] rounded-lg transition-all duration-200 
hover:shadow-lg hover:shadow-black/10 hover:scale-105 active:scale-95
```

#### Sidebar Panel
```css
bg-[#0A0A0A] border-r border-[#181818]
```

### Form Elements

#### Text Input/Textarea
```css
bg-[#111] rounded-2xl p-6 text-white resize-none 
focus:outline-none focus:ring-1 focus:ring-[#222] 
transition-shadow duration-200 shadow-lg shadow-black/10
```

#### Input Focus State
```css
border-color: rgba(34, 197, 94, 0.4);
box-shadow: 0 0 0 1px #22c55e;
background: #181818;
```

### Navigation

#### Active Navigation Item
```css
bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white 
shadow-lg shadow-black/10
```

#### Inactive Navigation Item
```css
text-[#888] hover:bg-[#111] hover:text-white 
hover:shadow-lg hover:shadow-black/10
```

### Avatars & Images

#### User Avatar
```css
rounded-xl object-cover shadow-sm shadow-black/20 
ring-1 ring-[#181818] hover:ring-[#22c55e] transition-colors
```

#### Assistant Avatar
```css
bg-gradient-to-br from-[#222] to-[#333] rounded-xl
```

#### User Message Avatar
```css
bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl
```

## Animations & Transitions

### Standard Transition
```css
transition-all duration-200
```

### Fade In Animation
```css
animate-fade-in
/* Keyframe: 0% opacity-0 translateY(8px) → 100% opacity-1 translateY(0) */
```

### Slide In Animation
```css
animate-slide-in
/* Keyframe: 0% opacity-0 translateX(-8px) → 100% opacity-1 translateX(0) */
```

### Scale Animations
```css
hover:scale-105 active:scale-95
```

### Timing Functions
- **Standard**: `cubic-bezier(0.2, 0, 0, 1)`
- **Duration**: `200ms` for interactions, `300ms` for fade-ins

## Shadows & Effects

### Standard Shadow
```css
shadow-lg shadow-black/10
```

### Elevated Shadow
```css
shadow-xl shadow-black/20
```

### Glow Effect (Focus/Hover)
```css
box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.2), 0 2px 4px rgba(34, 197, 94, 0.1);
```

## Z-Index Scale

### Layer Hierarchy
- **Base Content**: `z-0` to `z-10`
- **Dropdowns**: `z-20` to `z-30`
- **Sticky Elements**: `z-40` to `z-50`
- **Modals**: `z-[10000]` to `z-[10001]`

### Modal Layering
```css
/* Backdrop */
z-[10000]

/* Modal Content */
z-[10001]
```

## Responsive Design

### Breakpoints
- **Mobile**: Default (< 475px)
- **Small**: `xs:` (≥ 475px)
- **Tablet**: `md:` (≥ 768px)
- **Desktop**: `lg:` (≥ 1024px)

### Container Responsive Padding
```css
/* Mobile */
padding: 0 24px;

/* Desktop */
@media (min-width: 768px) {
  padding: 0 32px;
}
```

## Accessibility

### Focus States
```css
:focus-visible {
  outline: 2px solid #22c55e;
  outline-offset: 2px;
  border-radius: 12px;
}
```

### Text Selection
```css
::selection {
  background: rgba(34, 197, 94, 0.15);
  color: #fff;
}
```

### Disabled States
```css
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
```

## Scrollbar Styling

### Custom Scrollbar
```css
::-webkit-scrollbar {
  width: 4px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #222;
  border-radius: 2px;
  border: 1px solid #181818;
}

::-webkit-scrollbar-thumb:hover {
  background: #444;
}
```

## Specific Component Guidelines

### Section Headers
```css
/* Uppercase section labels with accent dot */
text-xs text-[#666] font-medium tracking-wide uppercase
flex items-center gap-2 pl-1

/* Accent dot */
w-1 h-1 bg-[#22c55e] rounded-full
```

### Chat Messages

#### Message Container
```css
/* User/Assistant message wrapper */
flex gap-4 p-4 animate-fade-in

/* Assistant message background */
bg-[#111] rounded-2xl shadow-lg shadow-black/10
```

#### Message Avatars
```css
/* Avatar container */
w-9 h-9 rounded-xl flex items-center justify-center flex-shrink-0
shadow-sm shadow-black/20

/* Assistant avatar gradient */
bg-gradient-to-br from-[#222] to-[#333]

/* User avatar gradient */
bg-gradient-to-br from-indigo-500 to-purple-500
```

### Widget Components

#### Widget Container
```css
/* Inactive widget opacity */
opacity-50

/* Widget grid layout */
grid grid-cols-4 gap-2
```

#### Widget Button
```css
p-3 rounded-lg bg-[#111] hover:bg-[#181818]
transition-all duration-200 hover:shadow-lg hover:shadow-black/10
group hover:scale-105 active:scale-95
```

### Input Area

#### Sticky Input Container
```css
relative p-8 bg-black sticky bottom-0
```

#### Main Textarea
```css
w-full bg-[#111] rounded-2xl p-6 text-white resize-none h-24
focus:outline-none focus:ring-1 focus:ring-[#222] text-sm leading-relaxed
transition-shadow duration-200 shadow-lg shadow-black/10
```

#### Input Controls
```css
/* Controls container */
absolute bottom-12 right-12 flex items-center gap-3 text-[#666]

/* Character counter */
text-sm

/* Submit button active state */
bg-white text-black hover:bg-gray-100

/* Submit button inactive state */
bg-[#222] disabled:opacity-50 disabled:cursor-not-allowed
```

### Alert System

#### Alert Container
```css
fixed bottom-8 right-8 z-50 space-y-4 max-w-md w-full
```

#### Alert Message
```css
p-4 rounded-xl shadow-lg animate-fade-in flex items-start gap-3
bg-[#111] border

/* Success variant */
border-[#22c55e] text-[#22c55e]

/* Error variant */
border-red-500 text-red-500

/* Info variant */
border-blue-500 text-blue-500

/* Warning variant */
border-yellow-500 text-yellow-500
```

### Modal Components

#### Modal Backdrop
```css
fixed inset-0 bg-black/50 flex items-center justify-center z-[10000]
```

#### Modal Content
```css
bg-[#111] rounded-2xl w-full max-w-md p-6 relative animate-fade-in
z-[10001] mx-4
```

#### Modal Header
```css
flex items-center justify-between mb-6
```

### Loading States

#### Loading Spinner Container
```css
flex items-center gap-2 text-[#666] animate-pulse p-4
```

#### Spinner Icon
```css
animate-spin
```

## Design Principles

### 1. Dark-First Design
- All components use dark backgrounds with light text
- Subtle gradients and shadows for depth
- High contrast for accessibility

### 2. Consistent Spacing
- Use the 8px grid system (multiples of 8px)
- Consistent gap patterns: `gap-2`, `gap-4`, `gap-6`
- Uniform padding: `p-2`, `p-4`, `p-6`, `p-8`

### 3. Smooth Interactions
- All interactive elements have hover/active states
- Consistent transition duration (200ms)
- Scale animations for tactile feedback

### 4. Semantic Color Usage
- Green (`#22c55e`) for primary actions and success
- Red for errors and destructive actions
- Blue for informational content
- Yellow for warnings

### 5. Typography Hierarchy
- Clear distinction between heading and body text
- Consistent font weights across similar elements
- Proper line height for readability

## Implementation Notes

### CSS Custom Properties
The design system relies on Tailwind CSS utility classes with custom extensions defined in `tailwind.config.js`.

### Animation Performance
All animations use `transform` and `opacity` properties for optimal performance, with `will-change` and `backface-visibility` optimizations.

### Component Composition
Components follow a consistent pattern of:
1. Container with proper spacing
2. Interactive states with hover/active feedback
3. Semantic color application
4. Consistent border radius and shadows

### Maintenance Guidelines
- Always use the defined color palette
- Maintain consistent spacing patterns
- Test all interactive states
- Ensure proper contrast ratios
- Validate responsive behavior across breakpoints
