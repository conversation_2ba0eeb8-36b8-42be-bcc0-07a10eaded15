"""
Intelligent Memory Manager using Mem0
Provides ChatGPT-level contextual awareness for Chad GPT
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

try:
    from mem0 import Memory
    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False
    logging.warning("Mem0 not available. Install with: pip install mem0ai")

logger = logging.getLogger(__name__)

class IntelligentMemoryManager:
    """
    Intelligent Memory Manager using Mem0 for persistent, contextual memory.
    Replaces manual context hints with AI-powered memory retrieval.
    """
    
    def __init__(self):
        self.memory = None
        self.enabled = False
        self._initialize_memory()
    
    def _initialize_memory(self):
        """Initialize Mem0 memory system"""
        if not MEM0_AVAILABLE:
            logger.warning("Mem0 not available - memory features disabled")
            return
        
        try:
            # Initialize Mem0 with configuration
            config = {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4o-mini",
                        "temperature": 0.1,
                        "max_tokens": 1500,
                    }
                },
                "embedder": {
                    "provider": "openai",
                    "config": {
                        "model": "text-embedding-3-small"
                    }
                },
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "collection_name": "chad_gpt_memory",
                        "host": "localhost",
                        "port": 6333,
                    }
                }
            }
            
            self.memory = Memory.from_config(config)
            self.enabled = True
            logger.info("✅ Mem0 Memory Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Mem0: {e}")
            logger.info("💡 Falling back to basic memory management")
            self.enabled = False
    
    async def add_conversation_memory(
        self, 
        user_message: str, 
        assistant_response: str, 
        thread_id: str,
        widget_context: Optional[Dict[str, Any]] = None
    ):
        """Add conversation turn to memory with intelligent extraction"""
        if not self.enabled:
            return
        
        try:
            # Create rich context for memory storage
            conversation_context = f"""
            User Message: {user_message}
            Assistant Response: {assistant_response}
            Thread ID: {thread_id}
            Timestamp: {datetime.now().isoformat()}
            """
            
            # Add widget context if available
            if widget_context:
                conversation_context += f"\nWidget Context: {json.dumps(widget_context, indent=2)}"
            
            # Store in Mem0 with user-specific memory
            await asyncio.to_thread(
                self.memory.add,
                conversation_context,
                user_id=thread_id,
                metadata={
                    "type": "conversation",
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "has_widget_context": widget_context is not None
                }
            )
            
            logger.info(f"💾 Added conversation memory for thread {thread_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to add conversation memory: {e}")
    
    async def add_widget_memory(
        self, 
        widget_type: str, 
        widget_data: Dict[str, Any], 
        thread_id: str
    ):
        """Add widget interaction to memory"""
        if not self.enabled:
            return
        
        try:
            # Create structured widget memory
            widget_memory = f"""
            Widget Type: {widget_type}
            Thread ID: {thread_id}
            Timestamp: {datetime.now().isoformat()}
            Widget Data: {json.dumps(widget_data, indent=2)}
            """
            
            # Store widget-specific memory
            await asyncio.to_thread(
                self.memory.add,
                widget_memory,
                user_id=thread_id,
                metadata={
                    "type": "widget_interaction",
                    "widget_type": widget_type,
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            logger.info(f"🎮 Added {widget_type} widget memory for thread {thread_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to add widget memory: {e}")
    
    async def get_relevant_context(
        self, 
        user_message: str, 
        thread_id: str, 
        limit: int = 5
    ) -> str:
        """Get relevant context for the current user message"""
        if not self.enabled:
            return ""
        
        try:
            # Search for relevant memories
            relevant_memories = await asyncio.to_thread(
                self.memory.search,
                query=user_message,
                user_id=thread_id,
                limit=limit
            )
            
            if not relevant_memories:
                return ""
            
            # Format context for the agent
            context_parts = ["## 🧠 RELEVANT MEMORY CONTEXT"]
            context_parts.append(f"**Query**: {user_message}")
            context_parts.append(f"**Thread**: {thread_id}")
            context_parts.append("")
            
            for i, memory in enumerate(relevant_memories, 1):
                memory_text = memory.get('memory', '')
                score = memory.get('score', 0)
                
                context_parts.append(f"### Memory {i} (Relevance: {score:.2f})")
                context_parts.append(memory_text)
                context_parts.append("")
            
            context_parts.append("**INSTRUCTION**: Use this memory context to provide personalized, contextually aware responses. Reference specific details when relevant.")
            
            context = "\n".join(context_parts)
            logger.info(f"🔍 Retrieved {len(relevant_memories)} relevant memories for thread {thread_id}")
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Failed to retrieve relevant context: {e}")
            return ""
    
    async def get_thread_summary(self, thread_id: str) -> str:
        """Get a summary of the thread's conversation history"""
        if not self.enabled:
            return ""
        
        try:
            # Get all memories for this thread
            all_memories = await asyncio.to_thread(
                self.memory.get_all,
                user_id=thread_id
            )
            
            if not all_memories:
                return ""
            
            # Create thread summary
            conversation_memories = [m for m in all_memories if m.get('metadata', {}).get('type') == 'conversation']
            widget_memories = [m for m in all_memories if m.get('metadata', {}).get('type') == 'widget_interaction']
            
            summary_parts = [f"## 📊 THREAD SUMMARY ({thread_id})"]
            summary_parts.append(f"**Total Memories**: {len(all_memories)}")
            summary_parts.append(f"**Conversations**: {len(conversation_memories)}")
            summary_parts.append(f"**Widget Interactions**: {len(widget_memories)}")
            
            if widget_memories:
                widget_types = list(set(m.get('metadata', {}).get('widget_type') for m in widget_memories))
                summary_parts.append(f"**Widgets Used**: {', '.join(filter(None, widget_types))}")
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            logger.error(f"❌ Failed to get thread summary: {e}")
            return ""
    
    async def clear_thread_memory(self, thread_id: str):
        """Clear all memories for a specific thread"""
        if not self.enabled:
            return
        
        try:
            # Delete all memories for this user/thread
            await asyncio.to_thread(
                self.memory.delete_all,
                user_id=thread_id
            )
            
            logger.info(f"🗑️ Cleared all memories for thread {thread_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to clear thread memory: {e}")
    
    def is_enabled(self) -> bool:
        """Check if memory system is enabled and working"""
        return self.enabled

# Global memory manager instance
memory_manager = IntelligentMemoryManager()
