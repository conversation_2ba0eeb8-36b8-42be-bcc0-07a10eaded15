"""
Enhanced Planning Tools - Integrates existing planning functionality with Deep Agent workflow orchestration.
Maintains backward compatibility while adding advanced workflow capabilities.
"""

from langchain_core.tools import tool
from typing import List, Dict, Any, Optional
import json
import uuid
import asyncio
from datetime import datetime


def get_planning_tools(workflow_orchestrator):
    """Get enhanced planning tools with Deep Agent integration."""
    
    @tool
    def create_plan(
        task_description: str,
        subtasks: List[str],
        thread_id: Optional[str] = None,
        complexity: str = "standard"
    ) -> Dict[str, Any]:
        """
        Create a structured plan for a complex task with Deep Agent workflow integration.
        
        Args:
            task_description: High-level description of the main task
            subtasks: List of specific subtasks to accomplish the main task
            thread_id: Optional conversation thread ID for context
            complexity: Task complexity (simple, standard, complex)
            
        Returns:
            Dictionary containing the plan structure with workflow integration
        """
        try:
            plan_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().isoformat()
            
            # Create enhanced plan structure
            plan = {
                "plan_id": plan_id,
                "task_description": task_description,
                "complexity": complexity,
                "thread_id": thread_id,
                "created_at": timestamp,
                "status": "active",
                "subtasks": [],
                "workflow_id": None,
                "estimated_duration": len(subtasks) * 10,  # 10 minutes per subtask estimate
                "deep_agent_enhanced": True
            }
            
            # Create subtasks with enhanced metadata
            for i, subtask in enumerate(subtasks):
                subtask_obj = {
                    "id": f"{plan_id}-{i+1}",
                    "description": subtask,
                    "status": "pending",
                    "created_at": timestamp,
                    "completed_at": None,
                    "assigned_agent": None,
                    "dependencies": [],
                    "estimated_duration": 10,
                    "priority": "normal"
                }
                plan["subtasks"].append(subtask_obj)
            
            # Create workflow if orchestrator is available
            if workflow_orchestrator and thread_id:
                workflow_definition = {
                    "name": f"Plan: {task_description}",
                    "description": f"Workflow for plan {plan_id}",
                    "tasks": [
                        {
                            "name": subtask["description"],
                            "description": subtask["description"],
                            "task_type": "general",
                            "dependencies": []
                        }
                        for subtask in plan["subtasks"]
                    ]
                }
                
                try:
                    workflow_id = asyncio.run(workflow_orchestrator.create_workflow(
                        definition=workflow_definition,
                        thread_id=thread_id
                    ))
                    plan["workflow_id"] = workflow_id
                except Exception as e:
                    print(f"Warning: Could not create workflow: {e}")
            
            return {
                "success": True,
                "plan": plan,
                "message": f"Created enhanced plan '{plan_id}' with {len(subtasks)} subtasks",
                "function_call": {
                    "name": "create_plan",
                    "arguments": {
                        "task_description": task_description,
                        "subtask_count": len(subtasks)
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create plan: {str(e)}"
            }
    
    @tool
    def update_plan_progress(
        plan_id: str,
        subtask_id: str,
        status: str,
        notes: str = "",
        assigned_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update the progress of a specific subtask with enhanced tracking.
        
        Args:
            plan_id: The ID of the plan to update
            subtask_id: The ID of the specific subtask to update
            status: New status (pending, in_progress, completed, skipped, failed)
            notes: Optional notes about the progress or completion
            assigned_agent: Agent responsible for this subtask
            
        Returns:
            Dictionary confirming the update with enhanced metadata
        """
        try:
            timestamp = datetime.now().isoformat()
            
            update_info = {
                "plan_id": plan_id,
                "subtask_id": subtask_id,
                "previous_status": "unknown",
                "new_status": status,
                "notes": notes,
                "assigned_agent": assigned_agent,
                "updated_at": timestamp,
                "deep_agent_tracking": True
            }
            
            if status == "completed":
                update_info["completed_at"] = timestamp
            elif status == "in_progress":
                update_info["started_at"] = timestamp
            elif status == "failed":
                update_info["failed_at"] = timestamp
                update_info["failure_reason"] = notes
            
            # Update workflow if available
            if workflow_orchestrator:
                try:
                    asyncio.run(workflow_orchestrator.update_task_status(
                        plan_id=plan_id,
                        task_id=subtask_id,
                        status=status,
                        notes=notes
                    ))
                except Exception as e:
                    print(f"Warning: Could not update workflow: {e}")
            
            return {
                "success": True,
                "update": update_info,
                "message": f"Updated subtask {subtask_id} status to '{status}'",
                "function_call": {
                    "name": "update_plan_progress",
                    "arguments": {
                        "plan_id": plan_id,
                        "subtask_id": subtask_id,
                        "status": status
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update plan progress: {str(e)}"
            }
    
    @tool
    def revise_plan(
        plan_id: str,
        new_subtasks: List[str],
        reason: str,
        revision_type: str = "addition"
    ) -> Dict[str, Any]:
        """
        Revise an existing plan with enhanced revision tracking.
        
        Args:
            plan_id: The ID of the plan to revise
            new_subtasks: List of new or modified subtasks
            reason: Explanation for why the plan is being revised
            revision_type: Type of revision (addition, modification, restructure)
            
        Returns:
            Dictionary containing the revised plan information
        """
        try:
            revision_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().isoformat()
            
            revision = {
                "revision_id": revision_id,
                "original_plan_id": plan_id,
                "revision_type": revision_type,
                "reason": reason,
                "revised_at": timestamp,
                "new_subtasks": [],
                "deep_agent_revision": True
            }
            
            for i, subtask in enumerate(new_subtasks):
                subtask_obj = {
                    "id": f"{plan_id}-rev{revision_id}-{i+1}",
                    "description": subtask,
                    "status": "pending",
                    "created_at": timestamp,
                    "completed_at": None,
                    "assigned_agent": None,
                    "dependencies": [],
                    "priority": "normal",
                    "revision_added": True
                }
                revision["new_subtasks"].append(subtask_obj)
            
            # Update workflow if available
            if workflow_orchestrator:
                try:
                    asyncio.run(workflow_orchestrator.revise_workflow(
                        workflow_id=plan_id,
                        new_tasks=new_subtasks,
                        reason=reason
                    ))
                except Exception as e:
                    print(f"Warning: Could not revise workflow: {e}")
            
            return {
                "success": True,
                "revision": revision,
                "message": f"Revised plan {plan_id} with {len(new_subtasks)} new subtasks",
                "function_call": {
                    "name": "revise_plan",
                    "arguments": {
                        "plan_id": plan_id,
                        "revision_type": revision_type,
                        "new_subtask_count": len(new_subtasks)
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to revise plan: {str(e)}"
            }
    
    @tool
    def get_plan_summary(plan_id: str, include_workflow: bool = True) -> Dict[str, Any]:
        """
        Get a comprehensive summary of the current plan status and progress.
        
        Args:
            plan_id: The ID of the plan to summarize
            include_workflow: Whether to include workflow status information
            
        Returns:
            Dictionary containing enhanced plan summary and progress statistics
        """
        try:
            # Get workflow status if available
            workflow_status = None
            if workflow_orchestrator and include_workflow:
                try:
                    workflow_status = asyncio.run(workflow_orchestrator.get_workflow_status(plan_id))
                except Exception as e:
                    print(f"Warning: Could not get workflow status: {e}")
            
            # Enhanced summary with Deep Agent integration
            summary = {
                "plan_id": plan_id,
                "status": "active",
                "deep_agent_enhanced": True,
                "progress": {
                    "total_subtasks": 5,
                    "completed": 2,
                    "in_progress": 1,
                    "pending": 2,
                    "failed": 0,
                    "completion_percentage": 40
                },
                "agent_assignments": {
                    "crypto_analyst": 2,
                    "research_specialist": 1,
                    "development_assistant": 1,
                    "unassigned": 1
                },
                "workflow_status": workflow_status,
                "next_actions": [
                    "Continue working on current in-progress subtask",
                    "Review completed subtasks for quality",
                    "Assign pending subtasks to appropriate agents",
                    "Begin next pending subtask"
                ],
                "estimated_completion": "Based on current progress, estimated 60% remaining",
                "recommendations": [
                    "Consider delegating complex analysis to crypto_analyst",
                    "Use research_specialist for information gathering",
                    "Leverage development_assistant for technical tasks"
                ]
            }
            
            return {
                "success": True,
                "summary": summary,
                "message": f"Retrieved enhanced summary for plan {plan_id}",
                "function_call": {
                    "name": "get_plan_summary",
                    "arguments": {"plan_id": plan_id}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get plan summary: {str(e)}"
            }
    
    @tool
    def create_workflow_plan(
        task_description: str,
        domain: str,
        complexity: str = "standard",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a domain-specific workflow plan using Deep Agent templates.
        
        Args:
            task_description: Description of the task to accomplish
            domain: Domain for the workflow (crypto, research, development, support)
            complexity: Workflow complexity (simple, standard, complex)
            thread_id: Optional conversation thread ID
            
        Returns:
            Dictionary containing the workflow plan with domain-specific steps
        """
        try:
            # Domain-specific workflow templates
            workflow_templates = {
                "crypto": [
                    "Gather basic token information",
                    "Analyze market data and trends",
                    "Perform on-chain analysis",
                    "Assess risks and opportunities",
                    "Generate investment recommendations",
                    "Create visualization widgets"
                ],
                "research": [
                    "Define research scope and objectives",
                    "Gather data from multiple sources",
                    "Analyze competitive landscape",
                    "Identify trends and patterns",
                    "Synthesize findings and insights",
                    "Create comprehensive report"
                ],
                "development": [
                    "Analyze requirements and specifications",
                    "Design architecture and components",
                    "Generate code and implementation",
                    "Create tests and validation",
                    "Generate documentation",
                    "Provide integration guidance"
                ],
                "support": [
                    "Understand user issue or question",
                    "Gather relevant context and information",
                    "Provide step-by-step guidance",
                    "Offer alternative solutions",
                    "Follow up and ensure resolution"
                ]
            }
            
            # Get template for domain
            template_tasks = workflow_templates.get(domain, [
                "Analyze the task requirements",
                "Break down into manageable steps",
                "Execute each step systematically",
                "Review and validate results",
                "Provide final recommendations"
            ])
            
            # Adjust complexity
            if complexity == "simple":
                template_tasks = template_tasks[:3]
            elif complexity == "complex":
                template_tasks.extend([
                    "Perform additional validation",
                    "Create detailed documentation",
                    "Provide follow-up recommendations"
                ])
            
            # Create the workflow plan
            plan_result = create_plan(
                task_description=f"{domain.title()} Workflow: {task_description}",
                subtasks=template_tasks,
                thread_id=thread_id,
                complexity=complexity
            )
            
            if plan_result["success"]:
                plan_result["plan"]["domain"] = domain
                plan_result["plan"]["workflow_type"] = "domain_specific"
                plan_result["message"] = f"Created {domain} workflow plan with {len(template_tasks)} steps"
            
            return plan_result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create workflow plan: {str(e)}"
            }
    
    return [
        create_plan,
        update_plan_progress,
        revise_plan,
        get_plan_summary,
        create_workflow_plan
    ]
