import { createContext, useContext, useState, ReactNode } from 'react';
import type { Message, ChatThread } from '../types';

interface MessagesContextType {
  messages: Message[];
  setMessages: (messages: Message[] | ((prev: Message[]) => Message[])) => void;
  activeWidgetId: string | null;
  setActiveWidgetId: (id: string | null) => void;
  currentThreadId: string | null;
  setCurrentThreadId: (id: string | null) => void;
  getChatThreads: () => ChatThread[];
}

const MessagesContext = createContext<MessagesContextType | undefined>(undefined);

export function MessagesProvider({ children }: { children: ReactNode }) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [activeWidgetId, setActiveWidgetId] = useState<string | null>(null);
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);

  const getChatThreads = (): ChatThread[] => {
    // Group messages by threadId, excluding widgets
    const nonWidgetMessages = messages.filter(msg => msg.type !== 'widget');
    const threadMap = new Map<string, Message[]>();

    nonWidgetMessages.forEach(msg => {
      const threadId = msg.threadId || 'default';
      if (!threadMap.has(threadId)) {
        threadMap.set(threadId, []);
      }
      threadMap.get(threadId)!.push(msg);
    });

    // Convert to ChatThread objects
    const threads: ChatThread[] = [];
    threadMap.forEach((threadMessages, threadId) => {
      if (threadMessages.length > 0) {
        const sortedMessages = threadMessages.sort((a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
        const lastMessage = sortedMessages[0];

        // Generate thread title from first user message or use default
        const firstUserMessage = threadMessages
          .filter(msg => msg.type === 'user')
          .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())[0];

        const title = firstUserMessage
          ? firstUserMessage.content.slice(0, 50) + (firstUserMessage.content.length > 50 ? '...' : '')
          : 'New Chat';

        threads.push({
          id: threadId,
          title,
          lastMessage: lastMessage.content.slice(0, 100) + (lastMessage.content.length > 100 ? '...' : ''),
          lastActivity: lastMessage.timestamp,
          messageCount: threadMessages.length
        });
      }
    });

    // Sort by last activity
    return threads.sort((a, b) =>
      new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
    );
  };

  return (
    <MessagesContext.Provider value={{
      messages,
      setMessages,
      activeWidgetId,
      setActiveWidgetId,
      currentThreadId,
      setCurrentThreadId,
      getChatThreads
    }}>
      {children}
    </MessagesContext.Provider>
  );
}

export function useMessages() {
  const context = useContext(MessagesContext);
  if (context === undefined) {
    throw new Error('useMessages must be used within a MessagesProvider');
  }
  return context;
}