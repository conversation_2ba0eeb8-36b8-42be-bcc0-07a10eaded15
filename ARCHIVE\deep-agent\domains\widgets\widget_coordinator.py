"""
Widget Coordinator - Manages all widget specialist agents
Routes widget-related requests to appropriate specialists
"""

from typing import Dict, List, Any, Optional
from .pumpfun_agent import PumpFunWidgetAgent
from .dexscreener_agent import DexScreenerWidgetAgent
from .jupiter_agent import JupiterWidgetAgent
from .phantom_agent import PhantomWidgetAgent


class WidgetCoordinator:
    """Coordinates all widget specialist agents."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize widget specialists
        self.pumpfun_agent = PumpFunWidgetAgent(config)
        self.dexscreener_agent = DexScreenerWidgetAgent(config)
        self.jupiter_agent = JupiterWidgetAgent(config)
        self.phantom_agent = PhantomWidgetAgent(config)
        
        # Widget routing patterns
        self.widget_patterns = {
            "pumpfun": [
                "pumpfun", "pump.fun", "pump fun",
                "meme tokens", "new tokens", "latest tokens",
                "for you tokens", "for-you tokens", "recommended tokens",
                "trending tokens", "hot tokens", "popular tokens",
                "what's on pumpfun", "show pumpfun", "pump tokens"
            ],
            "dexscreener": [
                "chart", "charts", "price chart", "trading chart",
                "dexscreener", "dex screener", "technical analysis",
                "price data", "trading data", "dex", "check dex"
            ],
            "jupiter": [
                "swap", "trade", "jupiter", "exchange",
                "buy", "sell", "trading"
            ],
            "phantom": [
                "wallet", "phantom", "balance", "holdings",
                "connect wallet", "wallet connection"
            ]
        }
    
    def should_handle_request(self, message: str) -> Optional[str]:
        """Enhanced intelligent analysis to determine widget needs."""
        message_lower = message.lower()

        # Enhanced intent analysis with context awareness
        intent_analysis = self._analyze_user_intent(message_lower)
        print(f"🔍 Widget Coordinator - Intent analysis for '{message}': {intent_analysis}")

        # Priority-based widget selection
        if intent_analysis["confidence"] > 0.7:
            print(f"🎯 Widget Coordinator - High confidence match: {intent_analysis['widget_type']}")
            return intent_analysis["widget_type"]

        # Fallback to pattern matching
        for widget_type, patterns in self.widget_patterns.items():
            for pattern in patterns:
                if pattern in message_lower:
                    print(f"🎯 Widget Coordinator - Pattern match: '{pattern}' -> {widget_type}")
                    return widget_type

        # Smart defaults based on context
        if self._is_trading_intent(message_lower):
            return "jupiter"
        elif self._is_exploration_intent(message_lower):
            return "pumpfun"
        elif self._is_analysis_intent(message_lower):
            return "dexscreener"
        elif self._is_wallet_intent(message_lower):
            return "phantom"

        return None

    def _analyze_user_intent(self, message: str) -> Dict[str, Any]:
        """Advanced intent analysis using multiple signals."""
        intent_scores = {
            "pumpfun": 0.0,
            "dexscreener": 0.0,
            "jupiter": 0.0,
            "phantom": 0.0
        }

        # Action-based scoring
        if any(word in message for word in ["swap", "trade", "buy", "sell", "exchange"]):
            intent_scores["jupiter"] += 0.8

        if any(word in message for word in ["chart", "price", "analysis", "technical", "dex", "dexscreener", "check dex"]):
            intent_scores["dexscreener"] += 0.8

        if any(word in message for word in ["wallet", "balance", "holdings", "connect"]):
            intent_scores["phantom"] += 0.8

        if any(word in message for word in ["pumpfun", "meme", "new tokens", "explore"]):
            intent_scores["pumpfun"] += 0.8

        # Context-based scoring
        if "let's see" in message or "show me" in message:
            if "jupiter" in message:
                intent_scores["jupiter"] += 0.9
            elif any(word in message for word in ["tokens", "coins"]):
                intent_scores["pumpfun"] += 0.6

        # Find highest scoring intent
        best_intent = max(intent_scores.items(), key=lambda x: x[1])

        return {
            "widget_type": best_intent[0],
            "confidence": best_intent[1],
            "all_scores": intent_scores
        }

    def _is_trading_intent(self, message: str) -> bool:
        """Detect trading/swapping intent."""
        trading_signals = ["want to", "need to", "how do i", "can i"]
        trading_actions = ["swap", "trade", "buy", "sell", "exchange"]

        return any(signal in message for signal in trading_signals) and \
               any(action in message for action in trading_actions)

    def _is_exploration_intent(self, message: str) -> bool:
        """Detect token exploration intent."""
        exploration_signals = ["what's", "show", "see", "browse", "explore", "find"]
        token_context = ["tokens", "coins", "meme", "new", "trending"]

        return any(signal in message for signal in exploration_signals) and \
               any(context in message for context in token_context)

    def _is_analysis_intent(self, message: str) -> bool:
        """Detect chart/analysis intent."""
        analysis_signals = ["analyze", "check", "look at", "examine"]
        analysis_targets = ["price", "chart", "performance", "trend"]

        return any(signal in message for signal in analysis_signals) and \
               any(target in message for target in analysis_targets)

    def _is_wallet_intent(self, message: str) -> bool:
        """Detect wallet-related intent."""
        wallet_signals = ["my", "check", "see", "connect", "manage"]
        wallet_targets = ["wallet", "balance", "holdings", "portfolio"]

        return any(signal in message for signal in wallet_signals) and \
               any(target in message for target in wallet_targets)
    
    async def process_widget_request(
        self,
        message: str,
        thread_id: str,
        widget_type: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Route request to appropriate widget specialist."""
        
        if widget_type == "pumpfun":
            return await self.pumpfun_agent.process_request(message, thread_id, context)
        
        elif widget_type == "dexscreener":
            return await self.dexscreener_agent.process_request(message, thread_id, context)
        
        elif widget_type == "jupiter":
            return await self.jupiter_agent.process_request(message, thread_id, context)

        elif widget_type == "phantom":
            return await self.phantom_agent.process_request(message, thread_id, context)
        
        else:
            return {
                "content": f"I don't have a specialist for {widget_type} widgets yet.",
                "error": f"Unknown widget type: {widget_type}"
            }
    
    def get_widget_status(self) -> Dict[str, Any]:
        """Get status of all widget specialists."""
        return {
            "active_specialists": ["pumpfun", "dexscreener", "jupiter", "phantom"],
            "pumpfun_widgets": len(self.pumpfun_agent.active_widgets),
            "dexscreener_charts": len(self.dexscreener_agent.active_charts),
            "total_patterns": sum(len(patterns) for patterns in self.widget_patterns.values())
        }
