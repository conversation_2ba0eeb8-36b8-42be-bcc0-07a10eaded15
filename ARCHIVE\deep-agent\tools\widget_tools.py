"""
Simplified Widget Tools - Restored from working LangGraph agent.
These tools handle widget functionality for PumpFun, DexScreener, Jupiter, and Phantom.
"""

from langchain_core.tools import tool
from typing import Dict, List, Any, Optional


def get_all_widget_tools(widget_coordinator):
    """Get all widget tools - simplified working version."""
    
    @tool
    def show_pumpfun_widget() -> Dict[str, Any]:
        """
        Display the PumpFun interactive widget for browsing and exploring meme tokens.

        ONLY use this tool when the user specifically wants to:
        - Browse or explore pump.fun tokens interactively
        - See the actual pump.fun interface to discover new tokens
        - Interact with pump.fun's live token listings
        - When user asks "what's on pumpfun", "show pumpfun", "pumpfun widget", etc.

        DO NOT use for general questions about pump.fun or meme tokens - answer those conversationally.
        """
        return {
            "widget_type": "pumpfun",
            "description": "Displaying PumpFun widget for interactive token browsing"
        }

    @tool
    def show_dexscreener_widget() -> Dict[str, Any]:
        """
        Display the DexScreener interactive widget for live trading charts and DEX data.

        ONLY use this tool when the user specifically wants to:
        - View live price charts and trading data
        - Analyze specific token charts with technical indicators
        - Monitor real-time DEX trading activity
        - Access the actual DexScreener interface for detailed analysis

        DO NOT use for general questions about DEX trading or price info - answer those conversationally.
        """
        return {
            "widget_type": "dexscreener",
            "description": "Displaying DexScreener widget for live trading charts"
        }

    @tool
    def show_jupiter_widget() -> Dict[str, Any]:
        """
        Display the Jupiter interactive widget for token swapping.

        ONLY use this tool when the user specifically wants to:
        - Swap tokens on Solana
        - Access Jupiter's DEX aggregator interface
        - Execute actual token trades
        - Compare swap routes and prices

        DO NOT use for general questions about swapping - answer those conversationally.
        """
        return {
            "widget_type": "jupiter",
            "description": "Displaying Jupiter widget for token swapping"
        }

    @tool
    def show_phantom_widget() -> Dict[str, Any]:
        """
        Display the Phantom wallet widget for wallet connection and management.

        ONLY use this tool when the user specifically wants to:
        - Connect their Phantom wallet
        - View wallet balance and holdings
        - Manage wallet settings
        - Access wallet functionality

        DO NOT use for general questions about wallets - answer those conversationally.
        """
        return {
            "widget_type": "phantom",
            "description": "Displaying Phantom wallet widget"
        }

    @tool
    def show_token_chart_widget(
        thread_id: str,
        token_address: str,
        chain: str = "solana",
        chart_provider: str = "gmgn"
    ) -> Dict[str, Any]:
        """
        Display a token chart widget for price analysis.
        
        Args:
            thread_id: Conversation thread identifier
            token_address: Contract address of the token
            chain: Blockchain network (solana, ethereum, bsc, etc.)
            chart_provider: Chart provider (gmgn, dexscreener)
            
        Returns:
            Widget creation result with chart information
        """
        try:
            # Enhanced initial data
            initial_data = {
                "token_address": token_address,
                "chain": chain,
                "chart_provider": chart_provider,
                "timeframe": "1h",
                "auto_refresh": True,
                "deep_agent_context": True,
                "thread_id": thread_id
            }
            
            # Create widget
            widget_id = asyncio.run(widget_coordinator.create_widget(
                widget_type="token_chart",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={
                    "token_address": token_address,
                    "chain": chain,
                    "chart_provider": chart_provider,
                    "created_by": "show_token_chart"
                }
            ))
            
            return {
                "success": True,
                "widget_type": "token_chart",
                "widget_id": widget_id,
                "token_address": token_address,
                "chain": chain,
                "chart_provider": chart_provider,
                "message": f"Created {chain} token chart for {token_address} using {chart_provider}",
                "function_call": {
                    "name": "show_token_chart",
                    "arguments": {
                        "token_address": token_address,
                        "chain": chain,
                        "thread_id": thread_id
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create token chart: {str(e)}"
            }
    
    @tool
    def show_jupiter_widget(
        thread_id: str,
        input_token: str = "SOL",
        output_token: str = "USDC",
        amount: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Display the Jupiter swap widget for token trading.
        
        Args:
            thread_id: Conversation thread identifier
            input_token: Symbol or address of input token
            output_token: Symbol or address of output token
            amount: Optional amount to pre-fill
            
        Returns:
            Widget creation result with swap configuration
        """
        try:
            initial_data = {
                "input_token": input_token,
                "output_token": output_token,
                "amount": amount,
                "slippage": 0.5,
                "auto_route": True,
                "deep_agent_context": True,
                "thread_id": thread_id
            }
            
            widget_id = asyncio.run(widget_coordinator.create_widget(
                widget_type="jupiter",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={
                    "swap_pair": f"{input_token}/{output_token}",
                    "created_by": "show_jupiter_widget"
                }
            ))
            
            return {
                "success": True,
                "widget_type": "jupiter",
                "widget_id": widget_id,
                "swap_pair": f"{input_token}/{output_token}",
                "message": f"Created Jupiter swap widget for {input_token} to {output_token}",
                "function_call": {
                    "name": "show_jupiter_widget",
                    "arguments": {
                        "input_token": input_token,
                        "output_token": output_token,
                        "thread_id": thread_id
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create Jupiter widget: {str(e)}"
            }
    
    @tool
    def show_phantom_widget(thread_id: str) -> Dict[str, Any]:
        """
        Display the Phantom wallet widget for wallet management.
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            Widget creation result for wallet interface
        """
        try:
            initial_data = {
                "auto_connect": False,
                "show_balance": True,
                "show_transactions": True,
                "deep_agent_context": True,
                "thread_id": thread_id
            }
            
            widget_id = asyncio.run(widget_coordinator.create_widget(
                widget_type="phantom",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={
                    "wallet_type": "phantom",
                    "created_by": "show_phantom_widget"
                }
            ))
            
            return {
                "success": True,
                "widget_type": "phantom",
                "widget_id": widget_id,
                "message": "Created Phantom wallet widget",
                "function_call": {
                    "name": "show_phantom_widget",
                    "arguments": {"thread_id": thread_id}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create Phantom widget: {str(e)}"
            }
    
    @tool
    def show_dexscreener_widget(
        thread_id: str,
        token_address: Optional[str] = None,
        pair_address: Optional[str] = None,
        view_mode: str = "marketcap"
    ) -> Dict[str, Any]:
        """
        Display the DexScreener widget for DEX trading data.
        
        Args:
            thread_id: Conversation thread identifier
            token_address: Token contract address to analyze
            pair_address: Specific trading pair address
            view_mode: Display mode (marketcap, price, volume)
            
        Returns:
            Widget creation result with DEX data configuration
        """
        try:
            initial_data = {
                "token_address": token_address,
                "pair_address": pair_address,
                "view_mode": view_mode,
                "auto_refresh": True,
                "deep_agent_context": True,
                "thread_id": thread_id
            }
            
            widget_id = asyncio.run(widget_coordinator.create_widget(
                widget_type="dexscreener",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={
                    "token_address": token_address,
                    "pair_address": pair_address,
                    "view_mode": view_mode,
                    "created_by": "show_dexscreener_widget"
                }
            ))
            
            return {
                "success": True,
                "widget_type": "dexscreener",
                "widget_id": widget_id,
                "token_address": token_address,
                "pair_address": pair_address,
                "view_mode": view_mode,
                "message": "Created DexScreener widget for DEX trading data",
                "function_call": {
                    "name": "show_dexscreener_widget",
                    "arguments": {
                        "token_address": token_address,
                        "thread_id": thread_id
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create DexScreener widget: {str(e)}"
            }
    
    @tool
    def update_widget_data(
        widget_id: str,
        data: str,
        metadata: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update data for an existing widget.
        
        Args:
            widget_id: Unique identifier of the widget to update
            data: JSON string of new data to update
            metadata: JSON string of metadata to update
            
        Returns:
            Update operation result
        """
        try:
            update_data = json.loads(data)
            update_meta = json.loads(metadata) if metadata else None
            
            success = asyncio.run(widget_coordinator.update_widget_data(
                widget_id=widget_id,
                data=update_data,
                metadata=update_meta
            ))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "message": f"Updated widget {widget_id} successfully",
                    "function_call": {
                        "name": "update_widget_data",
                        "arguments": {"widget_id": widget_id}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Widget {widget_id} not found or inactive"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update widget {widget_id}: {str(e)}"
            }
    
    @tool
    def close_widget(widget_id: str) -> Dict[str, Any]:
        """
        Close and remove a widget from the conversation.
        
        Args:
            widget_id: Unique identifier of the widget to close
            
        Returns:
            Close operation result
        """
        try:
            success = asyncio.run(widget_coordinator.close_widget(widget_id))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "message": f"Closed widget {widget_id} successfully",
                    "function_call": {
                        "name": "close_widget",
                        "arguments": {"widget_id": widget_id}
                    }
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Widget {widget_id} not found or already closed"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to close widget {widget_id}: {str(e)}"
            }
    
    @tool
    def get_active_widgets(thread_id: str) -> Dict[str, Any]:
        """
        Get all active widgets for a conversation thread.
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            List of active widgets with their current state
        """
        try:
            widgets = asyncio.run(widget_coordinator.get_active_widgets(thread_id))
            
            return {
                "success": True,
                "thread_id": thread_id,
                "active_widgets": widgets,
                "widget_count": len(widgets),
                "message": f"Retrieved {len(widgets)} active widgets for thread {thread_id}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get active widgets for thread {thread_id}: {str(e)}"
            }
    
    return [
        show_pumpfun_widget,
        show_dexscreener_widget,
        show_jupiter_widget,
        show_phantom_widget,
        show_token_chart_widget,
        update_widget_data,
        close_widget,
        get_active_widgets
    ]
