/**
 * Agent Integration Layer
 * 
 * This module provides a standardized interface for bidirectional communication
 * between the UI/widgets and any backend agent system. It's designed to be
 * agent-agnostic so that rebuilding the backend won't break the frontend.
 */

// Browser-compatible EventEmitter implementation
class BrowserEventEmitter {
  private events: Map<string, Function[]> = new Map();

  on(event: string, listener: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.events.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]): void {
    const listeners = this.events.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }
}

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface AgentMessage {
  id: string;
  threadId: string;
  content: string;
  type: 'user' | 'assistant' | 'system' | 'widget' | 'error';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface WidgetContext {
  widgetId: string;
  widgetType: 'pumpfun' | 'dexscreener' | 'phantom' | 'jupiter';
  threadId: string;
  data: Record<string, any>;
  timestamp: Date;
  isActive: boolean;
}

export interface AgentResponse {
  content: string;
  type: 'text' | 'function_call' | 'widget' | 'thinking' | 'error';
  functionName?: string;
  functionArgs?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface AgentConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
  retryAttempts: number;
  enableStreaming: boolean;
  enableMemory: boolean;
}

// ============================================================================
// AGENT INTEGRATION CLASS
// ============================================================================

export class AgentIntegration extends BrowserEventEmitter {
  private config: AgentConfig;
  private activeWidgets: Map<string, WidgetContext> = new Map();
  private messageHistory: Map<string, AgentMessage[]> = new Map();
  private connectionStatus: 'connected' | 'disconnected' | 'connecting' = 'disconnected';

  constructor(config: Partial<AgentConfig> = {}) {
    super();
    
    this.config = {
      baseUrl: (import.meta as any).env?.VITE_DEEP_AGENT_URL || 'http://localhost:8001',
      timeout: 30000,
      retryAttempts: 3,
      enableStreaming: true,
      enableMemory: true,
      ...config
    };

    this.setupConnectionMonitoring();
  }

  // ============================================================================
  // CONNECTION MANAGEMENT
  // ============================================================================

  private setupConnectionMonitoring() {
    // Ping the agent every 30 seconds to check connection
    setInterval(async () => {
      try {
        await this.pingAgent();
        if (this.connectionStatus !== 'connected') {
          this.connectionStatus = 'connected';
          this.emit('connectionStatusChanged', 'connected');
        }
      } catch (error) {
        if (this.connectionStatus !== 'disconnected') {
          this.connectionStatus = 'disconnected';
          this.emit('connectionStatusChanged', 'disconnected');
          this.emit('error', { type: 'connection', error });
        }
      }
    }, 30000);
  }

  private async pingAgent(): Promise<boolean> {
    const response = await fetch(`${this.config.baseUrl}/api/health`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      signal: AbortSignal.timeout(5000)
    });
    return response.ok;
  }

  public getConnectionStatus(): string {
    return this.connectionStatus;
  }

  // ============================================================================
  // MESSAGE HANDLING
  // ============================================================================

  public async sendMessage(
    message: string, 
    threadId: string, 
    options: {
      includeWidgetContext?: boolean;
      includeHistory?: boolean;
      streaming?: boolean;
      image?: string;
    } = {}
  ): Promise<AgentResponse | AsyncIterable<AgentResponse>> {
    
    const {
      includeWidgetContext = true,
      includeHistory = true,
      streaming = this.config.enableStreaming
    } = options;

    // Prepare message payload
    const payload = {
      message,
      threadId,
      timestamp: new Date().toISOString(),
      context: this.buildMessageContext(threadId, includeWidgetContext, includeHistory),
      options: {
        streaming,
        enableMemory: this.config.enableMemory
      }
    };

    // Store user message
    this.addMessageToHistory({
      id: this.generateId(),
      threadId,
      content: message,
      type: 'user',
      timestamp: new Date()
    });

    try {
      if (streaming) {
        return this.sendStreamingMessage(payload);
      } else {
        return this.sendSingleMessage(payload);
      }
    } catch (error) {
      this.emit('error', { type: 'message', error, threadId });
      throw error;
    }
  }

  private async sendSingleMessage(payload: any): Promise<AgentResponse> {
    // Build API-compliant payload: { messages: [{role, content, image?}, ...], threadId }
    const history = this.getMessageHistory(payload.threadId) || [];
    const apiMessages = history
      .filter(m => m.type === 'user' || m.type === 'assistant')
      .map(m => ({ role: m.type, content: m.content, image: (m as any).image }))
      .concat([{ role: 'user', content: payload.message, image: payload.options?.image }]);

    const response = await fetch(`${this.config.baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ messages: apiMessages, threadId: payload.threadId }),
      signal: AbortSignal.timeout(this.config.timeout)
    });

    if (!response.ok) {
      throw new Error(`Agent request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    const agentResponse: AgentResponse = {
      content: data.content || '',
      type: data.type || 'text',
      functionName: data.function_name,
      functionArgs: data.function_args,
      metadata: data.metadata,
      timestamp: new Date()
    };

    // Store assistant response
    this.addMessageToHistory({
      id: this.generateId(),
      threadId: payload.threadId,
      content: agentResponse.content,
      type: 'assistant',
      timestamp: agentResponse.timestamp,
      metadata: agentResponse.metadata
    });

    this.emit('messageReceived', agentResponse);
    return agentResponse;
  }

  private async* sendStreamingMessage(payload: any): AsyncIterable<AgentResponse> {
    // Build API-compliant payload: { messages: [{role, content, image?}, ...], threadId }
    const history = this.getMessageHistory(payload.threadId) || [];
    const apiMessages = history
      .filter(m => m.type === 'user' || m.type === 'assistant')
      .map(m => ({ role: m.type, content: m.content, image: (m as any).image }))
      .concat([{ role: 'user', content: payload.message, image: payload.options?.image }]);

    const response = await fetch(`${this.config.baseUrl}/api/chat/stream`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ messages: apiMessages, threadId: payload.threadId })
      // No AbortSignal timeout for streaming to prevent premature termination
    });

    if (!response.ok) {
      throw new Error(`Streaming request failed: ${response.status} ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    const decoder = new TextDecoder();
    let buffer = '';
    let fullContent = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (let line of lines) {
          // Normalize CRLF and trim
          line = line.replace(/\r$/g, '').trim();
          if (line.startsWith('data:')) {
            const jsonPart = line.slice(5).trim(); // slice after 'data:'
            if (!jsonPart || jsonPart === '[DONE]') continue;
            try {
              const data = JSON.parse(jsonPart);

              const agentResponse: AgentResponse = {
                content: data.content || '',
                type: data.type || 'text',
                functionName: data.function_name,
                functionArgs: data.function_args,
                metadata: data.metadata,
                timestamp: new Date()
              };

              if (agentResponse.type === 'text' || agentResponse.type === 'content') {
                fullContent += agentResponse.content;
              }

              this.emit('messageChunk', agentResponse);
              yield agentResponse;

            } catch (parseError) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }

      // Store complete assistant response
      if (fullContent) {
        this.addMessageToHistory({
          id: this.generateId(),
          threadId: payload.threadId,
          content: fullContent,
          type: 'assistant',
          timestamp: new Date()
        });
      }

    } finally {
      reader.releaseLock();
    }
  }

  // ============================================================================
  // WIDGET CONTEXT MANAGEMENT
  // ============================================================================

  public registerWidget(widgetContext: Omit<WidgetContext, 'timestamp' | 'isActive'>): void {
    const fullContext: WidgetContext = {
      ...widgetContext,
      timestamp: new Date(),
      isActive: true
    };

    this.activeWidgets.set(widgetContext.widgetId, fullContext);
    
    // Send widget context to agent
    this.sendWidgetContextToAgent(fullContext);
    
    this.emit('widgetRegistered', fullContext);
  }

  public updateWidgetData(widgetId: string, data: Record<string, any>): void {
    const widget = this.activeWidgets.get(widgetId);
    if (widget) {
      widget.data = { ...widget.data, ...data };
      widget.timestamp = new Date();
      
      this.sendWidgetContextToAgent(widget);
      this.emit('widgetUpdated', widget);
    }
  }

  public deactivateWidget(widgetId: string): void {
    const widget = this.activeWidgets.get(widgetId);
    if (widget) {
      widget.isActive = false;
      this.emit('widgetDeactivated', widget);
    }
  }

  public getActiveWidgets(threadId?: string): WidgetContext[] {
    const widgets = Array.from(this.activeWidgets.values())
      .filter(w => w.isActive);
    
    if (threadId) {
      return widgets.filter(w => w.threadId === threadId);
    }
    
    return widgets;
  }

  private async sendWidgetContextToAgent(widget: WidgetContext): Promise<void> {
    try {
      await fetch(`${this.config.baseUrl}/api/widget-context`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          widgetType: widget.widgetType,
          widgetId: widget.widgetId,
          threadId: widget.threadId,
          data: widget.data,
          timestamp: widget.timestamp.toISOString()
        })
      });
    } catch (error) {
      console.warn('Failed to send widget context to agent:', error);
      this.emit('error', { type: 'widget_context', error, widget });
    }
  }

  // ============================================================================
  // CONTEXT BUILDING
  // ============================================================================

  private buildMessageContext(
    threadId: string, 
    includeWidgetContext: boolean, 
    includeHistory: boolean
  ): Record<string, any> {
    const context: Record<string, any> = {
      threadId,
      timestamp: new Date().toISOString()
    };

    if (includeWidgetContext) {
      const activeWidgets = this.getActiveWidgets(threadId);
      context.widgets = activeWidgets.map(w => ({
        type: w.widgetType,
        id: w.widgetId,
        data: w.data,
        timestamp: w.timestamp.toISOString()
      }));
    }

    if (includeHistory) {
      const history = this.getMessageHistory(threadId);
      context.recentMessages = history.slice(-10); // Last 10 messages
    }

    return context;
  }

  // ============================================================================
  // MESSAGE HISTORY
  // ============================================================================

  private addMessageToHistory(message: AgentMessage): void {
    const threadHistory = this.messageHistory.get(message.threadId) || [];
    threadHistory.push(message);
    
    // Keep only last 100 messages per thread
    if (threadHistory.length > 100) {
      threadHistory.splice(0, threadHistory.length - 100);
    }
    
    this.messageHistory.set(message.threadId, threadHistory);
  }

  public getMessageHistory(threadId: string): AgentMessage[] {
    return this.messageHistory.get(threadId) || [];
  }

  public clearMessageHistory(threadId: string): void {
    this.messageHistory.delete(threadId);
    this.emit('historyCleared', threadId);
  }

  // ============================================================================
  // UTILITIES
  // ============================================================================

  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public getConfig(): AgentConfig {
    return { ...this.config };
  }

  public updateConfig(newConfig: Partial<AgentConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const agentIntegration = new AgentIntegration();

// ============================================================================
// REACT HOOKS
// ============================================================================

export { useAgentIntegration } from './hooks/useAgentIntegration';
export { useWidgetContext } from './hooks/useWidgetContext';
export { useAgentMemory } from './hooks/useAgentMemory';
