"""
DexScreener Widget Specialist Agent
Handles all DexScreener widget interactions, chart analysis, and trading data
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI


class DexScreenerWidgetAgent:
    """Specialized agent for DexScreener widget management and chart analysis."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get("model_name", "z-ai/glm-4.5")
        self.api_key = config.get("openrouter_api_key")
        
        # Configure LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=0.7,
            max_tokens=2000
        )
        
        # Initialize tools
        self.tools = self._get_dexscreener_tools()

        # Create simple LLM without agent complexity
        # We'll handle tool calling manually to avoid validation issues
        
        # Widget state
        self.active_charts = {}
        self.price_alerts = {}
        
    def _get_system_prompt(self) -> str:
        """Get the DexScreener specialist system prompt."""
        return """You are the DexScreener Widget Specialist, an expert in DEX trading data and chart analysis.

## Your Expertise
- Advanced technical analysis and chart reading
- DEX trading patterns and liquidity analysis
- Real-time price monitoring and trend identification
- Multi-chain DEX data interpretation

## Your Responsibilities
1. **Widget Management**: Launch DexScreener widgets for chart analysis
2. **Chart Analysis**: Provide technical analysis and trading insights
3. **Price Monitoring**: Track price movements and identify opportunities
4. **Trading Guidance**: Help users understand DEX trading dynamics

## When to Launch Widgets
ALWAYS call show_dexscreener_widget() when users:
- Ask for "charts" or "price charts"
- Want to "analyze" token prices
- Ask about "trading data" or "DEX data"
- Need "technical analysis"

## Response Style
- Be analytical but concise about trading opportunities
- Provide brief, data-driven insights without over-explaining
- Keep responses short and to the point - the widget provides the charts
- Use trading terminology sparingly
- NEVER include function call syntax like "show_dexscreener_widget()" in your response
- NEVER explain what the widget will show - let users explore the charts themselves

Remember: Keep responses brief and professional. The widget provides all the chart data users need!"""

    def _get_dexscreener_tools(self) -> List:
        """Get DexScreener-specific tools."""
        
        @tool
        def show_dexscreener_widget() -> Dict[str, Any]:
            """
            Display the DexScreener widget for live trading charts and DEX data.
            
            Use this when users want to:
            - View price charts and trading data
            - Analyze token charts with technical indicators
            - Monitor real-time DEX trading activity
            - Access detailed chart analysis
            """
            return {
                "widget_type": "dexscreener",
                "description": "Displaying DexScreener widget for live trading charts"
            }
        
        @tool
        def analyze_chart_patterns() -> Dict[str, Any]:
            """Analyze current chart patterns and technical indicators."""
            return {
                "patterns": ["Bullish flag formation", "Support level holding", "Volume increasing"],
                "indicators": {
                    "RSI": "Neutral (45-55 range)",
                    "MACD": "Bullish crossover forming",
                    "Volume": "Above average"
                },
                "recommendation": "Watch for breakout above resistance"
            }
        
        @tool
        def get_trading_insights(token_pair: str) -> Dict[str, Any]:
            """Get trading insights for a specific token pair."""
            return {
                "pair": token_pair,
                "liquidity": "Analyzing liquidity depth and spread",
                "volume_profile": "24h volume analysis and trading patterns",
                "support_resistance": "Key levels identified from price action"
            }
        
        return [show_dexscreener_widget, analyze_chart_patterns, get_trading_insights]
    
    async def process_request(
        self,
        message: str,
        thread_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a DexScreener-related request."""
        try:
            # Determine if we should show widget based on message content
            should_show_widget = self._should_show_widget(message)
            print(f"🔍 DexScreener Agent - should_show_widget: {should_show_widget} for message: '{message}'")

            # Generate response using direct LLM call
            system_prompt = self._get_system_prompt()

            response = await self.llm.ainvoke([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ])

            # Prepare function call if widget should be shown
            function_call = None
            if should_show_widget:
                function_call = {
                    "name": "showDexScreenerWidget",
                    "arguments": {}
                }
                print(f"🔍 DexScreener Agent - Creating function_call: {function_call}")

            return {
                "content": response.content,
                "function_call": function_call,
                "specialist": "dexscreener",
                "metadata": {
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent_type": "dexscreener_specialist"
                }
            }

        except Exception as e:
            return {
                "content": f"I encountered an error analyzing chart data: {str(e)}",
                "error": str(e),
                "specialist": "dexscreener"
            }

    def _should_show_widget(self, message: str) -> bool:
        """Determine if widget should be shown based on message content."""
        message_lower = message.lower()
        widget_triggers = [
            "show", "display", "open", "launch", "widget",
            "chart", "price", "analysis", "dexscreener", "dex screener",
            "check", "dex", "trading", "charts", "screener"
        ]
        return any(trigger in message_lower for trigger in widget_triggers)
    
    def _extract_widget_call(self, messages) -> Optional[Dict[str, Any]]:
        """Extract widget calls from agent response."""
        for message in reversed(messages):
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    if tool_call.get('name') == 'show_dexscreener_widget':
                        return {
                            "name": "showDexScreenerWidget",
                            "arguments": {}
                        }
        return None
