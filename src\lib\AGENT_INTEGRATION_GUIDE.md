# 🤖 Chad GPT Agent Integration Guide

This guide explains how to use the future-proof agent integration system that ensures seamless bidirectional data flow between the UI/widgets and any backend agent system.

## 🏗️ Architecture Overview

The agent integration system is designed to be **agent-agnostic**, meaning you can rebuild the backend agents without breaking the frontend. It provides:

- **Bidirectional Communication**: UI ↔ Agent ↔ Widgets
- **Automatic Context Sharing**: Widgets automatically share data with agents
- **Memory Management**: Persistent conversation and widget context
- **Connection Resilience**: Automatic reconnection and error handling
- **Type Safety**: Full TypeScript support

## 🚀 Quick Start

### 1. Basic Agent Communication

```tsx
import { useAgentIntegration } from '../lib/hooks/useAgentIntegration';

function ChatComponent() {
  const {
    sendMessage,
    messages,
    isConnected,
    isLoading,
    streamingContent,
    isStreaming
  } = useAgentIntegration({
    threadId: 'user-thread-123',
    enableStreaming: true,
    enableMemory: true
  });

  const handleSendMessage = async () => {
    await sendMessage('What are the top tokens on PumpFun?', {
      includeWidgetContext: true,
      includeHistory: true
    });
  };

  return (
    <div>
      {messages.map(msg => (
        <div key={msg.id}>{msg.content}</div>
      ))}
      {isStreaming && <div>{streamingContent}</div>}
      <button onClick={handleSendMessage} disabled={!isConnected}>
        Send Message
      </button>
    </div>
  );
}
```

### 2. Widget Integration

```tsx
import { usePumpFunWidget } from '../lib/hooks/useWidgetContext';

function PumpFunWidget() {
  const {
    widgetId,
    isRegistered,
    updateData,
    sendContextToAgent
  } = usePumpFunWidget('user-thread-123', {
    autoRegister: true,
    syncInterval: 5000
  });

  // When token data changes
  const handleTokensLoaded = (tokens: Token[]) => {
    updateData({
      category: 'for-you',
      tokenCount: tokens.length,
      topTokens: tokens.slice(0, 10),
      lastUpdated: Date.now()
    });
  };

  return (
    <div>
      <h3>PumpFun Widget {isRegistered ? '✅' : '⏳'}</h3>
      {/* Widget content */}
    </div>
  );
}
```

### 3. Memory Management

```tsx
import { useAgentMemory } from '../lib/hooks/useAgentMemory';

function MemoryComponent() {
  const {
    getRelevantMemories,
    addMemory,
    buildContextSummary,
    memoryStats
  } = useAgentMemory({
    threadId: 'user-thread-123',
    enableAutoSave: true
  });

  const searchMemories = async () => {
    const memories = await getRelevantMemories('PumpFun tokens', 5);
    console.log('Relevant memories:', memories);
  };

  return (
    <div>
      <p>Total memories: {memoryStats.totalEntries}</p>
      <button onClick={searchMemories}>Search Memories</button>
    </div>
  );
}
```

## 🔧 Advanced Usage

### Custom Widget Types

```tsx
// Create a custom widget hook
function useCustomWidget(threadId: string) {
  return useWidgetContext({
    widgetType: 'custom' as any, // Extend the type
    threadId,
    autoRegister: true
  });
}

// Use in component
function CustomWidget() {
  const widget = useCustomWidget('thread-123');
  
  useEffect(() => {
    widget.updateData({
      customData: 'value',
      timestamp: Date.now()
    });
  }, []);

  return <div>Custom Widget</div>;
}
```

### Agent Configuration

```tsx
import { agentIntegration } from '../lib/agentIntegration';

// Update agent configuration
agentIntegration.updateConfig({
  baseUrl: 'http://localhost:8000',
  timeout: 30000,
  enableStreaming: true,
  enableMemory: true
});

// Listen to connection events
agentIntegration.on('connectionStatusChanged', (status) => {
  console.log('Connection status:', status);
});

agentIntegration.on('error', (error) => {
  console.error('Agent error:', error);
});
```

### Manual Context Management

```tsx
// Manually register widget
agentIntegration.registerWidget({
  widgetId: 'custom-widget-123',
  widgetType: 'pumpfun',
  threadId: 'thread-123',
  data: { tokens: [], category: 'for-you' }
});

// Update widget data
agentIntegration.updateWidgetData('custom-widget-123', {
  newTokens: [...],
  lastUpdated: Date.now()
});

// Get active widgets
const activeWidgets = agentIntegration.getActiveWidgets('thread-123');
```

## 🎯 Backend Agent Requirements

For the agent integration to work, your backend must implement these endpoints:

### Required Endpoints

```
POST /api/chat
POST /api/chat/stream (for streaming)
POST /api/widget-context
GET  /api/health
POST /api/memory/search
GET  /api/memory/thread/:threadId
POST /api/memory/add
DELETE /api/memory/:memoryId
POST /api/memory/context-summary
```

### Message Format

```json
{
  "message": "User message",
  "threadId": "thread-123",
  "timestamp": "2024-01-15T10:30:00Z",
  "context": {
    "widgets": [
      {
        "type": "pumpfun",
        "id": "widget-123",
        "data": { "tokens": [...] },
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ],
    "recentMessages": [...]
  },
  "options": {
    "streaming": true,
    "enableMemory": true
  }
}
```

### Widget Context Format

```json
{
  "widgetType": "pumpfun",
  "widgetId": "widget-123",
  "threadId": "thread-123",
  "data": {
    "category": "for-you",
    "tokenCount": 50,
    "topTokens": [...],
    "lastUpdated": 1642248600000
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔄 Migration Guide

### From Old System to New Agent Integration

1. **Replace direct API calls**:
   ```tsx
   // Old
   const response = await fetch('/api/chat', { ... });
   
   // New
   const { sendMessage } = useAgentIntegration({ threadId });
   await sendMessage('Hello');
   ```

2. **Replace manual context management**:
   ```tsx
   // Old
   sendWidgetContext(widgetType, data);
   
   // New
   const widget = usePumpFunWidget(threadId);
   widget.updateData(data);
   ```

3. **Add memory integration**:
   ```tsx
   // New
   const memory = useAgentMemory({ threadId });
   const relevantContext = await memory.getRelevantMemories(query);
   ```

## 🧪 Testing

### Test Agent Connection

```tsx
import { agentIntegration } from '../lib/agentIntegration';

// Check connection status
console.log('Status:', agentIntegration.getConnectionStatus());

// Test ping
try {
  await agentIntegration.pingAgent();
  console.log('✅ Agent is reachable');
} catch (error) {
  console.log('❌ Agent is not reachable');
}
```

### Mock Agent for Development

```tsx
// Create a mock agent for testing
const mockAgent = {
  async sendMessage(message: string) {
    return {
      content: `Mock response to: ${message}`,
      type: 'text',
      timestamp: new Date()
    };
  }
};
```

## 🎉 Benefits

### For Frontend Development
- ✅ **Future-proof**: Works with any backend agent implementation
- ✅ **Type-safe**: Full TypeScript support
- ✅ **Automatic**: Widgets auto-register and sync data
- ✅ **Resilient**: Handles connection issues gracefully

### For Backend Development
- ✅ **Standardized**: Clear API contract
- ✅ **Flexible**: Can rebuild agents without breaking frontend
- ✅ **Rich context**: Automatic widget and conversation context
- ✅ **Memory-aware**: Built-in memory management support

### For User Experience
- ✅ **Seamless**: Widgets and chat work together automatically
- ✅ **Contextual**: Agent understands widget state
- ✅ **Persistent**: Conversations and context are remembered
- ✅ **Real-time**: Streaming responses and live updates

This agent integration system ensures that Chad GPT will continue to work seamlessly regardless of how the backend agents are implemented or rebuilt! 🚀
