"""
Delegation tools for the Chad GPT Deep Agent.
These tools handle task delegation to specialized sub-agents.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List
from datetime import datetime
import uuid
from subagents import get_subagent_by_name, list_available_subagents


@tool
def delegate_to_subagent(agent_name: str, task_description: str, context: str = "", priority: str = "normal") -> Dict[str, Any]:
    """
    Delegate a specific task to a specialized sub-agent.
    
    Args:
        agent_name: Name of the sub-agent to delegate to (crypto-analyst, trading-strategist, defi-specialist, research-specialist, code-analyst, widget-specialist)
        task_description: Clear description of the task to be performed
        context: Additional context or background information for the task
        priority: Task priority level (low, normal, high, urgent)
        
    Returns:
        Dictionary containing the delegation result and sub-agent response
    """
    # Validate agent name
    available_agents = list_available_subagents()
    if agent_name not in available_agents:
        return {
            "success": False,
            "error": f"Unknown sub-agent '{agent_name}'. Available agents: {', '.join(available_agents)}",
            "available_agents": available_agents
        }
    
    # Get sub-agent configuration
    subagent = get_subagent_by_name(agent_name)
    if not subagent:
        return {
            "success": False,
            "error": f"Failed to load configuration for sub-agent '{agent_name}'"
        }
    
    # Create delegation record
    delegation_id = str(uuid.uuid4())[:8]
    delegation_record = {
        "delegation_id": delegation_id,
        "agent_name": agent_name,
        "task_description": task_description,
        "context": context,
        "priority": priority,
        "delegated_at": datetime.now().isoformat(),
        "status": "delegated"
    }
    
    # Simulate sub-agent processing
    # In a real implementation, this would invoke the actual sub-agent
    simulated_response = _simulate_subagent_response(agent_name, task_description, context)
    
    return {
        "success": True,
        "delegation": delegation_record,
        "subagent_info": {
            "name": subagent["name"],
            "description": subagent["description"],
            "available_tools": subagent.get("tools", [])
        },
        "response": simulated_response,
        "message": f"Successfully delegated task to {agent_name}"
    }


@tool
def list_subagent_capabilities() -> Dict[str, Any]:
    """
    List all available sub-agents and their capabilities.
    
    Returns:
        Dictionary containing information about all available sub-agents
    """
    from subagents import SUBAGENTS
    
    capabilities = []
    for agent in SUBAGENTS:
        capability = {
            "name": agent["name"],
            "description": agent["description"],
            "tools": agent.get("tools", []),
            "specializations": _extract_specializations(agent["prompt"])
        }
        capabilities.append(capability)
    
    return {
        "success": True,
        "total_agents": len(capabilities),
        "agents": capabilities,
        "message": f"Listed capabilities for {len(capabilities)} sub-agents"
    }


@tool
def get_delegation_status(delegation_id: str) -> Dict[str, Any]:
    """
    Get the status of a previously delegated task.
    
    Args:
        delegation_id: The ID of the delegation to check
        
    Returns:
        Dictionary containing the delegation status and any results
    """
    # In a real implementation, this would retrieve actual delegation status
    # For now, we'll simulate a response
    
    status_info = {
        "delegation_id": delegation_id,
        "status": "completed",  # pending, in_progress, completed, failed
        "progress": 100,
        "started_at": "2024-01-01T10:00:00",
        "completed_at": datetime.now().isoformat(),
        "result_summary": "Task completed successfully by sub-agent",
        "output_files": ["analysis_report.md", "data_summary.json"],
        "next_actions": ["Review generated report", "Validate findings", "Share results with user"]
    }
    
    return {
        "success": True,
        "status": status_info,
        "message": f"Retrieved status for delegation {delegation_id}"
    }


def _simulate_subagent_response(agent_name: str, task: str, context: str) -> Dict[str, Any]:
    """Simulate a sub-agent response based on the agent type and task."""
    
    base_response = {
        "agent": agent_name,
        "task_received": task,
        "processing_time": "2.3 seconds",
        "confidence": 0.85
    }
    
    if agent_name == "crypto-analyst":
        base_response.update({
            "analysis_type": "fundamental_and_technical",
            "key_findings": [
                "Strong tokenomics with deflationary mechanism",
                "Active development team with regular updates",
                "Growing community engagement and adoption"
            ],
            "risk_assessment": "Medium risk with high potential upside",
            "recommendation": "Consider for long-term portfolio allocation"
        })
    
    elif agent_name == "trading-strategist":
        base_response.update({
            "strategy_type": "momentum_with_risk_management",
            "entry_signals": ["RSI oversold + volume spike", "Break above resistance"],
            "risk_management": "2% position size, 5% stop loss",
            "time_horizon": "2-4 weeks",
            "expected_return": "15-25% target"
        })
    
    elif agent_name == "defi-specialist":
        base_response.update({
            "protocol_analysis": "Automated Market Maker with yield farming",
            "yield_opportunities": "12-18% APY on stable pairs",
            "risks": ["Smart contract risk", "Impermanent loss", "Governance changes"],
            "recommendation": "Suitable for experienced DeFi users"
        })
    
    elif agent_name == "research-specialist":
        base_response.update({
            "research_scope": "Comprehensive market analysis",
            "data_sources": ["CoinGecko", "DeFiLlama", "GitHub", "Social sentiment"],
            "key_insights": ["Market consolidation phase", "Institutional adoption growing"],
            "report_sections": ["Executive Summary", "Market Analysis", "Recommendations"]
        })
    
    elif agent_name == "code-analyst":
        base_response.update({
            "code_quality": "High - follows best practices",
            "security_issues": "2 low-severity findings",
            "gas_efficiency": "Optimized for current gas prices",
            "recommendations": ["Add more unit tests", "Consider upgrade mechanism"]
        })
    
    elif agent_name == "widget-specialist":
        base_response.update({
            "widget_recommendation": "DexScreener for price analysis",
            "interaction_type": "Real-time chart analysis",
            "user_guidance": "Focus on volume and price action",
            "additional_tools": "Consider Jupiter for execution"
        })
    
    return base_response


def _extract_specializations(prompt: str) -> List[str]:
    """Extract key specializations from a sub-agent prompt."""
    # Simple extraction based on common patterns
    specializations = []
    
    if "cryptocurrency analysis" in prompt.lower():
        specializations.append("Cryptocurrency Analysis")
    if "trading" in prompt.lower():
        specializations.append("Trading Strategies")
    if "defi" in prompt.lower():
        specializations.append("DeFi Protocols")
    if "research" in prompt.lower():
        specializations.append("Market Research")
    if "smart contract" in prompt.lower():
        specializations.append("Smart Contract Analysis")
    if "widget" in prompt.lower():
        specializations.append("Widget Integration")
    
    return specializations if specializations else ["General Analysis"]


# List of all delegation tools
DELEGATION_TOOLS = [
    delegate_to_subagent,
    list_subagent_capabilities,
    get_delegation_status
]
