import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON>s, Tooltip, ResponsiveContainer, CartesianGrid, ComposedChart, Bar } from 'recharts';
import { BarChart2, TrendingUp } from 'lucide-react';
import clsx from 'clsx';

interface PriceChartProps {
  data: Array<{
    date: number;
    value: number;
    open?: number;
    close?: number;
    high?: number;
    low?: number;
  }>;
  title?: string;
  subtitle?: string;
  height?: string | number;
  isLoading?: boolean;
}

type ChartType = 'line' | 'candlestick';

function formatChartPrice(value: number) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 6,
  }).format(value);
}

function formatDate(timestamp: number) {
  const date = new Date(timestamp);
  return date.toLocaleDateString(undefined, {
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

export function PriceChart({ data, title = 'Price Chart', subtitle = 'Live Data', height = '300px', isLoading = false }: PriceChartProps) {
  const [chartType, setChartType] = React.useState<ChartType>('line');

  return (
    <div className="bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <span className="w-1 h-1 bg-[#22c55e] rounded-full" />
          <h4 className="font-medium">{title}</h4>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 bg-[#111] rounded-lg p-1">
            <button
              onClick={() => setChartType('line')}
              className={clsx(
                "p-1.5 rounded-md transition-all duration-200",
                chartType === 'line' ? "bg-[#222] text-white" : "text-[#666] hover:text-white"
              )}
            >
              <TrendingUp size={14} />
            </button>
            <button
              onClick={() => setChartType('candlestick')}
              className={clsx(
                "p-1.5 rounded-md transition-all duration-200",
                chartType === 'candlestick' ? "bg-[#222] text-white" : "text-[#666] hover:text-white"
              )}
            >
              <BarChart2 size={14} />
            </button>
          </div>
          <span className="text-xs text-[#666]">{subtitle}</span>
        </div>
      </div>
      <div style={{ height }} className="w-full">
        {isLoading ? (
          <div className="h-full flex items-center justify-center text-[#666]">
            Loading price data...
          </div>
        ) : !data.length ? (
          <div className="h-full flex items-center justify-center text-[#666]">
            No price data available
          </div>
        ) : chartType === 'line' ? (
          <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid 
              strokeDasharray="3 3" 
              vertical={false} 
              stroke="#222"
            />
            <XAxis 
              dataKey="date" 
              stroke="#666" 
              fontSize={12} 
              tickLine={false} 
              axisLine={false}
              tickFormatter={(value) => formatDate(value)}
              tickMargin={8}
            />
            <YAxis 
              stroke="#666" 
              fontSize={12} 
              tickLine={false} 
              axisLine={false}
              tickFormatter={(value) => formatChartPrice(value)}
              domain={['auto', 'auto']}
              width={80}
            />
            <Tooltip
              contentStyle={{ 
                background: '#0A0A0A', 
                border: '1px solid #222', 
                borderRadius: '12px', 
                boxShadow: '0 8px 16px -4px rgb(0 0 0 / 0.5)' 
              }}
              labelStyle={{ color: '#666', fontSize: '12px', fontWeight: 500 }}
              formatter={(value) => [formatChartPrice(Number(value)), 'Price']}
              labelFormatter={(label) => formatDate(Number(label))}
              cursor={{ stroke: '#22c55e', strokeWidth: 1, strokeDasharray: '4 4' }}
            />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke="#22c55e" 
              strokeWidth={2} 
              dot={false}
              isAnimationActive={true}
              animationDuration={750}
              animationEasing="ease-in-out"
              activeDot={{ 
                r: 6,
                fill: '#22c55e',
                strokeWidth: 2,
                stroke: '#0A0A0A'
              }}
            />
          </LineChart>
        </ResponsiveContainer>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={data}>
            <CartesianGrid 
              strokeDasharray="3 3" 
              vertical={false} 
              stroke="#222"
            />
            <XAxis 
              dataKey="date" 
              stroke="#666" 
              fontSize={12} 
              tickLine={false} 
              axisLine={false}
              tickFormatter={(value) => formatDate(value)}
              tickMargin={8}
            />
            <YAxis 
              stroke="#666" 
              fontSize={12} 
              tickLine={false} 
              axisLine={false}
              tickFormatter={(value) => formatChartPrice(value)}
              domain={['auto', 'auto']}
              width={80}
            />
            <Tooltip
              contentStyle={{ 
                background: '#0A0A0A', 
                border: '1px solid #222', 
                borderRadius: '12px', 
                boxShadow: '0 8px 16px -4px rgb(0 0 0 / 0.5)' 
              }}
              labelStyle={{ color: '#666', fontSize: '12px', fontWeight: 500 }}
              formatter={(value, name) => {
                if (name === 'value') return [formatChartPrice(Number(value)), 'Price'];
                if (name === 'high') return [formatChartPrice(Number(value)), 'High'];
                if (name === 'low') return [formatChartPrice(Number(value)), 'Low'];
                if (name === 'open') return [formatChartPrice(Number(value)), 'Open'];
                if (name === 'close') return [formatChartPrice(Number(value)), 'Close'];
                return [value, name];
              }}
              labelFormatter={(label) => formatDate(Number(label))}
              cursor={{ stroke: '#22c55e', strokeWidth: 1, strokeDasharray: '4 4' }}
            />
            <Bar
              dataKey="value"
              fill="#22c55e"
              stroke="#22c55e"
              isAnimationActive={true}
              animationDuration={750}
              animationEasing="ease-in-out"
              shape={(props: any) => {
                const { x, y, width, height } = props;
                const isPositive = height >= 0;
                return (
                  <rect
                    x={x - width / 2}
                    y={isPositive ? y : y + height}
                    width={width}
                    height={Math.abs(height)}
                    fill={isPositive ? '#22c55e' : '#ef4444'}
                  />
                );
              }}
            />
          </ComposedChart>
        </ResponsiveContainer>
        )}
      </div>
    </div>
  );
}