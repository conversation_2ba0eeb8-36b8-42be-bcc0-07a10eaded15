# Real-time WebSocket Integration Guide

## Overview

This guide explains how to integrate your existing WebSocket trading system with the enhanced PumpFun widget to display live trading data alongside static token information.

## Architecture

```
WebSocket Trades → Token Processing → Real-time Data → PumpFun Widget
     ↓                    ↓                ↓              ↓
Live Trades         Aggregated        Formatted      Enhanced Display
from pump.fun       Token Metrics     Real-time      with Live Data
                                      Data Map
```

## Integration Steps

### 1. Import Your Existing Hooks

```typescript
// In your main component
import { useWebSocketTrades, type Trade } from './use-websocket-trades'
import { useTokenProcessing, type TokenData } from './use-token-processing'
import { PumpFunWithRealtimeData } from './components/PumpFunWithRealtimeData'
```

### 2. Basic Integration

```typescript
export function TradingDashboard() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Your existing trading interface */}
      <div>
        <YourExistingTradingComponent />
      </div>
      
      {/* Enhanced PumpFun widget with real-time data */}
      <div>
        <PumpFunWithRealtimeData
          isActive={true}
          enableRealtimeIntegration={true}
          timeRange="60"
          solPrice={240}
          minTradeAmountFilter={10}
        />
      </div>
    </div>
  )
}
```

### 3. Advanced Integration with Shared State

```typescript
export function AdvancedTradingDashboard() {
  // Shared state between components
  const [allTrades, setAllTrades] = useState<Trade[]>([])
  const [tokens, setTokens] = useState<Map<string, TokenData>>(new Map())
  const [renderKey, setRenderKey] = useState(0)
  const [timeRange, setTimeRange] = useState('60')
  const [solPrice, setSolPrice] = useState(240)

  // WebSocket connection
  const { isConnected } = useWebSocketTrades(setAllTrades)

  // Token processing
  useTokenProcessing({
    allTrades,
    timeRange,
    solPrice,
    minTradeAmountFilter: 10,
    setTokens,
    setRenderKey
  })

  // Convert to real-time format
  const realtimeData = useMemo(() => {
    const realtimeMap = new Map<string, RealtimeTokenData>()
    tokens.forEach((tokenData, mint) => {
      realtimeMap.set(mint, {
        mint: tokenData.mint,
        volume: tokenData.total_volume,
        trader_count: tokenData.unique_trader_count,
        buy_sell_ratio: tokenData.buy_sell_ratio,
        last_trade_time: tokenData.last_trade_time
      })
    })
    return realtimeMap
  }, [tokens])

  return (
    <div className="space-y-6">
      {/* Connection status */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Trading Dashboard</h1>
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-[#666]">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Your existing components */}
        <YourTradingInterface 
          trades={allTrades}
          tokens={tokens}
          isConnected={isConnected}
        />
        
        {/* Enhanced PumpFun widget */}
        <PumpFunWidget
          isActive={true}
          realtimeData={realtimeData}
          enableRealtimeIntegration={true}
        />
      </div>
    </div>
  )
}
```

## Real-time Data Features

### Enhanced Token Display

When real-time integration is enabled, the PumpFun widget shows:

1. **Live Volume**: Real-time trading volume with 🔴 indicator
2. **Live Traders**: Current active trader count with 🔴 indicator  
3. **Buy/Sell Ratio**: Real-time buy vs sell percentage
4. **Live Status**: Visual indicators for real-time data

### Visual Indicators

- **🔴 Live Indicator**: Animated red dot for real-time data
- **Color Coding**: 
  - Orange for live volume
  - Purple for live trader count
  - Green for buy-heavy ratios
  - Red for sell-heavy ratios

## Data Flow

### 1. WebSocket → Trades
```typescript
// Your existing WebSocket hook
const { isConnected } = useWebSocketTrades(setAllTrades)
```

### 2. Trades → Token Metrics
```typescript
// Your existing token processing
useTokenProcessing({
  allTrades,
  timeRange,
  solPrice,
  minTradeAmountFilter,
  setTokens,
  setRenderKey
})
```

### 3. Token Metrics → Real-time Format
```typescript
const realtimeData = useMemo(() => {
  const realtimeMap = new Map<string, RealtimeTokenData>()
  tokens.forEach((tokenData, mint) => {
    realtimeMap.set(mint, {
      mint: tokenData.mint,
      volume: tokenData.total_volume,
      trader_count: tokenData.unique_trader_count,
      buy_sell_ratio: tokenData.buy_sell_ratio,
      last_trade_time: tokenData.last_trade_time
    })
  })
  return realtimeMap
}, [tokens])
```

### 4. Real-time Data → Enhanced Display
```typescript
<PumpFunWidget
  isActive={true}
  realtimeData={realtimeData}
  enableRealtimeIntegration={true}
/>
```

## Configuration Options

### PumpFunWithRealtimeData Props

```typescript
interface PumpFunWithRealtimeDataProps {
  isActive: boolean                    // Widget active state
  enableRealtimeIntegration?: boolean  // Enable real-time features
  timeRange?: string                   // Time range in minutes
  solPrice?: number                    // Current SOL price
  minTradeAmountFilter?: number        // Minimum trade amount filter
}
```

### RealtimeTokenData Interface

```typescript
interface RealtimeTokenData {
  mint: string           // Token mint address
  volume: number         // Trading volume in SOL
  trader_count: number   // Number of unique traders
  buy_sell_ratio: number // Buy ratio (0-1)
  last_trade_time: number // Last trade timestamp
}
```

## Performance Considerations

1. **Data Updates**: Real-time data updates trigger re-renders only when necessary
2. **Memory Management**: Old trades are automatically filtered based on time range
3. **Efficient Mapping**: Uses Map data structures for O(1) lookups
4. **Memoization**: Real-time data conversion is memoized to prevent unnecessary recalculations

## Testing

### Development Mode
- Debug panel shows real-time data statistics
- Mock data available for testing without WebSocket connection
- Console logging for connection status and data flow

### Production Mode
- Debug panels are automatically hidden
- Optimized performance with minimal logging
- Graceful fallback when real-time data is unavailable

## Best Practices

1. **Gradual Integration**: Start with `enableRealtimeIntegration={false}` and enable after testing
2. **Error Handling**: Implement proper error boundaries around WebSocket components
3. **Performance Monitoring**: Monitor re-render frequency and memory usage
4. **User Feedback**: Provide clear visual indicators for connection status
5. **Fallback Strategy**: Ensure the widget works without real-time data

## Troubleshooting

### Common Issues

1. **No Real-time Data**: Check WebSocket connection status
2. **Performance Issues**: Verify time range filters and data cleanup
3. **Missing Indicators**: Ensure `enableRealtimeIntegration={true}`
4. **Stale Data**: Check if token processing is running correctly

### Debug Steps

1. Check browser console for WebSocket connection logs
2. Verify `realtimeData` Map contains expected tokens
3. Confirm `enableRealtimeIntegration` prop is true
4. Test with mock data first before live WebSocket integration
