# Chad GPT LangGraph Agent environment
# Copy to .env and fill in values

# OpenRouter (required for LLM)
# You can set either OPENROUTER_API_KEY or VITE_OPENROUTER_API_KEY (frontend env). The agent will use either.
OPENROUTER_API_KEY=
VITE_OPENROUTER_API_KEY=
# Optional explicit override for OpenAI-compatible clients
OPENAI_API_KEY=
# Both variables are supported by different OpenAI client versions
OPENAI_BASE_URL=https://openrouter.ai/api/v1
OPENAI_API_BASE=https://openrouter.ai/api/v1

# Model selection
MODEL_NAME=moonshotai/kimi-k2
PORT=8001

# Moralis (required for MCP blockchain tools)
MORALIS_API_KEY=

