"""
Conversation Manager - Complete context awareness for Chad GPT Deep Agent.
Maintains conversation history, user preferences, and cross-session context.
"""

import json
import asyncio
import aiofiles
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import uuid
import os


@dataclass
class ConversationMessage:
    """Represents a single message in a conversation."""
    id: str
    thread_id: str
    role: str  # 'user', 'assistant', 'system'
    content: str
    image: Optional[str]
    timestamp: datetime
    metadata: Dict[str, Any]
    tool_calls: List[Dict[str, Any]]
    widget_updates: List[Dict[str, Any]]


@dataclass
class ConversationThread:
    """Represents a complete conversation thread."""
    thread_id: str
    user_id: Optional[str]
    title: str
    created_at: datetime
    last_updated: datetime
    message_count: int
    active_widgets: List[str]
    workflow_ids: List[str]
    user_preferences: Dict[str, Any]
    context_summary: str
    metadata: Dict[str, Any]


class ConversationManager:
    """
    Manages all conversation state, history, and context for the Deep Agent system.
    Provides complete context awareness across sessions and conversations.
    """

    def __init__(self, storage_path: str = "agent_workspace/conversations"):
        """Initialize conversation manager."""
        self.storage_path = storage_path
        self.active_threads: Dict[str, ConversationThread] = {}
        self.message_cache: Dict[str, List[ConversationMessage]] = {}
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        self.context_summaries: Dict[str, str] = {}
        
        # Ensure storage directory exists
        os.makedirs(storage_path, exist_ok=True)
        
        # Load existing data
        asyncio.create_task(self._load_existing_data())
    
    async def _load_existing_data(self):
        """Load existing conversation data from storage."""
        try:
            # Load conversation threads
            threads_file = f"{self.storage_path}/threads.json"
            if os.path.exists(threads_file):
                async with aiofiles.open(threads_file, 'r') as f:
                    content = await f.read()
                    threads_data = json.loads(content)
                    
                    for thread_id, thread_data in threads_data.items():
                        thread_data['created_at'] = datetime.fromisoformat(thread_data['created_at'])
                        thread_data['last_updated'] = datetime.fromisoformat(thread_data['last_updated'])
                        self.active_threads[thread_id] = ConversationThread(**thread_data)
            
            # Load user preferences
            prefs_file = f"{self.storage_path}/user_preferences.json"
            if os.path.exists(prefs_file):
                async with aiofiles.open(prefs_file, 'r') as f:
                    content = await f.read()
                    self.user_preferences = json.loads(content)
            
            # Load context summaries
            summaries_file = f"{self.storage_path}/context_summaries.json"
            if os.path.exists(summaries_file):
                async with aiofiles.open(summaries_file, 'r') as f:
                    content = await f.read()
                    self.context_summaries = json.loads(content)
                    
        except Exception as e:
            print(f"Error loading conversation data: {e}")
    
    async def update_conversation(
        self,
        thread_id: str,
        message: str,
        context: Dict[str, Any],
        image: Optional[str] = None
    ):
        """Update conversation with new message and context."""
        now = datetime.now()
        
        # Get or create conversation thread
        if thread_id not in self.active_threads:
            self.active_threads[thread_id] = ConversationThread(
                thread_id=thread_id,
                user_id=context.get("user_id"),
                title=self._generate_thread_title(message),
                created_at=now,
                last_updated=now,
                message_count=0,
                active_widgets=[],
                workflow_ids=[],
                user_preferences=context.get("user_context", {}).get("preferences", {}),
                context_summary="",
                metadata=context.get("user_context", {})
            )
        
        thread = self.active_threads[thread_id]
        
        # Update thread
        thread.last_updated = now
        thread.message_count += 1
        thread.metadata.update(context.get("user_context", {}))
        
        # Create message
        message_obj = ConversationMessage(
            id=str(uuid.uuid4()),
            thread_id=thread_id,
            role="user",
            content=message,
            image=image,
            timestamp=now,
            metadata=context,
            tool_calls=[],
            widget_updates=[]
        )
        
        # Add to message cache
        if thread_id not in self.message_cache:
            self.message_cache[thread_id] = []
        
        self.message_cache[thread_id].append(message_obj)
        
        # Trim message cache if too long (keep last 100 messages)
        if len(self.message_cache[thread_id]) > 100:
            self.message_cache[thread_id] = self.message_cache[thread_id][-100:]
        
        # Update context summary
        await self._update_context_summary(thread_id)
        
        # Save to persistent storage
        await self._save_thread_data(thread_id)
    
    async def add_assistant_message(
        self,
        thread_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        widget_updates: Optional[List[Dict[str, Any]]] = None
    ):
        """Add an assistant message to the conversation."""
        if thread_id not in self.message_cache:
            self.message_cache[thread_id] = []
        
        message_obj = ConversationMessage(
            id=str(uuid.uuid4()),
            thread_id=thread_id,
            role="assistant",
            content=content,
            image=None,
            timestamp=datetime.now(),
            metadata=metadata or {},
            tool_calls=tool_calls or [],
            widget_updates=widget_updates or []
        )
        
        self.message_cache[thread_id].append(message_obj)
        
        # Update thread
        if thread_id in self.active_threads:
            self.active_threads[thread_id].last_updated = datetime.now()
        
        # Update context summary
        await self._update_context_summary(thread_id)
        
        # Save to persistent storage
        await self._save_thread_data(thread_id)
    
    async def get_conversation_history(
        self,
        thread_id: str,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get conversation history for a thread."""
        if thread_id not in self.message_cache:
            # Try to load from storage
            await self._load_thread_messages(thread_id)
        
        messages = self.message_cache.get(thread_id, [])
        if limit:
            messages = messages[-limit:]
        
        return [
            {
                "id": msg.id,
                "role": msg.role,
                "content": msg.content,
                "image": msg.image,
                "timestamp": msg.timestamp.isoformat(),
                "metadata": msg.metadata,
                "tool_calls": msg.tool_calls,
                "widget_updates": msg.widget_updates
            }
            for msg in messages
        ]
    
    async def get_conversation(self, thread_id: str) -> Dict[str, Any]:
        """Get complete conversation data for a thread."""
        thread = self.active_threads.get(thread_id)
        history = await self.get_conversation_history(thread_id)
        context_summary = self.context_summaries.get(thread_id, "")
        
        return {
            "thread_id": thread_id,
            "thread_info": asdict(thread) if thread else None,
            "message_history": history,
            "context_summary": context_summary,
            "message_count": len(history)
        }
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences."""
        return self.user_preferences.get(user_id, {})
    
    async def update_user_preferences(
        self,
        user_id: str,
        preferences: Dict[str, Any]
    ):
        """Update user preferences."""
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = {}
        
        self.user_preferences[user_id].update(preferences)
        
        # Update all active threads for this user
        for thread in self.active_threads.values():
            if thread.user_id == user_id:
                thread.user_preferences.update(preferences)
        
        # Save preferences
        await self._save_user_preferences()
    
    async def add_widget_to_thread(self, thread_id: str, widget_id: str):
        """Add an active widget to a conversation thread."""
        if thread_id in self.active_threads:
            thread = self.active_threads[thread_id]
            if widget_id not in thread.active_widgets:
                thread.active_widgets.append(widget_id)
                thread.last_updated = datetime.now()
                await self._save_thread_data(thread_id)
    
    async def remove_widget_from_thread(self, thread_id: str, widget_id: str):
        """Remove a widget from a conversation thread."""
        if thread_id in self.active_threads:
            thread = self.active_threads[thread_id]
            if widget_id in thread.active_widgets:
                thread.active_widgets.remove(widget_id)
                thread.last_updated = datetime.now()
                await self._save_thread_data(thread_id)
    
    async def add_workflow_to_thread(self, thread_id: str, workflow_id: str):
        """Add a workflow to a conversation thread."""
        if thread_id in self.active_threads:
            thread = self.active_threads[thread_id]
            if workflow_id not in thread.workflow_ids:
                thread.workflow_ids.append(workflow_id)
                thread.last_updated = datetime.now()
                await self._save_thread_data(thread_id)
    
    async def get_active_threads(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of active conversation threads."""
        threads = []
        
        for thread_id, thread in self.active_threads.items():
            if user_id is None or thread.user_id == user_id:
                # Get recent messages for preview
                recent_messages = await self.get_conversation_history(thread_id, limit=3)
                
                thread_data = asdict(thread)
                thread_data['created_at'] = thread.created_at.isoformat()
                thread_data['last_updated'] = thread.last_updated.isoformat()
                thread_data['recent_messages'] = recent_messages
                thread_data['context_summary'] = self.context_summaries.get(thread_id, "")
                
                threads.append(thread_data)
        
        # Sort by last updated (most recent first)
        threads.sort(key=lambda x: x["last_updated"], reverse=True)
        
        return threads
    
    async def get_active_conversation_count(self) -> int:
        """Get count of active conversations."""
        return len(self.active_threads)
    
    async def _update_context_summary(self, thread_id: str):
        """Update context summary for a thread."""
        messages = self.message_cache.get(thread_id, [])
        if len(messages) < 3:
            return
        
        # Get last few messages for context
        recent_messages = messages[-5:]
        
        # Create a simple context summary
        topics = []
        tools_used = []
        widgets_mentioned = []
        
        for msg in recent_messages:
            # Extract topics from content
            if len(msg.content) > 20:
                topics.append(msg.content[:50] + "...")
            
            # Track tool calls
            for tool_call in msg.tool_calls:
                if tool_call.get("name") not in tools_used:
                    tools_used.append(tool_call.get("name"))
            
            # Track widget updates
            for widget_update in msg.widget_updates:
                widget_type = widget_update.get("widget_type")
                if widget_type and widget_type not in widgets_mentioned:
                    widgets_mentioned.append(widget_type)
        
        # Create summary
        summary_parts = []
        if topics:
            summary_parts.append(f"Recent topics: {', '.join(topics[:3])}")
        if tools_used:
            summary_parts.append(f"Tools used: {', '.join(tools_used[:3])}")
        if widgets_mentioned:
            summary_parts.append(f"Widgets: {', '.join(widgets_mentioned)}")
        
        self.context_summaries[thread_id] = "; ".join(summary_parts)
    
    def _generate_thread_title(self, first_message: str) -> str:
        """Generate a title for a conversation thread."""
        # Simple title generation from first message
        title = first_message[:50].strip()
        if len(first_message) > 50:
            title += "..."
        return title or "New Conversation"
    
    async def _save_thread_data(self, thread_id: str):
        """Save thread data to persistent storage."""
        try:
            # Save thread info
            threads_data = {}
            for tid, thread in self.active_threads.items():
                thread_data = asdict(thread)
                thread_data['created_at'] = thread.created_at.isoformat()
                thread_data['last_updated'] = thread.last_updated.isoformat()
                threads_data[tid] = thread_data
            
            threads_file = f"{self.storage_path}/threads.json"
            async with aiofiles.open(threads_file, 'w') as f:
                await f.write(json.dumps(threads_data, indent=2))
            
            # Save messages for this thread
            if thread_id in self.message_cache:
                messages_data = []
                for msg in self.message_cache[thread_id]:
                    msg_data = asdict(msg)
                    msg_data['timestamp'] = msg.timestamp.isoformat()
                    messages_data.append(msg_data)
                
                thread_file = f"{self.storage_path}/thread_{thread_id}.json"
                async with aiofiles.open(thread_file, 'w') as f:
                    await f.write(json.dumps(messages_data, indent=2))
            
            # Save context summaries
            summaries_file = f"{self.storage_path}/context_summaries.json"
            async with aiofiles.open(summaries_file, 'w') as f:
                await f.write(json.dumps(self.context_summaries, indent=2))
                
        except Exception as e:
            print(f"Error saving thread data: {e}")
    
    async def _save_user_preferences(self):
        """Save user preferences to storage."""
        try:
            prefs_file = f"{self.storage_path}/user_preferences.json"
            async with aiofiles.open(prefs_file, 'w') as f:
                await f.write(json.dumps(self.user_preferences, indent=2))
        except Exception as e:
            print(f"Error saving user preferences: {e}")
    
    async def _load_thread_messages(self, thread_id: str):
        """Load messages for a specific thread."""
        try:
            thread_file = f"{self.storage_path}/thread_{thread_id}.json"
            if os.path.exists(thread_file):
                async with aiofiles.open(thread_file, 'r') as f:
                    content = await f.read()
                    messages_data = json.loads(content)
                
                messages = []
                for msg_data in messages_data:
                    msg_data['timestamp'] = datetime.fromisoformat(msg_data['timestamp'])
                    messages.append(ConversationMessage(**msg_data))
                
                self.message_cache[thread_id] = messages
        except Exception as e:
            print(f"Error loading thread messages: {e}")
    
    async def cleanup_old_threads(self, days_old: int = 30):
        """Clean up old conversation threads."""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        threads_to_remove = []
        for thread_id, thread in self.active_threads.items():
            if thread.last_updated < cutoff_date:
                threads_to_remove.append(thread_id)
        
        for thread_id in threads_to_remove:
            del self.active_threads[thread_id]
            if thread_id in self.message_cache:
                del self.message_cache[thread_id]
            if thread_id in self.context_summaries:
                del self.context_summaries[thread_id]
            
            # Remove thread file
            thread_file = f"{self.storage_path}/thread_{thread_id}.json"
            if os.path.exists(thread_file):
                os.remove(thread_file)
    
    async def cleanup(self):
        """Cleanup conversation manager resources."""
        # Save all data
        for thread_id in self.active_threads.keys():
            await self._save_thread_data(thread_id)
        
        await self._save_user_preferences()
        
        # Cleanup old threads
        await self.cleanup_old_threads()
