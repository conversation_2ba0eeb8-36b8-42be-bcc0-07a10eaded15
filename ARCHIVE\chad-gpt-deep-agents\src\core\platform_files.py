"""
Platform file management system for Chad GPT Deep Agents.
Provides persistent storage, caching, and file operations for the agent system.
"""

import os
import json
import asyncio
import aiofiles
import hashlib
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path
import mimetypes
import shutil


class PlatformFileManager:
    """
    Manages file operations, caching, and persistent storage for the platform.
    Provides a unified interface for agent file system operations.
    """

    def __init__(self, workspace_root: str = "agent_workspace"):
        """Initialize file manager with workspace configuration."""
        self.workspace_root = Path(workspace_root)
        self.cache_dir = self.workspace_root / "cache"
        self.temp_dir = self.workspace_root / "temp"
        
        # File operation statistics
        self.stats = {
            "files_created": 0,
            "files_read": 0,
            "files_updated": 0,
            "files_deleted": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        # Cache configuration
        self.cache_config = {
            "max_size_mb": 100,
            "max_age_hours": 24,
            "cleanup_interval_minutes": 30
        }
        
        # File type handlers
        self.file_handlers = {
            ".json": self._handle_json_file,
            ".txt": self._handle_text_file,
            ".md": self._handle_markdown_file,
            ".csv": self._handle_csv_file,
            ".log": self._handle_log_file
        }
        
        # Initialize workspace
        asyncio.create_task(self._initialize_workspace())
        
        # Start background tasks
        asyncio.create_task(self._start_background_tasks())
    
    async def _initialize_workspace(self):
        """Initialize workspace directory structure."""
        directories = [
            self.workspace_root,
            self.workspace_root / "analysis",
            self.workspace_root / "conversations",
            self.workspace_root / "data",
            self.workspace_root / "reports",
            self.workspace_root / "research",
            self.workspace_root / "plans",
            self.workspace_root / "widgets",
            self.workspace_root / "temp",
            self.cache_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Create workspace info file
        workspace_info = {
            "created_at": datetime.now().isoformat(),
            "version": "1.0.0",
            "description": "Chad GPT Deep Agent Workspace",
            "directories": [str(d.relative_to(self.workspace_root)) for d in directories]
        }
        
        info_file = self.workspace_root / "workspace_info.json"
        if not info_file.exists():
            async with aiofiles.open(info_file, 'w') as f:
                await f.write(json.dumps(workspace_info, indent=2))
    
    async def _start_background_tasks(self):
        """Start background maintenance tasks."""
        # Cache cleanup task
        asyncio.create_task(self._cache_cleanup_loop())
        
        # Temp file cleanup task
        asyncio.create_task(self._temp_cleanup_loop())
    
    async def write_file(
        self,
        path: str,
        content: Union[str, bytes, Dict[str, Any]],
        encoding: str = "utf-8",
        create_dirs: bool = True
    ) -> Dict[str, Any]:
        """
        Write content to a file in the workspace.
        
        Args:
            path: File path relative to workspace root
            content: Content to write (string, bytes, or dict for JSON)
            encoding: File encoding for text files
            create_dirs: Whether to create parent directories
            
        Returns:
            Operation result with file information
        """
        try:
            file_path = self.workspace_root / path
            
            # Create parent directories if needed
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Handle different content types
            if isinstance(content, dict):
                content = json.dumps(content, indent=2, ensure_ascii=False)
                encoding = "utf-8"
            
            # Write file
            if isinstance(content, bytes):
                async with aiofiles.open(file_path, 'wb') as f:
                    await f.write(content)
                bytes_written = len(content)
            else:
                async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                    await f.write(content)
                bytes_written = len(content.encode(encoding))
            
            # Update statistics
            if file_path.exists():
                self.stats["files_updated"] += 1
            else:
                self.stats["files_created"] += 1
            
            # Get file info
            file_info = await self._get_file_info(file_path)
            
            return {
                "success": True,
                "path": path,
                "absolute_path": str(file_path),
                "bytes_written": bytes_written,
                "encoding": encoding,
                "file_info": file_info,
                "message": f"Successfully wrote {bytes_written} bytes to {path}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "path": path,
                "error": str(e),
                "message": f"Failed to write file {path}: {str(e)}"
            }
    
    async def read_file(
        self,
        path: str,
        encoding: str = "utf-8",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Read content from a file in the workspace.
        
        Args:
            path: File path relative to workspace root
            encoding: File encoding for text files
            use_cache: Whether to use cached content if available
            
        Returns:
            File content and metadata
        """
        try:
            file_path = self.workspace_root / path
            
            if not file_path.exists():
                return {
                    "success": False,
                    "path": path,
                    "error": "File not found",
                    "message": f"File {path} does not exist"
                }
            
            # Check cache first
            if use_cache:
                cached_content = await self._get_cached_content(file_path)
                if cached_content:
                    self.stats["cache_hits"] += 1
                    return {
                        "success": True,
                        "path": path,
                        "content": cached_content["content"],
                        "encoding": encoding,
                        "file_info": cached_content["file_info"],
                        "cached": True,
                        "message": f"Retrieved {path} from cache"
                    }
            
            self.stats["cache_misses"] += 1
            
            # Read file
            file_extension = file_path.suffix.lower()
            
            if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip']:
                # Binary file
                async with aiofiles.open(file_path, 'rb') as f:
                    content = await f.read()
                encoding = None
            else:
                # Text file
                async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                    content = await f.read()
            
            # Get file info
            file_info = await self._get_file_info(file_path)
            
            # Cache content if it's a text file and not too large
            if isinstance(content, str) and len(content) < 1024 * 1024:  # 1MB limit
                await self._cache_content(file_path, content, file_info)
            
            # Update statistics
            self.stats["files_read"] += 1
            
            return {
                "success": True,
                "path": path,
                "content": content,
                "encoding": encoding,
                "file_info": file_info,
                "cached": False,
                "message": f"Successfully read {path}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "path": path,
                "error": str(e),
                "message": f"Failed to read file {path}: {str(e)}"
            }
    
    async def list_files(
        self,
        path: str = "",
        recursive: bool = False,
        pattern: Optional[str] = None,
        include_hidden: bool = False
    ) -> Dict[str, Any]:
        """
        List files and directories in the workspace.
        
        Args:
            path: Directory path relative to workspace root
            recursive: Whether to list recursively
            pattern: File pattern to match (glob style)
            include_hidden: Whether to include hidden files
            
        Returns:
            List of files and directories with metadata
        """
        try:
            dir_path = self.workspace_root / path if path else self.workspace_root
            
            if not dir_path.exists():
                return {
                    "success": False,
                    "path": path,
                    "error": "Directory not found",
                    "message": f"Directory {path} does not exist"
                }
            
            if not dir_path.is_dir():
                return {
                    "success": False,
                    "path": path,
                    "error": "Not a directory",
                    "message": f"{path} is not a directory"
                }
            
            files = []
            directories = []
            
            if recursive:
                items = dir_path.rglob(pattern or "*")
            else:
                items = dir_path.glob(pattern or "*")
            
            for item in items:
                # Skip hidden files unless requested
                if not include_hidden and item.name.startswith('.'):
                    continue
                
                # Skip cache and temp directories in listings
                if any(part in ['.cache', 'temp', '__pycache__'] for part in item.parts):
                    continue
                
                relative_path = item.relative_to(self.workspace_root)
                
                if item.is_file():
                    file_info = await self._get_file_info(item)
                    files.append({
                        "name": item.name,
                        "path": str(relative_path),
                        "type": "file",
                        "size": file_info["size"],
                        "modified": file_info["modified"],
                        "extension": item.suffix,
                        "mime_type": file_info["mime_type"]
                    })
                elif item.is_dir():
                    # Count items in directory
                    try:
                        item_count = len(list(item.iterdir()))
                    except PermissionError:
                        item_count = 0
                    
                    directories.append({
                        "name": item.name,
                        "path": str(relative_path),
                        "type": "directory",
                        "items": item_count,
                        "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
                    })
            
            # Sort files and directories
            files.sort(key=lambda x: x["name"].lower())
            directories.sort(key=lambda x: x["name"].lower())
            
            return {
                "success": True,
                "path": path,
                "files": files,
                "directories": directories,
                "total_files": len(files),
                "total_directories": len(directories),
                "message": f"Listed {len(files)} files and {len(directories)} directories"
            }
            
        except Exception as e:
            return {
                "success": False,
                "path": path,
                "error": str(e),
                "message": f"Failed to list directory {path}: {str(e)}"
            }
    
    async def delete_file(self, path: str) -> Dict[str, Any]:
        """
        Delete a file or directory from the workspace.
        
        Args:
            path: File or directory path relative to workspace root
            
        Returns:
            Operation result
        """
        try:
            file_path = self.workspace_root / path
            
            if not file_path.exists():
                return {
                    "success": False,
                    "path": path,
                    "error": "File not found",
                    "message": f"File {path} does not exist"
                }
            
            # Get file info before deletion
            file_info = await self._get_file_info(file_path)
            
            # Delete file or directory
            if file_path.is_file():
                file_path.unlink()
                self.stats["files_deleted"] += 1
            elif file_path.is_dir():
                shutil.rmtree(file_path)
                self.stats["files_deleted"] += 1
            
            # Remove from cache
            await self._remove_from_cache(file_path)
            
            return {
                "success": True,
                "path": path,
                "file_info": file_info,
                "message": f"Successfully deleted {path}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "path": path,
                "error": str(e),
                "message": f"Failed to delete {path}: {str(e)}"
            }
    
    async def copy_file(self, source_path: str, dest_path: str) -> Dict[str, Any]:
        """
        Copy a file within the workspace.
        
        Args:
            source_path: Source file path relative to workspace root
            dest_path: Destination file path relative to workspace root
            
        Returns:
            Operation result
        """
        try:
            source = self.workspace_root / source_path
            dest = self.workspace_root / dest_path
            
            if not source.exists():
                return {
                    "success": False,
                    "source_path": source_path,
                    "dest_path": dest_path,
                    "error": "Source file not found",
                    "message": f"Source file {source_path} does not exist"
                }
            
            # Create destination directory if needed
            dest.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            if source.is_file():
                shutil.copy2(source, dest)
            elif source.is_dir():
                shutil.copytree(source, dest, dirs_exist_ok=True)
            
            # Get file info
            file_info = await self._get_file_info(dest)
            
            return {
                "success": True,
                "source_path": source_path,
                "dest_path": dest_path,
                "file_info": file_info,
                "message": f"Successfully copied {source_path} to {dest_path}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "source_path": source_path,
                "dest_path": dest_path,
                "error": str(e),
                "message": f"Failed to copy {source_path} to {dest_path}: {str(e)}"
            }
    
    async def get_workspace_stats(self) -> Dict[str, Any]:
        """Get workspace statistics and information."""
        try:
            # Calculate workspace size
            total_size = 0
            file_count = 0
            dir_count = 0
            
            for item in self.workspace_root.rglob("*"):
                if item.is_file():
                    total_size += item.stat().st_size
                    file_count += 1
                elif item.is_dir():
                    dir_count += 1
            
            # Get cache statistics
            cache_size = 0
            cache_files = 0
            
            if self.cache_dir.exists():
                for item in self.cache_dir.rglob("*"):
                    if item.is_file():
                        cache_size += item.stat().st_size
                        cache_files += 1
            
            return {
                "success": True,
                "workspace_root": str(self.workspace_root),
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "file_count": file_count,
                "directory_count": dir_count,
                "cache_size_bytes": cache_size,
                "cache_size_mb": round(cache_size / (1024 * 1024), 2),
                "cache_files": cache_files,
                "operation_stats": self.stats,
                "cache_config": self.cache_config
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get workspace stats: {str(e)}"
            }
    
    async def _get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Get detailed file information."""
        stat = file_path.stat()
        
        return {
            "size": stat.st_size,
            "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "accessed": datetime.fromtimestamp(stat.st_atime).isoformat(),
            "mime_type": mimetypes.guess_type(str(file_path))[0] or "application/octet-stream",
            "extension": file_path.suffix,
            "is_binary": file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip', '.exe']
        }
    
    async def _get_cached_content(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Get cached content for a file if available and valid."""
        cache_key = hashlib.md5(str(file_path).encode()).hexdigest()
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if not cache_file.exists():
            return None
        
        try:
            async with aiofiles.open(cache_file, 'r') as f:
                cache_data = json.loads(await f.read())
            
            # Check if cache is still valid
            file_modified = file_path.stat().st_mtime
            cache_modified = cache_data.get("file_modified", 0)
            
            if file_modified <= cache_modified:
                cache_age = datetime.now() - datetime.fromisoformat(cache_data["cached_at"])
                max_age = timedelta(hours=self.cache_config["max_age_hours"])
                
                if cache_age <= max_age:
                    return cache_data
            
            # Cache is invalid, remove it
            cache_file.unlink()
            return None
            
        except Exception:
            # Cache file is corrupted, remove it
            if cache_file.exists():
                cache_file.unlink()
            return None
    
    async def _cache_content(self, file_path: Path, content: str, file_info: Dict[str, Any]):
        """Cache file content for faster future access."""
        cache_key = hashlib.md5(str(file_path).encode()).hexdigest()
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        cache_data = {
            "content": content,
            "file_info": file_info,
            "file_path": str(file_path),
            "file_modified": file_path.stat().st_mtime,
            "cached_at": datetime.now().isoformat()
        }
        
        try:
            async with aiofiles.open(cache_file, 'w') as f:
                await f.write(json.dumps(cache_data))
        except Exception:
            # Ignore cache write errors
            pass
    
    async def _remove_from_cache(self, file_path: Path):
        """Remove file from cache."""
        cache_key = hashlib.md5(str(file_path).encode()).hexdigest()
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if cache_file.exists():
            cache_file.unlink()
    
    async def _cache_cleanup_loop(self):
        """Background task to clean up old cache files."""
        while True:
            try:
                await asyncio.sleep(self.cache_config["cleanup_interval_minutes"] * 60)
                await self._cleanup_cache()
            except Exception as e:
                print(f"Cache cleanup error: {e}")
    
    async def _cleanup_cache(self):
        """Clean up old and oversized cache files."""
        if not self.cache_dir.exists():
            return
        
        max_age = timedelta(hours=self.cache_config["max_age_hours"])
        max_size = self.cache_config["max_size_mb"] * 1024 * 1024
        
        cache_files = list(self.cache_dir.glob("*.json"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        # Remove old files
        now = datetime.now()
        for cache_file in cache_files:
            file_age = now - datetime.fromtimestamp(cache_file.stat().st_mtime)
            if file_age > max_age:
                cache_file.unlink()
                total_size -= cache_file.stat().st_size
        
        # Remove largest files if still over size limit
        if total_size > max_size:
            remaining_files = [f for f in cache_files if f.exists()]
            remaining_files.sort(key=lambda f: f.stat().st_size, reverse=True)
            
            for cache_file in remaining_files:
                if total_size <= max_size:
                    break
                total_size -= cache_file.stat().st_size
                cache_file.unlink()
    
    async def _temp_cleanup_loop(self):
        """Background task to clean up temporary files."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_temp_files()
            except Exception as e:
                print(f"Temp cleanup error: {e}")
    
    async def _cleanup_temp_files(self):
        """Clean up old temporary files."""
        if not self.temp_dir.exists():
            return
        
        max_age = timedelta(hours=24)  # Remove temp files older than 24 hours
        now = datetime.now()
        
        for temp_file in self.temp_dir.rglob("*"):
            if temp_file.is_file():
                file_age = now - datetime.fromtimestamp(temp_file.stat().st_mtime)
                if file_age > max_age:
                    temp_file.unlink()
    
    async def cleanup(self):
        """Cleanup file manager resources."""
        # Final cache cleanup
        await self._cleanup_cache()
        await self._cleanup_temp_files()
        
        # Save final statistics
        stats_file = self.workspace_root / "file_manager_stats.json"
        async with aiofiles.open(stats_file, 'w') as f:
            await f.write(json.dumps({
                "stats": self.stats,
                "last_cleanup": datetime.now().isoformat()
            }, indent=2))
