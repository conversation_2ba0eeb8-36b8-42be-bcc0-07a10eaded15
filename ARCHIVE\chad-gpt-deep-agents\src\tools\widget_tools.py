"""
Widget-specific tools for Chad GPT Deep Agents.
These tools provide specialized functionality for different widget types.
"""

from langchain_core.tools import tool
from typing import Dict, List, Any, Optional
import json


def get_widget_tools(widget_manager):
    """Get widget-specific tools with widget manager binding."""
    
    @tool
    def show_pumpfun_widget(
        thread_id: str,
        category: Optional[str] = "new",
        filters: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Display the PumpFun interactive widget for browsing meme tokens.
        
        Args:
            thread_id: Conversation thread identifier
            category: Token category to display (new, for-you, graduated, runners)
            filters: JSON string of additional filters to apply
            
        Returns:
            Dictionary with widget creation result
        """
        try:
            # Parse filters if provided
            filter_data = json.loads(filters) if filters else {}
            
            # Prepare initial data for PumpFun widget
            initial_data = {
                "category": category,
                "filters": filter_data,
                "auto_refresh": True,
                "refresh_interval": 30
            }
            
            # Create widget
            import asyncio
            widget_id = asyncio.run(widget_manager.create_widget(
                widget_type="pumpfun",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={"source": "agent_request"}
            ))
            
            return {
                "success": True,
                "widget_type": "pumpfun",
                "widget_id": widget_id,
                "category": category,
                "message": f"Displaying PumpFun widget with {category} tokens"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create PumpFun widget"
            }
    
    @tool
    def show_token_chart(
        thread_id: str,
        token_address: str,
        chain: Optional[str] = "solana",
        chart_type: Optional[str] = "gmgn"
    ) -> Dict[str, Any]:
        """
        Display a token chart widget for price analysis.
        
        Args:
            thread_id: Conversation thread identifier
            token_address: Contract address of the token
            chain: Blockchain network (solana, ethereum, bsc, etc.)
            chart_type: Chart provider (gmgn, dexscreener)
            
        Returns:
            Dictionary with widget creation result
        """
        try:
            # Prepare initial data for token chart
            initial_data = {
                "token_address": token_address,
                "chain": chain,
                "chart_provider": chart_type,
                "timeframe": "1h",
                "auto_refresh": True
            }
            
            # Create widget
            import asyncio
            widget_id = asyncio.run(widget_manager.create_widget(
                widget_type="token_chart",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={"token_address": token_address, "chain": chain}
            ))
            
            return {
                "success": True,
                "widget_type": "token_chart",
                "widget_id": widget_id,
                "token_address": token_address,
                "chain": chain,
                "message": f"Displaying {chain} token chart for {token_address}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create token chart for {token_address}"
            }
    
    @tool
    def show_jupiter_widget(
        thread_id: str,
        input_token: Optional[str] = "SOL",
        output_token: Optional[str] = "USDC"
    ) -> Dict[str, Any]:
        """
        Display the Jupiter swap widget for token trading.
        
        Args:
            thread_id: Conversation thread identifier
            input_token: Symbol or address of input token
            output_token: Symbol or address of output token
            
        Returns:
            Dictionary with widget creation result
        """
        try:
            # Prepare initial data for Jupiter widget
            initial_data = {
                "input_token": input_token,
                "output_token": output_token,
                "slippage": 0.5,
                "auto_route": True
            }
            
            # Create widget
            import asyncio
            widget_id = asyncio.run(widget_manager.create_widget(
                widget_type="jupiter",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={"swap_pair": f"{input_token}/{output_token}"}
            ))
            
            return {
                "success": True,
                "widget_type": "jupiter",
                "widget_id": widget_id,
                "swap_pair": f"{input_token}/{output_token}",
                "message": f"Displaying Jupiter swap widget for {input_token} to {output_token}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create Jupiter swap widget"
            }
    
    @tool
    def show_phantom_widget(thread_id: str) -> Dict[str, Any]:
        """
        Display the Phantom wallet widget for wallet management.
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            Dictionary with widget creation result
        """
        try:
            # Prepare initial data for Phantom widget
            initial_data = {
                "auto_connect": False,
                "show_balance": True,
                "show_transactions": True
            }
            
            # Create widget
            import asyncio
            widget_id = asyncio.run(widget_manager.create_widget(
                widget_type="phantom",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={"wallet_type": "phantom"}
            ))
            
            return {
                "success": True,
                "widget_type": "phantom",
                "widget_id": widget_id,
                "message": "Displaying Phantom wallet widget"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create Phantom wallet widget"
            }
    
    @tool
    def show_dexscreener_widget(
        thread_id: str,
        token_address: Optional[str] = None,
        pair_address: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Display the DexScreener widget for DEX trading data.
        
        Args:
            thread_id: Conversation thread identifier
            token_address: Token contract address to analyze
            pair_address: Specific trading pair address
            
        Returns:
            Dictionary with widget creation result
        """
        try:
            # Prepare initial data for DexScreener widget
            initial_data = {
                "token_address": token_address,
                "pair_address": pair_address,
                "view_mode": "marketcap",  # Default to market cap view
                "auto_refresh": True
            }
            
            # Create widget
            import asyncio
            widget_id = asyncio.run(widget_manager.create_widget(
                widget_type="dexscreener",
                thread_id=thread_id,
                initial_data=initial_data,
                metadata={"token_address": token_address, "pair_address": pair_address}
            ))
            
            return {
                "success": True,
                "widget_type": "dexscreener",
                "widget_id": widget_id,
                "token_address": token_address,
                "pair_address": pair_address,
                "message": "Displaying DexScreener widget for DEX trading data"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create DexScreener widget"
            }
    
    @tool
    def update_pumpfun_category(
        widget_id: str,
        category: str
    ) -> Dict[str, Any]:
        """
        Update the category displayed in a PumpFun widget.
        
        Args:
            widget_id: Unique identifier of the PumpFun widget
            category: New category to display (new, for-you, graduated, runners)
            
        Returns:
            Dictionary with update result
        """
        try:
            # Update widget data
            import asyncio
            success = asyncio.run(widget_manager.update_widget(
                widget_id=widget_id,
                data={"category": category},
                metadata={"last_category_change": category}
            ))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "new_category": category,
                    "message": f"Updated PumpFun widget to show {category} tokens"
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"PumpFun widget {widget_id} not found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update PumpFun widget category"
            }
    
    @tool
    def update_chart_timeframe(
        widget_id: str,
        timeframe: str
    ) -> Dict[str, Any]:
        """
        Update the timeframe for a token chart widget.
        
        Args:
            widget_id: Unique identifier of the chart widget
            timeframe: New timeframe (1m, 5m, 15m, 1h, 4h, 1d, 1w)
            
        Returns:
            Dictionary with update result
        """
        try:
            # Update widget data
            import asyncio
            success = asyncio.run(widget_manager.update_widget(
                widget_id=widget_id,
                data={"timeframe": timeframe},
                metadata={"last_timeframe_change": timeframe}
            ))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "new_timeframe": timeframe,
                    "message": f"Updated chart timeframe to {timeframe}"
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Chart widget {widget_id} not found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update chart timeframe"
            }
    
    @tool
    def refresh_widget_data(widget_id: str) -> Dict[str, Any]:
        """
        Force refresh data for a specific widget.
        
        Args:
            widget_id: Unique identifier of the widget to refresh
            
        Returns:
            Dictionary with refresh result
        """
        try:
            # Send refresh command to widget
            import asyncio
            success = asyncio.run(widget_manager.update_widget(
                widget_id=widget_id,
                data={"refresh_command": True},
                metadata={"last_refresh": "manual"}
            ))
            
            if success:
                return {
                    "success": True,
                    "widget_id": widget_id,
                    "message": f"Refreshed data for widget {widget_id}"
                }
            else:
                return {
                    "success": False,
                    "error": "Widget not found",
                    "message": f"Widget {widget_id} not found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to refresh widget {widget_id}"
            }
    
    return [
        show_pumpfun_widget,
        show_token_chart,
        show_jupiter_widget,
        show_phantom_widget,
        show_dexscreener_widget,
        update_pumpfun_category,
        update_chart_timeframe,
        refresh_widget_data
    ]
