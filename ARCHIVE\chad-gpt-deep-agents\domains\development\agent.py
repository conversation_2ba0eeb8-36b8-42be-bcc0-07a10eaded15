"""
Development Assistant Agent for Chad GPT Deep Agents.
Specialized agent for code generation, technical support, and widget development.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import ast
import re

from ...src.core.platform_agent_loop import PlatformAgentLoop
from ...src.core.workflow_planning import WorkflowPlanner


class DevelopmentAgent:
    """
    Specialized agent for development assistance and code generation.
    Provides comprehensive development support for the Chad GPT platform.
    """

    def __init__(self, platform_agent: PlatformAgentLoop):
        """Initialize development agent with platform integration."""
        self.platform_agent = platform_agent
        self.workflow_planner = WorkflowPlanner()
        
        # Development frameworks and patterns
        self.development_frameworks = {
            "widget_development": {
                "react_patterns": [
                    "functional_components",
                    "hooks_usage",
                    "state_management",
                    "effect_handling"
                ],
                "styling_patterns": [
                    "tailwind_classes",
                    "responsive_design",
                    "dark_theme_support",
                    "animation_patterns"
                ],
                "integration_patterns": [
                    "websocket_communication",
                    "api_integration",
                    "error_handling",
                    "loading_states"
                ]
            },
            "api_integration": {
                "rest_apis": [
                    "endpoint_design",
                    "authentication",
                    "error_handling",
                    "rate_limiting"
                ],
                "websocket_apis": [
                    "connection_management",
                    "message_handling",
                    "reconnection_logic",
                    "state_synchronization"
                ]
            },
            "agent_development": {
                "langgraph_patterns": [
                    "agent_loops",
                    "tool_integration",
                    "state_management",
                    "workflow_orchestration"
                ],
                "prompt_engineering": [
                    "system_prompts",
                    "few_shot_examples",
                    "chain_of_thought",
                    "output_formatting"
                ]
            }
        }
        
        # Code templates and snippets
        self.code_templates = {
            "react_widget": self._get_react_widget_template(),
            "langgraph_tool": self._get_langgraph_tool_template(),
            "api_endpoint": self._get_api_endpoint_template(),
            "websocket_handler": self._get_websocket_handler_template()
        }
    
    async def create_custom_widget(
        self,
        widget_name: str,
        widget_description: str,
        features: List[str],
        data_sources: List[str] = None,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a custom widget for the Chad GPT platform.
        
        Args:
            widget_name: Name of the widget to create
            widget_description: Description of widget functionality
            features: List of features the widget should have
            data_sources: List of data sources the widget will use
            thread_id: Conversation thread for progress updates
            
        Returns:
            Widget creation results with generated code
        """
        try:
            # Create development workflow
            workflow_id = await self._create_development_workflow(
                f"Custom Widget: {widget_name}", "widget_development", thread_id
            )
            
            # Step 1: Analyze requirements
            requirements = await self._analyze_widget_requirements(
                widget_name, widget_description, features, data_sources
            )
            
            # Step 2: Design component architecture
            architecture = await self._design_widget_architecture(requirements)
            
            # Step 3: Generate React component code
            component_code = await self._generate_widget_component(
                widget_name, requirements, architecture
            )
            
            # Step 4: Generate TypeScript interfaces
            type_definitions = await self._generate_widget_types(requirements)
            
            # Step 5: Generate styling
            styles = await self._generate_widget_styles(widget_name, requirements)
            
            # Step 6: Generate integration code
            integration_code = await self._generate_widget_integration(
                widget_name, requirements
            )
            
            # Step 7: Generate tests
            test_code = await self._generate_widget_tests(widget_name, requirements)
            
            # Step 8: Create documentation
            documentation = await self._generate_widget_documentation(
                widget_name, requirements, features
            )
            
            # Package results
            results = {
                "widget_name": widget_name,
                "requirements": requirements,
                "architecture": architecture,
                "generated_code": {
                    "component": component_code,
                    "types": type_definitions,
                    "styles": styles,
                    "integration": integration_code,
                    "tests": test_code
                },
                "documentation": documentation,
                "installation_instructions": await self._generate_installation_instructions(widget_name)
            }
            
            # Save widget files
            await self._save_widget_files(widget_name, results)
            
            return {
                "success": True,
                "workflow_id": workflow_id,
                "widget_name": widget_name,
                "widget_results": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "widget_name": widget_name
            }
    
    async def integrate_new_api(
        self,
        api_name: str,
        api_documentation: str,
        integration_type: str = "rest",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create integration code for a new API.
        
        Args:
            api_name: Name of the API to integrate
            api_documentation: API documentation or endpoint details
            integration_type: Type of integration (rest, websocket, graphql)
            thread_id: Conversation thread for progress updates
            
        Returns:
            API integration code and documentation
        """
        try:
            # Analyze API documentation
            api_analysis = await self._analyze_api_documentation(
                api_name, api_documentation, integration_type
            )
            
            # Generate client code
            client_code = await self._generate_api_client(api_analysis)
            
            # Generate type definitions
            type_definitions = await self._generate_api_types(api_analysis)
            
            # Generate error handling
            error_handling = await self._generate_api_error_handling(api_analysis)
            
            # Generate tests
            test_code = await self._generate_api_tests(api_analysis)
            
            # Generate documentation
            documentation = await self._generate_api_documentation(api_analysis)
            
            results = {
                "api_name": api_name,
                "integration_type": integration_type,
                "api_analysis": api_analysis,
                "generated_code": {
                    "client": client_code,
                    "types": type_definitions,
                    "error_handling": error_handling,
                    "tests": test_code
                },
                "documentation": documentation
            }
            
            # Save integration files
            await self._save_api_integration_files(api_name, results)
            
            return {
                "success": True,
                "api_name": api_name,
                "integration_results": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "api_name": api_name
            }
    
    async def create_langgraph_tool(
        self,
        tool_name: str,
        tool_description: str,
        parameters: List[Dict[str, Any]],
        functionality: str,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new LangGraph tool for the agent system.
        
        Args:
            tool_name: Name of the tool to create
            tool_description: Description of tool functionality
            parameters: List of tool parameters with types
            functionality: Detailed description of what the tool does
            thread_id: Conversation thread for progress updates
            
        Returns:
            Generated tool code and integration instructions
        """
        try:
            # Analyze tool requirements
            tool_requirements = await self._analyze_tool_requirements(
                tool_name, tool_description, parameters, functionality
            )
            
            # Generate tool implementation
            tool_code = await self._generate_langgraph_tool(tool_requirements)
            
            # Generate tool tests
            test_code = await self._generate_tool_tests(tool_requirements)
            
            # Generate integration code
            integration_code = await self._generate_tool_integration(tool_requirements)
            
            # Generate documentation
            documentation = await self._generate_tool_documentation(tool_requirements)
            
            results = {
                "tool_name": tool_name,
                "requirements": tool_requirements,
                "generated_code": {
                    "tool": tool_code,
                    "tests": test_code,
                    "integration": integration_code
                },
                "documentation": documentation
            }
            
            # Save tool files
            await self._save_tool_files(tool_name, results)
            
            return {
                "success": True,
                "tool_name": tool_name,
                "tool_results": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
    
    async def optimize_code(
        self,
        code: str,
        language: str,
        optimization_goals: List[str] = None,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Optimize existing code for performance, readability, or other goals.
        
        Args:
            code: Code to optimize
            language: Programming language
            optimization_goals: List of optimization goals
            thread_id: Conversation thread for progress updates
            
        Returns:
            Optimized code with explanations
        """
        try:
            if optimization_goals is None:
                optimization_goals = ["performance", "readability", "maintainability"]
            
            # Analyze current code
            code_analysis = await self._analyze_code_quality(code, language)
            
            # Apply optimizations
            optimized_code = await self._apply_optimizations(
                code, language, optimization_goals, code_analysis
            )
            
            # Generate optimization report
            optimization_report = await self._generate_optimization_report(
                code_analysis, optimized_code, optimization_goals
            )
            
            results = {
                "original_code": code,
                "language": language,
                "optimization_goals": optimization_goals,
                "code_analysis": code_analysis,
                "optimized_code": optimized_code,
                "optimization_report": optimization_report
            }
            
            return {
                "success": True,
                "optimization_results": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "language": language
            }
    
    async def _create_development_workflow(
        self,
        project_name: str,
        project_type: str,
        thread_id: Optional[str]
    ) -> str:
        """Create workflow for development project."""
        workflow_definition = {
            "name": project_name,
            "description": f"Development project: {project_type}",
            "steps": [
                {"id": "requirements", "name": "Analyze Requirements"},
                {"id": "design", "name": "Design Architecture"},
                {"id": "implementation", "name": "Generate Code"},
                {"id": "testing", "name": "Create Tests"},
                {"id": "documentation", "name": "Generate Documentation"},
                {"id": "integration", "name": "Create Integration Guide"}
            ]
        }
        
        return await self.workflow_planner.create_workflow(
            definition=workflow_definition,
            thread_id=thread_id or "development_project"
        )
    
    def _get_react_widget_template(self) -> str:
        """Get React widget template."""
        return '''
import React, { useState, useEffect } from 'react';
import { {WidgetName}Props, {WidgetName}Data } from './types';

export const {WidgetName}: React.FC<{WidgetName}Props> = ({
  data,
  onUpdate,
  className = ""
}) => {
  const [widgetData, setWidgetData] = useState<{WidgetName}Data>(data);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setWidgetData(data);
  }, [data]);

  const handleUpdate = async (newData: Partial<{WidgetName}Data>) => {
    setLoading(true);
    try {
      const updatedData = { ...widgetData, ...newData };
      setWidgetData(updatedData);
      onUpdate?.(updatedData);
    } catch (error) {
      console.error('Widget update error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-[#111] rounded-2xl p-6 shadow-lg shadow-black/10 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">{WidgetName}</h3>
        {loading && (
          <div className="animate-spin w-4 h-4 border-2 border-[#22c55e] border-t-transparent rounded-full" />
        )}
      </div>
      
      {/* Widget content goes here */}
      <div className="space-y-4">
        {/* Add widget-specific content */}
      </div>
    </div>
  );
};
'''
    
    def _get_langgraph_tool_template(self) -> str:
        """Get LangGraph tool template."""
        return '''
from langchain_core.tools import tool
from typing import Dict, Any, Optional

@tool
def {tool_name}({parameters}) -> Dict[str, Any]:
    """
    {tool_description}
    
    Args:
        {parameter_docs}
        
    Returns:
        Dictionary with operation result
    """
    try:
        # Tool implementation goes here
        result = {
            "success": True,
            "data": {},
            "message": "Operation completed successfully"
        }
        
        return result
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Tool execution failed: {str(e)}"
        }
'''
    
    def _get_api_endpoint_template(self) -> str:
        """Get API endpoint template."""
        return '''
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel

router = APIRouter()

class {RequestModel}(BaseModel):
    # Request model fields
    pass

class {ResponseModel}(BaseModel):
    # Response model fields
    pass

@router.{method}("/{endpoint}")
async def {endpoint_name}(
    request: {RequestModel},
    # Add dependencies here
) -> {ResponseModel}:
    """
    {endpoint_description}
    """
    try:
        # Endpoint implementation
        result = {}
        
        return {ResponseModel}(**result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
'''
    
    def _get_websocket_handler_template(self) -> str:
        """Get WebSocket handler template."""
        return '''
import asyncio
import json
from typing import Dict, Any
import websockets

class {HandlerName}:
    """WebSocket handler for {description}."""
    
    def __init__(self):
        self.connections = set()
        self.handlers = {}
    
    async def handle_connection(self, websocket, path):
        """Handle new WebSocket connection."""
        self.connections.add(websocket)
        try:
            async for message in websocket:
                await self.handle_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            self.connections.remove(websocket)
    
    async def handle_message(self, websocket, message):
        """Handle incoming WebSocket message."""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type in self.handlers:
                response = await self.handlers[message_type](data)
                await websocket.send(json.dumps(response))
                
        except Exception as e:
            error_response = {
                "type": "error",
                "error": str(e)
            }
            await websocket.send(json.dumps(error_response))
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients."""
        if self.connections:
            await asyncio.gather(
                *[conn.send(json.dumps(message)) for conn in self.connections],
                return_exceptions=True
            )
'''
    
    async def _save_widget_files(self, widget_name: str, results: Dict[str, Any]):
        """Save generated widget files."""
        base_path = f"agent_workspace/widgets/{widget_name.lower()}"
        
        # Save component file
        await self.platform_agent.file_manager.write_file(
            path=f"{base_path}/{widget_name}.tsx",
            content=results["generated_code"]["component"]
        )
        
        # Save types file
        await self.platform_agent.file_manager.write_file(
            path=f"{base_path}/types.ts",
            content=results["generated_code"]["types"]
        )
        
        # Save documentation
        await self.platform_agent.file_manager.write_file(
            path=f"{base_path}/README.md",
            content=results["documentation"]
        )


def create_development_agent(platform_agent: PlatformAgentLoop) -> DevelopmentAgent:
    """Factory function to create development agent."""
    return DevelopmentAgent(platform_agent)
