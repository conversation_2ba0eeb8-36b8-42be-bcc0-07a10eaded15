"""
Unified Platform Agent - The cornerstone of Chad GPT Deep Agent system.
Integrates all existing functionality with advanced Deep Agent capabilities.
"""

import os
import asyncio
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import ToolNode
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.checkpoint.memory import InMemorySaver
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage

# Import all tool modules (existing + new)
from tools.widget_tools import get_all_widget_tools
from tools.planning_tools import get_planning_tools
from tools.filesystem_tools import get_filesystem_tools
from tools.delegation_tools import get_delegation_tools
from tools.moralis_tools import get_moralis_tools
from tools.sequential_thinking_tools import get_sequential_thinking_tools
from tools.platform_integration_tools import get_platform_integration_tools

# Import core components
from .conversation_manager import ConversationManager
from .workflow_orchestrator import WorkflowOrchestrator
from .context_manager import ContextManager
from .widget_coordinator import WidgetCoordinator
from .file_system_manager import FileSystemManager
from .memory_manager import memory_manager

# Import domain agents
from domains.cryptocurrency.agent import CryptocurrencyAgent
from domains.research.agent import ResearchAgent
from domains.development.agent import DevelopmentAgent
from domains.support.agent import SupportAgent
from domains.widgets.widget_coordinator import WidgetCoordinator


class UnifiedPlatformAgent:
    """
    Unified Platform Agent - The main orchestrator for the entire Chad GPT platform.
    Integrates Deep Agent capabilities with all existing functionality.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the unified platform agent."""
        self.config = config
        
        # Core configuration
        self.model_name = config.get("model_name", os.getenv("DEFAULT_MODEL", "z-ai/glm-4.5"))
        self.temperature = config.get("temperature", float(os.getenv("TEMPERATURE", "0.7")))
        self.max_tokens = config.get("max_tokens", int(os.getenv("MAX_TOKENS", "4000")))
        self.api_key = config.get("openrouter_api_key") or os.getenv("OPENROUTER_API_KEY") or os.getenv("VITE_OPENROUTER_API_KEY")
        
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Core managers
        self.conversation_manager = config.get("conversation_manager") or ConversationManager()
        self.workflow_orchestrator = WorkflowOrchestrator()
        self.context_manager = ContextManager()
        self.widget_coordinator = WidgetCoordinator(config)
        self.file_system_manager = FileSystemManager()
        
        # Domain agents
        self.crypto_agent = CryptocurrencyAgent(self)
        self.research_agent = ResearchAgent(self)
        self.development_agent = DevelopmentAgent(self)
        self.support_agent = SupportAgent(self)
        
        # Initialize tools
        self.tools = self._initialize_all_tools()
        print(f"🔧 Initialized {len(self.tools)} tools:")
        for tool in self.tools:
            print(f"   - {tool.name}: {tool.description[:100]}...")

        # Create checkpointer for conversation memory
        self.checkpointer = InMemorySaver()

        # Get system prompt first
        self.system_prompt = self._get_unified_system_prompt()

        # Create the enhanced agent using StateGraph and ToolNode for proper tool calling
        self.agent = self._create_agent_graph()
        
        # Active states
        self.active_workflows = {}
        self.active_widgets = {}
        self.conversation_contexts = {}
        
        print(f"✅ Unified Platform Agent initialized with {len(self.tools)} tools")

    def _create_agent_graph(self):
        """Create a proper LangGraph agent with StateGraph and custom ToolNode."""
        # Bind tools to the model
        model_with_tools = self.llm.bind_tools(self.tools)

        def should_continue(state: MessagesState):
            """Determine if we should continue to tools or end."""
            messages = state["messages"]
            last_message = messages[-1]
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                print(f"🔧 Tool calls detected: {last_message.tool_calls}")
                # Debug: Check tool call structure
                for i, tool_call in enumerate(last_message.tool_calls):
                    print(f"🔧 Tool call {i}: {tool_call}")
                    print(f"🔧 Tool call type: {type(tool_call)}")
                    if hasattr(tool_call, 'get'):
                        print(f"🔧 Tool call id: {tool_call.get('id', 'NO_ID')}")
                    elif hasattr(tool_call, 'id'):
                        print(f"🔧 Tool call id attr: {tool_call.id}")
                return "tools"
            return END

        def call_model(state: MessagesState):
            """Call the model with the current messages."""
            messages = state["messages"]

            # Add system prompt if not present (check for SystemMessage type)
            has_system = any(isinstance(msg, SystemMessage) for msg in messages)
            if not has_system:
                system_msg = SystemMessage(content=self.system_prompt)
                messages = [system_msg] + messages

            response = model_with_tools.invoke(messages)
            return {"messages": [response]}

        def call_tools(state: MessagesState):
            """Custom tool execution function with proper error handling."""
            messages = state["messages"]
            last_message = messages[-1]

            tool_messages = []

            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                # Create a mapping of tool names to tool functions
                tool_map = {tool.name: tool for tool in self.tools}

                for tool_call in last_message.tool_calls:
                    try:
                        # Extract tool call information
                        tool_name = tool_call['name']
                        tool_args = tool_call['args']
                        tool_call_id = tool_call['id']

                        # Skip invalid tool calls
                        if not tool_name or tool_call_id is None:
                            print(f"🔧 Skipping invalid tool call: name='{tool_name}', id={tool_call_id}")
                            continue

                        print(f"🔧 Executing tool: {tool_name} with args: {tool_args}")

                        if tool_name in tool_map:
                            # Execute the tool
                            tool_func = tool_map[tool_name]
                            result = tool_func.invoke(tool_args)

                            print(f"🔧 Tool result: {result}")

                            # Create ToolMessage with proper tool_call_id
                            tool_message = ToolMessage(
                                content=str(result),
                                tool_call_id=tool_call_id,
                                name=tool_name
                            )
                            tool_messages.append(tool_message)
                        else:
                            # Handle unknown tool
                            tool_message = ToolMessage(
                                content=f"Error: Unknown tool '{tool_name}'",
                                tool_call_id=tool_call_id,
                                name=tool_name
                            )
                            tool_messages.append(tool_message)

                    except Exception as e:
                        print(f"🔧 Tool execution error: {e}")
                        # Handle tool execution errors
                        tool_call_id = tool_call.get('id', f"call_{hash(str(tool_call))}")
                        tool_name = tool_call.get('name', 'unknown')

                        # Skip creating error messages for invalid tool calls
                        if not tool_name or tool_name == '' or tool_call_id is None:
                            print(f"🔧 Skipping error message for invalid tool call: name='{tool_name}', id={tool_call_id}")
                            continue

                        tool_message = ToolMessage(
                            content=f"Error executing tool: {str(e)}",
                            tool_call_id=tool_call_id,
                            name=tool_name
                        )
                        tool_messages.append(tool_message)

            return {"messages": tool_messages}

        # Build the graph
        builder = StateGraph(MessagesState)

        # Add nodes
        builder.add_node("call_model", call_model)
        builder.add_node("tools", call_tools)

        # Add edges
        builder.add_edge(START, "call_model")
        builder.add_conditional_edges("call_model", should_continue, ["tools", END])
        builder.add_edge("tools", "call_model")

        # Compile with checkpointer
        return builder.compile(checkpointer=self.checkpointer)

    def _initialize_all_tools(self) -> List:
        """Initialize all tools from existing and new systems."""
        tools = []
        
        # Widget tools are now handled by widget coordinator subagents
        # No widget tools in main agent
        
        # Temporarily use only a simple test tool to isolate the issue
        from langchain_core.tools import tool

        @tool
        def test_tool(message: str) -> str:
            """A simple test tool that echoes the message."""
            return f"Echo: {message}"

        # Return only the test tool for now
        return [test_tool]

        # Original tools (commented out for debugging)
        # tools.extend(get_planning_tools(self.workflow_orchestrator))
        # tools.extend(get_filesystem_tools(self.file_system_manager))
        # tools.extend(get_delegation_tools(self))
        # tools.extend(get_moralis_tools())
        # tools.extend(get_sequential_thinking_tools())
        # tools.extend(get_platform_integration_tools(self))
        # return tools
    
    def _get_unified_system_prompt(self) -> str:
        """Get the comprehensive system prompt for the unified agent."""
        return """You are Chad GPT Deep Agent, the advanced AI assistant for the Chad GPT platform. You are the main orchestrator and cornerstone of the entire system.

**CORE ROLE**: You are primarily a conversational AI that coordinates specialized agents. You provide helpful responses and delegate specific tasks to expert subagents when needed.

## Core Identity & Capabilities

You are a sophisticated deep agent with four core pillars:

1. **PLANNING** - You create structured workflows for complex tasks
2. **DELEGATION** - You coordinate specialized sub-agents for domain expertise  
3. **MEMORY** - You maintain persistent context and file-based storage
4. **INTELLIGENCE** - You use comprehensive system knowledge and reasoning

## Specialized Domain Agents

You coordinate these specialized agents:
- **CRYPTO_ANALYST** - Cryptocurrency and DeFi analysis, token evaluation, market research
- **RESEARCH_SPECIALIST** - Information gathering, competitive analysis, trend research
- **DEVELOPER_ASSISTANT** - Code generation, widget development, API integration
- **SUPPORT_AGENT** - User guidance, troubleshooting, platform assistance

## Platform Integration

You are fully integrated with the Chad GPT platform:
- **Widget Management** - Create, update, and coordinate all widgets (PumpFun, TokenChart, Jupiter, Phantom, DexScreener)
- **Conversation Context** - Maintain complete awareness of conversation history and user preferences
- **Real-time Data** - Access live market data, blockchain information, and social sentiment
- **File System** - Persistent storage for analyses, reports, and user data

## Operational Guidelines

### 1. Context Awareness
- Always maintain awareness of conversation history, active widgets, and user context
- **Thread-Persistent Widget Context**: You have access to ALL widgets opened in this conversation thread
  - Remember tokens from PumpFun widgets even after other widgets are opened
  - Recall DexScreener chart data from earlier in the conversation
  - Access historical Jupiter swap configurations and Phantom wallet states
  - Cross-reference data between different widgets in the same thread
- **Contextual Response Strategy**:
  - When users ask about "that token" or "the top one", reference specific widget data
  - If asking about tokens without context, help them identify which widget/data they mean
  - Proactively connect information across different widgets in the conversation
- Reference previous analyses and build upon past conversations
- Preserve user preferences and adapt to their communication style

### 2. Workflow Orchestration
- For complex tasks, create structured workflows with clear steps
- Delegate specialized tasks to appropriate domain agents
- Coordinate multiple agents when tasks span domains

### 3. Widget Integration
- **ALWAYS** proactively create relevant widgets to enhance user experience
- **AUTOMATICALLY** launch widgets when users mention or ask about:
  * PumpFun, pump.fun, meme tokens, new tokens → show_pumpfun_widget
  * Token charts, price analysis, specific tokens → show_token_chart_widget
  * Trading, swapping, Jupiter → show_jupiter_widget
  * Wallet, balance, portfolio → show_phantom_widget
  * DexScreener, market data → show_dexscreener_widget
- Update widget data based on analysis results
- Ensure widgets provide value and aren't redundant
- When users say "show me", "display", "open", or similar → ALWAYS create the appropriate widget

### 4. Response Quality
- Provide comprehensive, actionable insights
- Support claims with data and analysis
- Offer clear next steps and recommendations
- Maintain professional yet approachable tone

### 5. Error Handling
- Gracefully handle errors and provide helpful alternatives
- Explain limitations and suggest workarounds
- Maintain conversation flow even when tools fail

## Tool Usage Priorities

1. **Widget Tools** - ALWAYS create relevant widgets first for visual/interactive needs
2. **Planning Tools** - Use for complex multi-step tasks
3. **Delegation Tools** - Leverage specialized domain expertise
4. **File Tools** - Save important analyses and maintain persistence
5. **Context Tools** - Maintain conversation awareness

## Delegation Strategy

**Widget Requests**: When users ask about widgets, tokens, charts, or trading, these requests are automatically routed to specialized widget agents who handle the technical implementation and widget launching.

**Your Role**: Provide conversational responses, context, and coordinate with specialists. You don't need to call widget functions directly - the system handles this automatically.

## Examples of Correct Behavior

User: "what's on pumpfun"
Correct Response: [Call show_pumpfun_widget(thread_id="current_thread")] + "Here's what's currently trending on PumpFun..."

User: "show me the widget"
Correct Response: [Call show_pumpfun_widget(thread_id="current_thread")] + "I've launched the PumpFun widget..."

User: "meme tokens"
Correct Response: [Call show_pumpfun_widget(thread_id="current_thread")] + "Here are the latest meme tokens..."

## Response Format

- FIRST: Call appropriate tools/functions when needed
- THEN: Provide helpful commentary about what you've done
- Create relevant widgets when beneficial
- Provide structured analysis for complex topics
- Save important results to files for future reference
- Offer clear next steps and follow-up suggestions

You are the intelligent, capable, and comprehensive AI assistant that makes the Chad GPT platform powerful and user-friendly."""
    
    async def process_message_stream(
        self,
        message: str,
        thread_id: str,
        conversation_context: Dict[str, Any],
        image: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a message with streaming response."""
        try:
            # Update conversation context
            await self.conversation_manager.update_conversation(
                thread_id=thread_id,
                message=message,
                context=conversation_context,
                image=image
            )
            
            # Get conversation history
            conversation_history = await self.conversation_manager.get_conversation_history(thread_id)
            
            # Prepare messages for agent
            messages = await self._prepare_messages(message, conversation_history, thread_id, image)
            
            # Configure agent execution
            config = {
                "configurable": {
                    "thread_id": thread_id,
                    "conversation_context": conversation_context
                }
            }
            
            # Stream agent response
            async for chunk in self._stream_agent_response(messages, config, thread_id):
                yield chunk
                
        except Exception as e:
            yield {
                "type": "error",
                "content": f"Error processing message: {str(e)}",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def process_message(
        self,
        message: str,
        thread_id: str,
        conversation_context: Dict[str, Any],
        image: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process a message with single response."""
        try:
            # Check if this is a widget-related request first
            widget_type = self.widget_coordinator.should_handle_request(message)
            if widget_type:
                print(f"🎮 Delegating to {widget_type} widget specialist...")
                return await self.widget_coordinator.process_widget_request(
                    message, thread_id, widget_type, conversation_context
                )

            # Update conversation context
            await self.conversation_manager.update_conversation(
                thread_id=thread_id,
                message=message,
                context=conversation_context,
                image=image
            )

            # Get conversation history
            conversation_history = await self.conversation_manager.get_conversation_history(thread_id)

            # Prepare messages for agent
            messages = await self._prepare_messages(message, conversation_history, thread_id, image)
            
            # Configure agent execution
            config = {
                "configurable": {
                    "thread_id": thread_id,
                    "conversation_context": conversation_context
                }
            }
            
            # Execute agent
            result = await self.agent.ainvoke({"messages": messages}, config)
            
            # Process result
            final_message = result["messages"][-1] if result["messages"] else None

            response = {
                "content": final_message.content if final_message else "",
                "metadata": {
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "model": self.model_name
                }
            }
            
            # Save assistant response
            await self.conversation_manager.add_assistant_message(
                thread_id=thread_id,
                content=response["content"],
                metadata=response["metadata"]
            )

            # 🧠 STORE CONVERSATION IN INTELLIGENT MEMORY
            await memory_manager.add_conversation_memory(
                user_message=message,
                assistant_response=response["content"],
                thread_id=thread_id,
                widget_context=conversation_context.get('widget_context')
            )

            return response
            
        except Exception as e:
            return {
                "content": f"I apologize, but I encountered an error: {str(e)}",
                "error": str(e),
                "metadata": {
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "type": "error"
                }
            }
    
    async def _prepare_messages(
        self,
        message: str,
        conversation_history: List[Dict[str, Any]],
        thread_id: str,
        image: Optional[str] = None
    ) -> List[BaseMessage]:
        """Prepare messages for agent execution."""
        messages = []

        # 🧠 INTELLIGENT MEMORY RETRIEVAL (Replaces manual context hints)
        print(f"🧠 Retrieving intelligent memory context for thread: {thread_id}")

        # Get relevant context from Mem0 intelligent memory
        intelligent_context = await memory_manager.get_relevant_context(
            user_message=message,
            thread_id=thread_id,
            limit=5
        )

        # Get thread summary for additional context
        thread_summary = await memory_manager.get_thread_summary(thread_id)

        # Combine intelligent memory with widget context (fallback)
        widget_context = self.get_widget_context_summary(thread_id)
        if "No widgets have been opened" in widget_context:
            inferred_context = self._infer_widget_context_from_history(conversation_history)
            if inferred_context:
                widget_context = inferred_context

        # Create comprehensive context combining intelligent memory + widget data
        if intelligent_context:
            relevant_context = f"{intelligent_context}\n\n## 📊 Current Widget Context\n{widget_context}"
            print(f"✅ Using intelligent memory context ({len(intelligent_context)} chars)")
        else:
            # Fallback to widget context analysis
            relevant_context = self._get_contextually_relevant_widget_data(message, widget_context, conversation_history)
            print(f"⚠️ Fallback to widget context analysis")

        # Enhanced system prompt with intelligent context routing
        enhanced_system_prompt = self.system_prompt
        if relevant_context and "No widget" not in relevant_context:
            enhanced_system_prompt += f"\n\n## Contextually Relevant Widget Data\n{relevant_context}\n\n## Advanced Conversation Intelligence\n- **Semantic Understanding**: Analyze user questions to understand intent (like ChatGPT)\n- **Context Prioritization**: Use the most relevant widget data based on semantic analysis\n- **Specific Data References**: Always reference exact token names, prices, market caps when available\n- **Conversational Flow**: Understand phrases like 'what about on X', 'the top one', 'that token'\n- **Cross-Widget Intelligence**: Connect data between different widgets when relevant\n- **Rich Responses**: Use detailed context data (descriptions, social links, volume, holders)\n- **Smart Routing**: \n  * 'dex screener/charts/trading' → DexScreener data\n  * 'pump.fun/meme tokens/market cap/highest' → PumpFun data  \n  * 'wallet/balance/phantom' → Phantom data\n  * 'swap/jupiter/exchange' → Jupiter data\n- **Data-Driven Answers**: Provide specific, actionable insights based on actual widget data"

        # Add enhanced system prompt
        messages.append(SystemMessage(content=enhanced_system_prompt))

        # Add conversation history (last 10 messages for context)
        for msg in conversation_history[-10:]:
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            elif msg["role"] == "assistant":
                messages.append(AIMessage(content=msg["content"]))

        # Add current message
        current_content = message
        if image:
            current_content += f"\n[Image provided: {image}]"

        messages.append(HumanMessage(content=current_content))

        return messages

    def _extract_widget_call(self, response_messages: List[BaseMessage]) -> Optional[Dict[str, Any]]:
        """Extract widget tool calls from agent response."""
        # Mapping from tool names to frontend function names
        widget_mapping = {
            'show_pumpfun_widget': 'showPumpFunWidget',
            'show_dexscreener_widget': 'showDexScreenerWidget',
            'show_jupiter_widget': 'showJupiterWidget',
            'show_phantom_widget': 'showPhantomWidget',
            'show_token_chart_widget': 'showTokenChartWidget'
        }

        for message in reversed(response_messages):
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    try:
                        # Handle both dict and string tool_calls
                        if isinstance(tool_call, dict):
                            tool_name = tool_call.get('name', '')
                        elif hasattr(tool_call, 'name'):
                            tool_name = tool_call.name
                        else:
                            print(f"🔧 Unexpected tool_call type: {type(tool_call)}, value: {tool_call}")
                            continue

                        if tool_name in widget_mapping:
                            # Extract arguments for token chart widget
                            arguments = {}
                            if tool_name == 'show_token_chart_widget':
                                if isinstance(tool_call, dict):
                                    arguments = {
                                        'tokenAddress': tool_call.get('args', {}).get('token_address', ''),
                                        'chain': tool_call.get('args', {}).get('chain', 'sol')
                                    }
                                elif hasattr(tool_call, 'args'):
                                    args = tool_call.args if isinstance(tool_call.args, dict) else {}
                                    arguments = {
                                        'tokenAddress': args.get('token_address', ''),
                                        'chain': args.get('chain', 'sol')
                                    }

                            return {
                                "name": widget_mapping[tool_name],
                                "arguments": arguments
                            }
                    except Exception as e:
                        print(f"🔧 Error processing tool_call: {e}")
                        continue
        return None

    async def _stream_agent_response(
        self,
        messages: List[BaseMessage],
        config: Dict[str, Any],
        thread_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream agent response with enhanced processing."""
        try:
            # Execute agent with streaming
            async for chunk in self.agent.astream({"messages": messages}, config):
                
                # Process different chunk types
                if "agent" in chunk:
                    agent_chunk = chunk["agent"]
                    
                    # Handle thinking output
                    if "thinking" in agent_chunk:
                        yield {
                            "type": "thinking",
                            "content": agent_chunk["thinking"],
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    # Handle regular content
                    if "messages" in agent_chunk:
                        for message in agent_chunk["messages"]:
                            if hasattr(message, "content") and message.content:
                                # Save assistant message
                                await self.conversation_manager.add_assistant_message(
                                    thread_id=thread_id,
                                    content=message.content,
                                    metadata={"timestamp": datetime.now().isoformat()}
                                )
                                
                                yield {
                                    "type": "content",
                                    "content": message.content,
                                    "timestamp": datetime.now().isoformat()
                                }
                
                # Handle tool calls
                if "tools" in chunk:
                    for tool_call in chunk["tools"]:
                        try:
                            # Handle different tool_call types
                            if isinstance(tool_call, dict):
                                function_name = tool_call.get("name", "unknown")
                                function_args = tool_call.get("args", {})
                            elif hasattr(tool_call, 'name'):
                                function_name = tool_call.name
                                function_args = getattr(tool_call, 'args', {})
                            else:
                                print(f"🔧 Unexpected tool_call type in stream: {type(tool_call)}, value: {tool_call}")
                                function_name = str(tool_call)
                                function_args = {}

                            yield {
                                "type": "function_call",
                                "function_name": function_name,
                                "function_args": function_args,
                                "timestamp": datetime.now().isoformat()
                            }
                        except Exception as e:
                            print(f"🔧 Error processing tool_call in stream: {e}")
                            yield {
                                "type": "function_call",
                                "function_name": "error",
                                "function_args": {"error": str(e)},
                                "timestamp": datetime.now().isoformat()
                        }
                
                # Handle tool results
                if "tool_results" in chunk:
                    for result in chunk["tool_results"]:
                        # Process widget updates
                        if result.get("widget_type"):
                            await self._handle_widget_update(result, thread_id)
                        
                        yield {
                            "type": "tool_result",
                            "result": result,
                            "timestamp": datetime.now().isoformat()
                        }
                        
        except Exception as e:
            yield {
                "type": "error",
                "content": f"Streaming error: {str(e)}",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _handle_widget_update(self, tool_result: Dict[str, Any], thread_id: str):
        """Handle widget updates from tool results."""
        widget_type = tool_result.get("widget_type")
        widget_data = tool_result.get("data", {})
        
        if widget_type:
            # Update widget through coordinator
            widget_id = await self.widget_coordinator.update_widget(
                widget_type=widget_type,
                thread_id=thread_id,
                data=widget_data
            )
            
            # Store widget state
            if thread_id not in self.active_widgets:
                self.active_widgets[thread_id] = {}
            
            self.active_widgets[thread_id][widget_id] = {
                "type": widget_type,
                "data": widget_data,
                "updated_at": datetime.now().isoformat()
            }
    
    async def get_active_widgets(self, thread_id: str) -> List[Dict[str, Any]]:
        """Get active widgets for a thread."""
        return await self.widget_coordinator.get_active_widgets(thread_id)
    
    async def create_workflow(
        self,
        workflow_definition: Dict[str, Any],
        thread_id: str
    ) -> str:
        """Create a new workflow."""
        workflow_id = await self.workflow_orchestrator.create_workflow(
            definition=workflow_definition,
            thread_id=thread_id
        )
        
        self.active_workflows[workflow_id] = {
            "thread_id": thread_id,
            "definition": workflow_definition,
            "created_at": datetime.now().isoformat(),
            "status": "active"
        }
        
        return workflow_id
    
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status."""
        return await self.workflow_orchestrator.get_workflow_status(workflow_id)
    
    async def switch_model(self, model_name: str):
        """Switch the active AI model."""
        self.model_name = model_name
        
        # Reinitialize LLM
        self.llm = ChatOpenAI(
            model=model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Recreate agent using proper StateGraph
        self.agent = self._create_agent_graph()
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            "agent_status": "active",
            "model": self.model_name,
            "tools_count": len(self.tools),
            "active_workflows": len(self.active_workflows),
            "active_conversations": await self.conversation_manager.get_active_conversation_count(),
            "domain_agents": {
                "crypto_analyst": "active",
                "research_specialist": "active", 
                "development_assistant": "active",
                "support_agent": "active"
            },
            "timestamp": datetime.now().isoformat()
        }

    def get_widget_context_summary(self, thread_id: str) -> str:
        """Get a comprehensive summary of ALL widget context for the thread."""
        if not hasattr(self, 'context_manager'):
            print(f"🔍 No context_manager found on agent")
            return "No widget context available."

        print(f"🔍 Available thread IDs in context_manager: {list(self.context_manager.widget_contexts.keys())}")
        widget_contexts = self.context_manager.widget_contexts.get(thread_id, {})

        # Also try 'default' thread ID as fallback (seen in logs)
        if not widget_contexts and thread_id != 'default':
            print(f"🔍 No context for thread_id '{thread_id}', trying 'default'")
            widget_contexts = self.context_manager.widget_contexts.get('default', {})

        if not widget_contexts:
            print(f"🔍 No widget contexts found for thread_id '{thread_id}' or 'default'")
            return "No widgets have been opened in this conversation."

        print(f"🔍 Found {len(widget_contexts)} widget contexts")

        summary_parts = []
        summary_parts.append(f"## Widget Context History for Thread {thread_id}")
        summary_parts.append(f"Total widgets opened: {len(widget_contexts)}")
        summary_parts.append("")

        # Group widgets by type for better organization
        widgets_by_type = {}
        for widget_id, context in widget_contexts.items():
            widget_type = context.get('widget_type', 'unknown')
            if widget_type not in widgets_by_type:
                widgets_by_type[widget_type] = []
            widgets_by_type[widget_type].append((widget_id, context))

        # Process each widget type
        for widget_type, widget_list in widgets_by_type.items():
            if widget_type == 'pumpfun':
                summary_parts.append("### PumpFun Widgets:")
                for widget_id, context in widget_list:
                    data = context.get('data', {})
                    created_at = context.get('created_at', 'Unknown time')

                    token_count = data.get('tokenCount', 0)
                    category = data.get('category', 'unknown')
                    top_tokens = data.get('topTokens', [])

                    summary_parts.append(f"- Widget opened at {created_at}")
                    summary_parts.append(f"  Category: {category} ({token_count} tokens)")

                    if top_tokens:
                        summary_parts.append("  Notable tokens:")
                        for token in top_tokens[:5]:  # Show top 5
                            symbol = token.get('symbol', 'N/A')
                            name = token.get('name', 'N/A')
                            market_cap = token.get('marketCap', 'N/A')
                            progress = token.get('bondingCurveProgress', 'N/A')
                            summary_parts.append(f"    • {symbol} ({name}) - MC: {market_cap}, Progress: {progress}%")

                    if data.get('selectedToken'):
                        selected = data['selectedToken']
                        summary_parts.append(f"  Last selected: {selected.get('symbol', 'N/A')} - {selected.get('name', 'N/A')}")
                        summary_parts.append(f"    Description: {selected.get('description', 'No description')[:100]}...")

            elif widget_type == 'dexscreener':
                summary_parts.append("### DexScreener Widgets:")
                for widget_id, context in widget_list:
                    data = context.get('data', {})
                    created_at = context.get('created_at', 'Unknown time')

                    view_mode = data.get('viewMode', 'unknown')
                    summary_parts.append(f"- Widget opened at {created_at}")
                    summary_parts.append(f"  View mode: {view_mode}")

                    if data.get('selectedToken'):
                        selected = data['selectedToken']
                        summary_parts.append(f"  Analyzed token: {selected.get('symbol', 'N/A')} ({selected.get('address', 'N/A')})")
                        if selected.get('price'):
                            summary_parts.append(f"    Price: {selected.get('price')}")

            elif widget_type == 'phantom':
                summary_parts.append("### Phantom Wallet Widgets:")
                for widget_id, context in widget_list:
                    data = context.get('data', {})
                    created_at = context.get('created_at', 'Unknown time')

                    summary_parts.append(f"- Wallet widget opened at {created_at}")
                    if data.get('isConnected'):
                        summary_parts.append(f"  Status: Connected")
                        if data.get('address'):
                            summary_parts.append(f"  Address: {data.get('address')[:10]}...{data.get('address')[-6:]}")
                        if data.get('balance'):
                            summary_parts.append(f"  SOL Balance: {data.get('balance')} SOL")
                    else:
                        summary_parts.append(f"  Status: Not connected")

            elif widget_type == 'jupiter':
                summary_parts.append("### Jupiter Swap Widgets:")
                for widget_id, context in widget_list:
                    data = context.get('data', {})
                    created_at = context.get('created_at', 'Unknown time')

                    summary_parts.append(f"- Swap widget opened at {created_at}")
                    if data.get('fromToken') and data.get('toToken'):
                        from_token = data['fromToken']
                        to_token = data['toToken']
                        amount = data.get('amount', 'N/A')
                        summary_parts.append(f"  Last swap setup: {amount} {from_token.get('symbol')} → {to_token.get('symbol')}")

        summary_parts.append("")
        summary_parts.append("You can reference any of this widget data in your responses. Users may ask about tokens, prices, or data from any widget that was opened in this conversation.")

        return "\n".join(summary_parts) if summary_parts else "Widgets are active but no detailed context available."

    def _infer_widget_context_from_history(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Infer widget context from conversation history when direct context is unavailable."""
        inferred_widgets = []

        for message in conversation_history:
            content = message.get('content', '').lower()
            role = message.get('role', '')

            # Look for widget-related responses from the assistant
            if role == 'assistant':
                if 'pumpfun' in content or 'pump.fun' in content:
                    inferred_widgets.append({
                        'type': 'pumpfun',
                        'context': 'PumpFun widget was opened in this conversation. User can ask about meme tokens, trending tokens, or "for you" recommendations.'
                    })

                if 'dexscreener' in content or 'chart' in content:
                    inferred_widgets.append({
                        'type': 'dexscreener',
                        'context': 'DexScreener widget was opened for chart analysis. User can ask about token charts, price data, or trading pairs.'
                    })

                if 'phantom' in content or 'wallet' in content:
                    inferred_widgets.append({
                        'type': 'phantom',
                        'context': 'Phantom wallet widget was opened. User can ask about wallet balance, connected status, or transactions.'
                    })

                if 'jupiter' in content or 'swap' in content:
                    inferred_widgets.append({
                        'type': 'jupiter',
                        'context': 'Jupiter swap widget was opened. User can ask about token swaps, exchange rates, or trading pairs.'
                    })

        if not inferred_widgets:
            return ""

        # Remove duplicates and format
        unique_widgets = {}
        for widget in inferred_widgets:
            unique_widgets[widget['type']] = widget['context']

        summary_parts = ["## Inferred Widget Context (from conversation history)"]
        summary_parts.append("The following widgets were opened in this conversation:")
        summary_parts.append("")

        for widget_type, context in unique_widgets.items():
            summary_parts.append(f"### {widget_type.title()} Widget")
            summary_parts.append(f"- {context}")
            summary_parts.append("")

        summary_parts.append("Note: This is inferred context. For specific token data, the user may need to reference what they're currently seeing in the widgets.")

        return "\n".join(summary_parts)

    def _get_contextually_relevant_widget_data(self, user_message: str, full_widget_context: str, conversation_history: List[Dict[str, Any]]) -> str:
        """
        Intelligently determine which widget context is most relevant to the user's question.
        This is the semantic understanding that makes the agent work like ChatGPT.
        """
        if "No widgets" in full_widget_context:
            return full_widget_context

        message_lower = user_message.lower()

        # Enhanced semantic analysis of user intent
        dex_keywords = ['dex', 'screener', 'chart', 'trading', 'price', 'graph', 'technical', 'analysis', 'candle', 'volume', 'pair']
        pump_keywords = ['pump', 'meme', 'token', 'market cap', 'bonding', 'curve', 'graduation', 'highest', 'top', 'trending', 'hot', 'new', 'launched']
        phantom_keywords = ['wallet', 'phantom', 'balance', 'connect', 'transaction', 'sol', 'address', 'send', 'receive', 'holdings']
        jupiter_keywords = ['swap', 'jupiter', 'exchange', 'trade', 'convert', 'slippage', 'route', 'best price']

        # Contextual phrases that indicate specific widgets
        dex_phrases = ['on dex', 'dex screener', 'chart analysis', 'price action']
        pump_phrases = ['on pump', 'pump.fun', 'pump fun', 'meme coins', 'bonding curve']
        phantom_phrases = ['my wallet', 'wallet balance', 'phantom wallet']
        jupiter_phrases = ['swap tokens', 'exchange rate', 'jupiter swap']

        # Calculate enhanced relevance scores
        dex_score = sum(1 for keyword in dex_keywords if keyword in message_lower)
        pump_score = sum(1 for keyword in pump_keywords if keyword in message_lower)
        phantom_score = sum(1 for keyword in phantom_keywords if keyword in message_lower)
        jupiter_score = sum(1 for keyword in jupiter_keywords if keyword in message_lower)

        # Boost scores for contextual phrases (higher weight)
        dex_score += sum(3 for phrase in dex_phrases if phrase in message_lower)
        pump_score += sum(3 for phrase in pump_phrases if phrase in message_lower)
        phantom_score += sum(3 for phrase in phantom_phrases if phrase in message_lower)
        jupiter_score += sum(3 for phrase in jupiter_phrases if phrase in message_lower)

        # Analyze conversation context for additional clues
        recent_messages = conversation_history[-5:] if conversation_history else []
        for msg in recent_messages:
            content = msg.get('content', '').lower()
            if 'dexscreener' in content or 'chart' in content:
                dex_score += 2
            elif 'pumpfun' in content or 'pump.fun' in content:
                pump_score += 2
            elif 'phantom' in content:
                phantom_score += 2
            elif 'jupiter' in content:
                jupiter_score += 2

        # Determine the most relevant widget context
        scores = {
            'dexscreener': dex_score,
            'pumpfun': pump_score,
            'phantom': phantom_score,
            'jupiter': jupiter_score
        }

        max_score = max(scores.values())
        if max_score == 0:
            # No clear preference, return full context
            return full_widget_context

        # Find the widget type with highest score
        relevant_widget = max(scores, key=scores.get)

        print(f"🧠 Semantic Analysis: '{user_message}' → {relevant_widget} (scores: {scores})")

        # Return enhanced prioritized context
        prioritized_context = f"## 🎯 PRIORITY CONTEXT: {relevant_widget.upper()} Widget\n"
        prioritized_context += f"**Semantic Match**: User question '{user_message}' best matches {relevant_widget} context (confidence: {max_score})\n"
        prioritized_context += f"**Action**: Prioritize {relevant_widget} data in your response\n\n"

        # Add specific guidance based on widget type
        if relevant_widget == 'pumpfun':
            prioritized_context += "**PumpFun Context**: Use specific token data (names, market caps, bonding curve progress) to answer questions about tokens, market caps, trending coins.\n\n"
        elif relevant_widget == 'dexscreener':
            prioritized_context += "**DexScreener Context**: Use chart data, trading pairs, price analysis to answer questions about charts, trading, technical analysis.\n\n"
        elif relevant_widget == 'phantom':
            prioritized_context += "**Phantom Context**: Use wallet data (balance, connection status, transactions) to answer questions about wallet, balance, holdings.\n\n"
        elif relevant_widget == 'jupiter':
            prioritized_context += "**Jupiter Context**: Use swap data (token pairs, rates, slippage) to answer questions about swapping, exchanging, trading.\n\n"

        prioritized_context += "## 📊 Full Widget Context\n"
        prioritized_context += full_widget_context

        return prioritized_context

    async def cleanup(self):
        """Cleanup resources."""
        await self.conversation_manager.cleanup()
        await self.workflow_orchestrator.cleanup()
        await self.widget_coordinator.cleanup()
        await self.file_system_manager.cleanup()
