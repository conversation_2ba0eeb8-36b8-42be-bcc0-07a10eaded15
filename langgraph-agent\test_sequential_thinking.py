#!/usr/bin/env python3
"""
Test script for Sequential Thinking MCP integration.
"""

import sys
import os
from sequential_thinking_tools import (
    sequentialthinking_Sequential_thinking,
    start_sequential_thinking_session,
    get_mcp_instance
)

def test_sequential_thinking():
    """Test the Sequential Thinking tools."""
    print("🧠 Testing Sequential Thinking MCP Integration")
    print("=" * 50)
    
    # Test 1: Start a thinking session
    print("\n1. Starting a thinking session...")
    session_result = start_sequential_thinking_session.invoke({
        "problem_description": "Analyze the potential risks and opportunities of investing in a new DeFi protocol",
        "initial_thought_estimate": 5
    })
    
    if session_result["success"]:
        print(f"✅ Session started: {session_result['session_id']}")
        print(f"📝 Problem: {session_result['problem']}")
    else:
        print("❌ Failed to start session")
        return False
    
    # Test 2: First thought
    print("\n2. Processing first thought...")
    thought_result = sequentialthinking_Sequential_thinking.invoke({
        "thought": "Let me start by identifying the key risk categories for DeFi protocols: smart contract risk, liquidity risk, governance risk, and regulatory risk.",
        "next_thought_needed": True,
        "thought_number": 1,
        "total_thoughts": 5
    })
    
    if thought_result["success"]:
        print(f"✅ Thought 1 processed successfully")
        print(f"📝 Thought: {thought_result['thought_processed'][:100]}...")
        if thought_result.get("fallback_mode"):
            print("⚠️ Using fallback mode (MCP server not available)")
    else:
        print("❌ Failed to process thought")
        return False
    
    # Test 3: Second thought with revision
    print("\n3. Processing second thought...")
    thought_result2 = sequentialthinking_Sequential_thinking.invoke({
        "thought": "Actually, I should also consider market risk and oracle risk. Let me revise my risk categories to include: smart contract risk, liquidity risk, governance risk, regulatory risk, market risk, and oracle risk.",
        "next_thought_needed": True,
        "thought_number": 2,
        "total_thoughts": 6,  # Adjusted total
        "is_revision": True,
        "revises_thought": 1
    })
    
    if thought_result2["success"]:
        print(f"✅ Thought 2 (revision) processed successfully")
        print(f"📝 Revised total thoughts to: {thought_result2['total_thoughts']}")
    else:
        print("❌ Failed to process revised thought")
        return False
    
    # Test 4: Final thought
    print("\n4. Processing final thought...")
    final_thought = sequentialthinking_Sequential_thinking.invoke({
        "thought": "Based on my analysis of the six risk categories and potential opportunities like yield farming and governance tokens, I recommend a cautious approach: start with a small allocation (1-2% of portfolio), thoroughly audit the smart contracts, verify the team's track record, and monitor the protocol's TVL and governance decisions closely.",
        "next_thought_needed": False,  # End the thinking process
        "thought_number": 6,
        "total_thoughts": 6
    })
    
    if final_thought["success"]:
        print(f"✅ Final thought processed successfully")
        print(f"🎯 Thinking process completed")
    else:
        print("❌ Failed to process final thought")
        return False
    
    print("\n" + "=" * 50)
    print("✅ Sequential Thinking MCP integration test completed successfully!")
    
    # Check MCP server status
    mcp = get_mcp_instance()
    if mcp.server_running:
        print("🔗 MCP server is running")
    else:
        print("⚠️ MCP server is not running (using fallback mode)")
    
    return True

def test_mcp_server():
    """Test MCP server connectivity."""
    print("\n🔌 Testing MCP Server Connectivity")
    print("-" * 30)
    
    mcp = get_mcp_instance()
    
    if mcp.server_running:
        print("✅ MCP server is running")
        
        # Test a simple request
        test_request = mcp.send_request("tools/list")
        if test_request["success"]:
            print("✅ MCP server responding to requests")
        else:
            print(f"⚠️ MCP server not responding: {test_request.get('error')}")
    else:
        print("❌ MCP server is not running")
        print("💡 This is normal - the tools will work in fallback mode")
    
    return True

if __name__ == "__main__":
    print("🚀 Chad GPT Sequential Thinking MCP Test Suite")
    print("=" * 60)
    
    try:
        # Test MCP server
        test_mcp_server()
        
        # Test Sequential Thinking tools
        success = test_sequential_thinking()
        
        if success:
            print("\n🎉 All tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Cleanup
        try:
            from sequential_thinking_tools import cleanup_sequential_thinking
            cleanup_sequential_thinking()
            print("\n🧹 Cleanup completed")
        except:
            pass
