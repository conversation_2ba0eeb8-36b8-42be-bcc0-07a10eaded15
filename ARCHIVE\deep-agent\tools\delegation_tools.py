"""
Enhanced Delegation Tools - Integrates existing delegation with Deep Agent domain specialists.
Provides seamless delegation to specialized agents with full context awareness.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional
import json
import uuid
import asyncio
from datetime import datetime


def get_delegation_tools(unified_agent):
    """Get enhanced delegation tools with Deep Agent domain integration."""
    
    @tool
    def delegate_to_crypto_analyst(
        task_description: str,
        context: str = "",
        analysis_type: str = "comprehensive",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Delegate cryptocurrency analysis tasks to the specialized crypto analyst.
        
        Args:
            task_description: Clear description of the crypto analysis task
            context: Additional context or background information
            analysis_type: Type of analysis (quick, standard, comprehensive)
            thread_id: Optional conversation thread ID for context
            
        Returns:
            Dictionary containing the delegation result and analysis
        """
        try:
            delegation_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().isoformat()
            
            # Create delegation record
            delegation_record = {
                "delegation_id": delegation_id,
                "agent_type": "crypto_analyst",
                "task_description": task_description,
                "context": context,
                "analysis_type": analysis_type,
                "thread_id": thread_id,
                "delegated_at": timestamp,
                "status": "processing",
                "deep_agent_delegation": True
            }
            
            # Delegate to crypto agent
            if hasattr(unified_agent, 'crypto_agent'):
                # Determine the specific crypto analysis method
                if "token" in task_description.lower() and "analyze" in task_description.lower():
                    # Extract token address if present
                    token_address = _extract_token_address(task_description)
                    if token_address:
                        result = asyncio.run(unified_agent.crypto_agent.analyze_token(
                            token_address=token_address,
                            chain="solana",  # Default, could be enhanced to detect chain
                            analysis_depth=analysis_type,
                            thread_id=thread_id
                        ))
                    else:
                        result = {"success": False, "error": "No token address found in task description"}
                
                elif "portfolio" in task_description.lower():
                    wallet_address = _extract_wallet_address(task_description)
                    if wallet_address:
                        result = asyncio.run(unified_agent.crypto_agent.portfolio_analysis(
                            wallet_address=wallet_address,
                            chain="solana",
                            thread_id=thread_id
                        ))
                    else:
                        result = {"success": False, "error": "No wallet address found in task description"}
                
                elif "pumpfun" in task_description.lower() or "trends" in task_description.lower():
                    category = _extract_pumpfun_category(task_description)
                    result = asyncio.run(unified_agent.crypto_agent.analyze_pumpfun_trends(
                        category=category,
                        limit=20,
                        thread_id=thread_id
                    ))
                
                else:
                    # General crypto analysis
                    result = {
                        "success": True,
                        "analysis": {
                            "task": task_description,
                            "context": context,
                            "analysis_type": analysis_type,
                            "recommendations": [
                                "Provide more specific task details for targeted analysis",
                                "Consider using token analysis for specific tokens",
                                "Use portfolio analysis for wallet evaluation"
                            ]
                        }
                    }
            else:
                result = {"success": False, "error": "Crypto analyst not available"}
            
            delegation_record["status"] = "completed" if result.get("success") else "failed"
            delegation_record["completed_at"] = datetime.now().isoformat()
            
            return {
                "success": result.get("success", False),
                "delegation": delegation_record,
                "agent_info": {
                    "name": "Cryptocurrency Analyst",
                    "description": "Specialized in token analysis, portfolio evaluation, and DeFi research",
                    "capabilities": ["token_analysis", "portfolio_review", "market_research", "defi_analysis"]
                },
                "result": result,
                "message": f"Delegated crypto analysis task to specialist agent",
                "function_call": {
                    "name": "delegate_to_crypto_analyst",
                    "arguments": {
                        "task_description": task_description,
                        "analysis_type": analysis_type
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to delegate to crypto analyst: {str(e)}"
            }
    
    @tool
    def delegate_to_research_specialist(
        task_description: str,
        research_type: str = "market_research",
        scope: str = "standard",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Delegate research tasks to the specialized research agent.
        
        Args:
            task_description: Clear description of the research task
            research_type: Type of research (market_research, competitive_analysis, technology_research)
            scope: Research scope (quick, standard, comprehensive)
            thread_id: Optional conversation thread ID for context
            
        Returns:
            Dictionary containing the delegation result and research findings
        """
        try:
            delegation_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().isoformat()
            
            delegation_record = {
                "delegation_id": delegation_id,
                "agent_type": "research_specialist",
                "task_description": task_description,
                "research_type": research_type,
                "scope": scope,
                "thread_id": thread_id,
                "delegated_at": timestamp,
                "status": "processing",
                "deep_agent_delegation": True
            }
            
            # Delegate to research agent
            if hasattr(unified_agent, 'research_agent'):
                if research_type == "market_research":
                    topic = _extract_research_topic(task_description)
                    result = asyncio.run(unified_agent.research_agent.conduct_market_research(
                        topic=topic,
                        scope=scope,
                        timeframe="30d",
                        thread_id=thread_id
                    ))
                
                elif research_type == "competitive_analysis":
                    company = _extract_company_name(task_description)
                    industry = _extract_industry(task_description)
                    result = asyncio.run(unified_agent.research_agent.competitive_analysis(
                        company_or_product=company,
                        industry=industry,
                        analysis_depth=scope,
                        thread_id=thread_id
                    ))
                
                elif research_type == "technology_research":
                    technology = _extract_technology(task_description)
                    result = asyncio.run(unified_agent.research_agent.technology_research(
                        technology=technology,
                        research_focus="adoption",
                        depth=scope,
                        thread_id=thread_id
                    ))
                
                else:
                    # General research
                    result = {
                        "success": True,
                        "research_results": {
                            "task": task_description,
                            "research_type": research_type,
                            "scope": scope,
                            "insights": {
                                "key_insights": [
                                    "Research task received and processed",
                                    "Recommend specifying research type for targeted analysis"
                                ]
                            }
                        }
                    }
            else:
                result = {"success": False, "error": "Research specialist not available"}
            
            delegation_record["status"] = "completed" if result.get("success") else "failed"
            delegation_record["completed_at"] = datetime.now().isoformat()
            
            return {
                "success": result.get("success", False),
                "delegation": delegation_record,
                "agent_info": {
                    "name": "Research Specialist",
                    "description": "Specialized in market research, competitive analysis, and trend identification",
                    "capabilities": ["market_research", "competitive_analysis", "technology_research", "trend_analysis"]
                },
                "result": result,
                "message": f"Delegated research task to specialist agent",
                "function_call": {
                    "name": "delegate_to_research_specialist",
                    "arguments": {
                        "task_description": task_description,
                        "research_type": research_type
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to delegate to research specialist: {str(e)}"
            }
    
    @tool
    def delegate_to_development_assistant(
        task_description: str,
        development_type: str = "widget_creation",
        complexity: str = "standard",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Delegate development tasks to the specialized development assistant.
        
        Args:
            task_description: Clear description of the development task
            development_type: Type of development (widget_creation, api_integration, code_optimization)
            complexity: Task complexity (simple, standard, complex)
            thread_id: Optional conversation thread ID for context
            
        Returns:
            Dictionary containing the delegation result and development output
        """
        try:
            delegation_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().isoformat()
            
            delegation_record = {
                "delegation_id": delegation_id,
                "agent_type": "development_assistant",
                "task_description": task_description,
                "development_type": development_type,
                "complexity": complexity,
                "thread_id": thread_id,
                "delegated_at": timestamp,
                "status": "processing",
                "deep_agent_delegation": True
            }
            
            # Delegate to development agent
            if hasattr(unified_agent, 'development_agent'):
                if development_type == "widget_creation":
                    widget_name = _extract_widget_name(task_description)
                    features = _extract_widget_features(task_description)
                    result = asyncio.run(unified_agent.development_agent.create_custom_widget(
                        widget_name=widget_name,
                        widget_description=task_description,
                        features=features,
                        thread_id=thread_id
                    ))
                
                elif development_type == "api_integration":
                    api_name = _extract_api_name(task_description)
                    result = asyncio.run(unified_agent.development_agent.integrate_new_api(
                        api_name=api_name,
                        api_documentation=task_description,
                        integration_type="rest",
                        thread_id=thread_id
                    ))
                
                elif development_type == "code_optimization":
                    code = _extract_code(task_description)
                    language = _extract_language(task_description)
                    result = asyncio.run(unified_agent.development_agent.optimize_code(
                        code=code,
                        language=language,
                        optimization_goals=["performance", "readability"],
                        thread_id=thread_id
                    ))
                
                else:
                    # General development task
                    result = {
                        "success": True,
                        "development_results": {
                            "task": task_description,
                            "development_type": development_type,
                            "complexity": complexity,
                            "recommendations": [
                                "Development task received and processed",
                                "Recommend specifying development type for targeted assistance"
                            ]
                        }
                    }
            else:
                result = {"success": False, "error": "Development assistant not available"}
            
            delegation_record["status"] = "completed" if result.get("success") else "failed"
            delegation_record["completed_at"] = datetime.now().isoformat()
            
            return {
                "success": result.get("success", False),
                "delegation": delegation_record,
                "agent_info": {
                    "name": "Development Assistant",
                    "description": "Specialized in widget development, API integration, and code optimization",
                    "capabilities": ["widget_creation", "api_integration", "code_optimization", "technical_support"]
                },
                "result": result,
                "message": f"Delegated development task to specialist agent",
                "function_call": {
                    "name": "delegate_to_development_assistant",
                    "arguments": {
                        "task_description": task_description,
                        "development_type": development_type
                    }
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to delegate to development assistant: {str(e)}"
            }
    
    @tool
    def list_domain_agent_capabilities() -> Dict[str, Any]:
        """
        List all available domain agents and their capabilities.
        
        Returns:
            Dictionary containing information about all domain agents
        """
        try:
            capabilities = [
                {
                    "name": "Cryptocurrency Analyst",
                    "agent_type": "crypto_analyst",
                    "description": "Specialized in cryptocurrency and DeFi analysis",
                    "capabilities": [
                        "Token fundamental and technical analysis",
                        "Portfolio evaluation and optimization",
                        "DeFi protocol assessment",
                        "Market trend analysis",
                        "Risk assessment and recommendations"
                    ],
                    "tools": ["token_analysis", "portfolio_review", "pumpfun_trends", "defi_opportunities"],
                    "specializations": ["Tokenomics", "On-chain Analysis", "DeFi Protocols", "Market Research"]
                },
                {
                    "name": "Research Specialist",
                    "agent_type": "research_specialist",
                    "description": "Specialized in comprehensive research and analysis",
                    "capabilities": [
                        "Market research and competitive analysis",
                        "Technology assessment and trends",
                        "Information gathering and synthesis",
                        "Report generation and insights"
                    ],
                    "tools": ["market_research", "competitive_analysis", "technology_research", "trend_analysis"],
                    "specializations": ["Market Analysis", "Competitive Intelligence", "Technology Assessment", "Trend Identification"]
                },
                {
                    "name": "Development Assistant",
                    "agent_type": "development_assistant",
                    "description": "Specialized in code generation and technical development",
                    "capabilities": [
                        "Custom widget development",
                        "API integration and development",
                        "Code optimization and review",
                        "Technical documentation"
                    ],
                    "tools": ["widget_creation", "api_integration", "code_optimization", "technical_support"],
                    "specializations": ["React Development", "API Integration", "Code Optimization", "Technical Architecture"]
                },
                {
                    "name": "Support Agent",
                    "agent_type": "support_agent",
                    "description": "Specialized in user guidance and platform support",
                    "capabilities": [
                        "Platform guidance and tutorials",
                        "Troubleshooting and problem solving",
                        "Feature explanations and best practices",
                        "User onboarding and assistance"
                    ],
                    "tools": ["user_guidance", "troubleshooting", "feature_explanation", "platform_support"],
                    "specializations": ["User Support", "Platform Guidance", "Troubleshooting", "Best Practices"]
                }
            ]
            
            return {
                "success": True,
                "total_agents": len(capabilities),
                "domain_agents": capabilities,
                "message": f"Listed capabilities for {len(capabilities)} domain agents",
                "deep_agent_enhanced": True
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to list agent capabilities: {str(e)}"
            }
    
    # Helper functions for extracting information from task descriptions
    def _extract_token_address(text: str) -> Optional[str]:
        """Extract token address from text."""
        import re
        # Look for Ethereum-style addresses (0x followed by 40 hex chars)
        eth_pattern = r'0x[a-fA-F0-9]{40}'
        # Look for Solana-style addresses (base58, typically 32-44 chars)
        sol_pattern = r'[1-9A-HJ-NP-Za-km-z]{32,44}'
        
        eth_match = re.search(eth_pattern, text)
        if eth_match:
            return eth_match.group()
        
        sol_match = re.search(sol_pattern, text)
        if sol_match:
            return sol_match.group()
        
        return None
    
    def _extract_wallet_address(text: str) -> Optional[str]:
        """Extract wallet address from text."""
        return _extract_token_address(text)  # Same pattern for now
    
    def _extract_pumpfun_category(text: str) -> str:
        """Extract PumpFun category from text."""
        text_lower = text.lower()
        if "graduated" in text_lower:
            return "graduated"
        elif "runners" in text_lower:
            return "runners"
        elif "for-you" in text_lower or "for you" in text_lower:
            return "for-you"
        else:
            return "new"
    
    def _extract_research_topic(text: str) -> str:
        """Extract research topic from text."""
        # Simple extraction - in practice, this could be more sophisticated
        return text.replace("research", "").replace("analyze", "").strip()
    
    def _extract_company_name(text: str) -> str:
        """Extract company name from text."""
        # Simple extraction - look for capitalized words
        import re
        matches = re.findall(r'\b[A-Z][a-z]+\b', text)
        return matches[0] if matches else "Unknown Company"
    
    def _extract_industry(text: str) -> str:
        """Extract industry from text."""
        text_lower = text.lower()
        if "defi" in text_lower or "decentralized finance" in text_lower:
            return "DeFi"
        elif "crypto" in text_lower or "blockchain" in text_lower:
            return "Cryptocurrency"
        elif "tech" in text_lower or "technology" in text_lower:
            return "Technology"
        else:
            return "General"
    
    def _extract_technology(text: str) -> str:
        """Extract technology name from text."""
        return _extract_research_topic(text)
    
    def _extract_widget_name(text: str) -> str:
        """Extract widget name from text."""
        # Look for widget name patterns
        import re
        widget_pattern = r'(\w+)\s*widget'
        match = re.search(widget_pattern, text.lower())
        if match:
            return match.group(1).title() + "Widget"
        else:
            return "CustomWidget"
    
    def _extract_widget_features(text: str) -> List[str]:
        """Extract widget features from text."""
        # Simple feature extraction
        features = []
        text_lower = text.lower()
        
        if "price" in text_lower:
            features.append("price_display")
        if "chart" in text_lower:
            features.append("chart_visualization")
        if "alert" in text_lower:
            features.append("alert_system")
        if "track" in text_lower:
            features.append("tracking")
        if "real-time" in text_lower or "realtime" in text_lower:
            features.append("real_time_updates")
        
        return features if features else ["basic_functionality"]
    
    def _extract_api_name(text: str) -> str:
        """Extract API name from text."""
        # Look for API name patterns
        import re
        api_pattern = r'(\w+)\s*api'
        match = re.search(api_pattern, text.lower())
        if match:
            return match.group(1).title()
        else:
            return "CustomAPI"
    
    def _extract_code(text: str) -> str:
        """Extract code from text."""
        # Look for code blocks
        import re
        code_pattern = r'```[\w]*\n(.*?)\n```'
        match = re.search(code_pattern, text, re.DOTALL)
        if match:
            return match.group(1)
        else:
            return "// No code provided"
    
    def _extract_language(text: str) -> str:
        """Extract programming language from text."""
        text_lower = text.lower()
        if "python" in text_lower:
            return "python"
        elif "javascript" in text_lower or "js" in text_lower:
            return "javascript"
        elif "typescript" in text_lower or "ts" in text_lower:
            return "typescript"
        elif "react" in text_lower:
            return "javascript"
        else:
            return "javascript"  # Default
    
    return [
        delegate_to_crypto_analyst,
        delegate_to_research_specialist,
        delegate_to_development_assistant,
        list_domain_agent_capabilities
    ]
