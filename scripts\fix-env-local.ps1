param()
$ErrorActionPreference='Stop'
$root = (Resolve-Path "$PSScriptRoot\..\").Path
$envFile = Join-Path $root '.env.local'
if (!(Test-Path $envFile)) { throw '.env.local not found' }
$raw = Get-Content $envFile -Raw
# Extract values using regex
$restUrl = 'http://*************:8082/rest/v1'
$anonMatch = [regex]::Match($raw, 'VITE_SUPABASE_ANON_KEY=([^\r\n]+)')
if (-not $anonMatch.Success) { throw 'Could not parse VITE_SUPABASE_ANON_KEY' }
$anon = $anonMatch.Groups[1].Value.Trim()
$lines = @(
  'VITE_SUPABASE_REST_URL=' + $restUrl,
  'VITE_SUPABASE_ANON_KEY=' + $anon,
  'VITE_ENABLE_API_PROXY=false'
)
Set-Content -Path $envFile -Value $lines -NoNewline:$false
Write-Host 'Normalized .env.local'
