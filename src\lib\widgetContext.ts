/**
 * Widget Context Manager
 * Handles bidirectional communication between widgets and the Deep Agent
 * Sends widget state updates to provide contextual awareness
 */

interface WidgetContextData {
  widgetId: string;
  widgetType: string;
  threadId: string;
  data: any;
  timestamp: number;
}

interface WidgetState {
  [widgetId: string]: WidgetContextData;
}

class WidgetContextManager {
  private static instance: WidgetContextManager;
  private widgetStates: WidgetState = {};
  private deepAgentUrl = (import.meta as any).env?.VITE_DEEP_AGENT_URL || 'http://localhost:8001';

  static getInstance(): WidgetContextManager {
    if (!WidgetContextManager.instance) {
      WidgetContextManager.instance = new WidgetContextManager();
    }
    return WidgetContextManager.instance;
  }

  /**
   * Register a widget and send its initial state to the agent
   */
  async registerWidget(
    widgetId: string,
    widgetType: string,
    threadId: string,
    initialData: any
  ): Promise<void> {
    const contextData: WidgetContextData = {
      widgetId,
      widgetType,
      threadId,
      data: initialData,
      timestamp: Date.now()
    };

    this.widgetStates[widgetId] = contextData;
    
    console.log(`📡 Registering widget context:`, contextData);
    await this.sendContextToAgent(contextData);
  }

  /**
   * Update widget state and notify the agent
   */
  async updateWidgetContext(
    widgetId: string,
    newData: any
  ): Promise<void> {
    if (!this.widgetStates[widgetId]) {
      console.warn(`⚠️ Widget ${widgetId} not registered`);
      return;
    }

    this.widgetStates[widgetId].data = newData;
    this.widgetStates[widgetId].timestamp = Date.now();

    console.log(`📡 Updating widget context:`, this.widgetStates[widgetId]);
    await this.sendContextToAgent(this.widgetStates[widgetId]);
  }

  /**
   * Send widget context to the Deep Agent
   */
  private async sendContextToAgent(contextData: WidgetContextData): Promise<void> {
    try {
      const payload = {
        widgetType: contextData.widgetType,
        widgetId: contextData.widgetId,
        threadId: contextData.threadId,
        data: contextData.data,
        // API expects Optional[str] for timestamp; send ISO string for compatibility
        timestamp: new Date(contextData.timestamp).toISOString()
      };

      const response = await fetch(`${this.deepAgentUrl}/api/widget-context`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        console.warn(`⚠️ Failed to send widget context: ${response.status}`);
      } else {
        console.log(`✅ Widget context sent successfully`);
      }
    } catch (error) {
      console.warn(`⚠️ Error sending widget context:`, error);
    }
  }

  /**
   * Get current state of a widget
   */
  getWidgetState(widgetId: string): WidgetContextData | null {
    return this.widgetStates[widgetId] || null;
  }

  /**
   * Get all widgets for a thread
   */
  getThreadWidgets(threadId: string): WidgetContextData[] {
    return Object.values(this.widgetStates).filter(
      widget => widget.threadId === threadId
    );
  }

  /**
   * Remove widget from context
   */
  unregisterWidget(widgetId: string): void {
    delete this.widgetStates[widgetId];
    console.log(`🗑️ Unregistered widget: ${widgetId}`);
  }
}

// Export singleton instance
export const widgetContextManager = WidgetContextManager.getInstance();

// Helper functions for common widget context operations
export const registerWidgetContext = (
  widgetId: string,
  widgetType: string,
  threadId: string,
  initialData: any
) => widgetContextManager.registerWidget(widgetId, widgetType, threadId, initialData);

export const updateWidgetContext = (widgetId: string, data: any) => 
  widgetContextManager.updateWidgetContext(widgetId, data);

export const unregisterWidgetContext = (widgetId: string) => 
  widgetContextManager.unregisterWidget(widgetId);

// Context data formatters for different widget types
export const formatPumpFunContext = (tokens: any[], category: string, viewMode: string) => ({
  category,
  viewMode,
  tokenCount: tokens.length,
  topTokens: tokens.slice(0, 10).map(token => ({
    symbol: token.symbol,
    name: token.name,
    mint: token.mint,
    marketCap: token.market_cap || token.marketCap,
    price: token.usd_market_cap || token.price,
    bondingCurveProgress: token.bonding_curve_progress,
    // Enhanced context data
    description: token.description?.substring(0, 100) || 'No description',
    website: token.website,
    twitter: token.twitter,
    telegram: token.telegram,
    isGraduated: token.complete || false,
    createdAt: token.created_timestamp,
    volume24h: token.volume_24h,
    holders: token.holder_count
  })),
  // Add summary statistics
  totalMarketCap: tokens.reduce((sum, token) => sum + (token.market_cap || token.marketCap || 0), 0),
  averageProgress: tokens.reduce((sum, token) => sum + (token.bonding_curve_progress || 0), 0) / tokens.length,
  graduatedCount: tokens.filter(token => token.complete).length,
  lastUpdated: Date.now()
});

export const formatDexScreenerContext = (viewMode: string, selectedToken?: any) => ({
  viewMode,
  selectedToken: selectedToken ? {
    address: selectedToken.address,
    symbol: selectedToken.symbol,
    name: selectedToken.name,
    price: selectedToken.price
  } : null,
  lastUpdated: Date.now()
});

export const formatJupiterContext = (fromToken?: any, toToken?: any, amount?: number) => ({
  fromToken: fromToken ? {
    symbol: fromToken.symbol,
    address: fromToken.address,
    amount
  } : null,
  toToken: toToken ? {
    symbol: toToken.symbol,
    address: toToken.address
  } : null,
  lastUpdated: Date.now()
});
