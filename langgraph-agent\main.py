"""
FastAPI server for Chad GPT LangGraph agent.
"""

import os
from typing import List, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from dotenv import load_dotenv
from agent import get_agent
from pathlib import Path
import json

# Load environment variables
load_dotenv()

app = FastAPI(title="Chad GPT LangGraph Agent", version="1.0.0")

# Configure CORS - Allow all origins for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=False,  # Set to False when using allow_origins=["*"]
    allow_methods=["*"],
    allow_headers=["*"],
)


class Message(BaseModel):
    """Message model matching frontend interface."""
    role: str
    content: str
    image: Optional[str] = None


class ChatRequest(BaseModel):
    """Chat request model."""
    messages: List[Message]
    threadId: Optional[str] = None


class FunctionCall(BaseModel):
    """Function call model."""
    name: str
    arguments: dict = {}


class ChatResponse(BaseModel):
    """Enhanced chat response model supporting thinking outputs."""
    content: str
    thinking: Optional[str] = None  # For models that output thinking process
    function_call: Optional[FunctionCall] = None
    model_info: Optional[dict] = None  # Additional model metadata


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"message": "Chad GPT LangGraph Agent is running!"}


@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "healthy", "service": "chad-gpt-langgraph-agent"}

# Alias /api/health to match frontend expectations
@app.get("/api/health")
async def api_health():
    return {"status": "healthy", "service": "chad-gpt-langgraph-agent"}


@app.options("/api/chat")
async def chat_options():
    """Handle CORS preflight for chat endpoint."""
    return {"message": "OK"}


@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Main chat endpoint that processes messages through the LangGraph REACT agent.

    Args:
        request: ChatRequest with messages and optional threadId

    Returns:
        ChatResponse with content and optional function_call
    """
    try:
        # Get the agent instance
        agent = get_agent()

        # Convert Pydantic models to dictionaries
        messages = [msg.dict() for msg in request.messages]

        # Process through the agent
        response = await agent.chat(messages, request.threadId)

        # Convert function_call to Pydantic model if present
        function_call = None
        if response.get("function_call"):
            function_call = FunctionCall(**response["function_call"])

        return ChatResponse(
            content=response["content"],
            thinking=response.get("thinking"),
            function_call=function_call,
            model_info=response.get("model_info")

        )
    except Exception as e:
        print(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# Streaming chat endpoint (Server-Sent Events)
@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    try:
        agent = get_agent()
        messages = [msg.dict() for msg in request.messages]

        async def event_generator():
            # Use the agent's native streaming to yield incremental tokens/segments ASAP
            for item in agent.stream_chat(messages, request.threadId):
                # item is a dict with keys: type, content, function_name, function_args, metadata
                yield f"data: {json.dumps(item)}\n\n"

        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
        return StreamingResponse(event_generator(), media_type="text/event-stream", headers=headers)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Streaming error: {str(e)}")


# Widget context ingestion endpoint
class WidgetContextPayload(BaseModel):
    widgetType: str
    widgetId: str
    threadId: str
    data: dict
    timestamp: Optional[str] = None

@app.post("/api/widget-context")
async def widget_context(payload: WidgetContextPayload):
    try:
        # Persist lightweight context snapshots to an archive folder under langgraph-agent
        archive_dir = Path(__file__).parent / "ARCHIVE" / "widget_context"
        archive_dir.mkdir(parents=True, exist_ok=True)
        file_path = archive_dir / f"{payload.threadId}__{payload.widgetType}__{payload.widgetId}.json"

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(payload.dict(), f, ensure_ascii=False, indent=2)

        return {"status": "ok", "path": str(file_path)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to store widget context: {str(e)}")



if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("PORT", 8001))
    print(f"Starting Chad GPT LangGraph Agent on port {port}")

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
