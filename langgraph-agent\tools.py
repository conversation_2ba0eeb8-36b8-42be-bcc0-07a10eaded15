"""
Tools for the Chad GPT LangGraph agent.
These tools handle widget functionality for PumpFun and DexScreener.
"""

from langchain_core.tools import tool
from typing import Dict, Any


@tool
def show_pumpfun_widget() -> Dict[str, Any]:
    """
    Display the PumpFun interactive widget for browsing and exploring meme tokens.

    ONLY use this tool when the user specifically wants to:
    - Browse or explore pump.fun tokens interactively
    - See the actual pump.fun interface to discover new tokens
    - Interact with pump.fun's live token listings

    DO NOT use for general questions about pump.fun or meme tokens - answer those conversationally.
    """
    return {
        "widget_type": "pumpfun",
        "description": "Displaying PumpFun widget for interactive token browsing"
    }


@tool
def show_dexscreener_widget() -> Dict[str, Any]:
    """
    Display the DexScreener interactive widget for live trading charts and DEX data.

    ONLY use this tool when the user specifically wants to:
    - View live price charts and trading data
    - Analyze specific token charts with technical indicators
    - Monitor real-time DEX trading activity
    - Access the actual DexScreener interface for detailed analysis

    DO NOT use for general questions about DEX trading or price info - answer those conversationally.
    """
    return {
        "widget_type": "dexscreener",
        "description": "Displaying DexScreener widget for live trading charts"
    }


@tool
def show_jupiter_widget() -> Dict[str, Any]:
    """
    Display the Jupiter interactive widget for token swapping.

    ONLY use this tool when the user specifically wants to:
    - Actually perform a token swap transaction
    - Access Jupiter's swap interface to trade tokens
    - Compare swap routes and execute trades

    DO NOT use for general questions about swapping or Jupiter - answer those conversationally.
    """
    return {
        "widget_type": "jupiter",
        "description": "Displaying Jupiter widget for token swapping"
    }


@tool
def show_phantom_widget() -> Dict[str, Any]:
    """
    Display the Phantom wallet interactive widget for wallet management.

    ONLY use this tool when the user specifically wants to:
    - Access their Phantom wallet interface
    - Manage wallet settings or view balances
    - Connect or interact with their actual wallet

    DO NOT use for general questions about wallets or Phantom - answer those conversationally.
    """
    return {
        "widget_type": "phantom",
        "description": "Displaying Phantom wallet widget"
    }


@tool
def show_external_wallet_widget() -> Dict[str, Any]:
    """
    Display the External Wallet Inspector widget for analyzing any wallet address.

    ONLY use this tool when the user specifically wants to:
    - Investigate or analyze an external wallet address
    - Research wallet holdings, NFTs, and transaction history
    - Copy trade or study another trader's wallet
    - Analyze wallet activity for research purposes

    DO NOT use for general questions about wallets - answer those conversationally.
    """
    return {
        "widget_type": "external_wallet",
        "description": "Displaying External Wallet Inspector widget for wallet analysis"
    }


@tool
def show_token_chart_widget(token_address: str, chain: str = "sol") -> Dict[str, Any]:
    """
    Display an interactive token chart widget for a specific contract address.

    Use this tool when the user provides a contract address and wants to see:
    - Live price charts and trading data for the token
    - Technical analysis and market metrics
    - Real-time trading activity and volume

    Args:
        token_address: The contract address of the token
        chain: The blockchain network (e.g., "solana", "ethereum", "bsc", "base", "arbitrum", "polygon", "xrpl")

    ONLY use when a valid contract address is provided in the user's message.
    """
    return {
        "widget_type": "tokenchart",
        "token_address": token_address,
        "chain": chain,
        "description": f"Displaying chart for token {token_address} on {chain.upper()}"
    }


# List of all available tools
TOOLS = [show_pumpfun_widget, show_dexscreener_widget, show_jupiter_widget, show_phantom_widget, show_external_wallet_widget, show_token_chart_widget]
