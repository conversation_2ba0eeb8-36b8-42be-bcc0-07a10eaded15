# Chad GPT Deep Agent Tools Reference

## Overview

This document provides comprehensive reference for all tools available in the Chad GPT Deep Agent system. Tools are organized by category and provide specific functionality for different aspects of the platform.

## Tool Categories

### Planning Tools

Tools for workflow management and task orchestration.

#### `create_workflow_plan`

Creates a structured plan for complex multi-step tasks.

**Parameters:**
- `task_description` (str): High-level description of the main task
- `subtasks` (List[str]): List of specific subtasks to accomplish
- `dependencies` (Optional[Dict]): Task dependencies and ordering
- `estimated_duration` (Optional[str]): Estimated completion time

**Returns:**
```json
{
  "success": true,
  "plan_id": "plan_12345",
  "task_description": "Comprehensive token analysis",
  "subtasks": [
    {"id": "1", "description": "Gather basic token info", "status": "pending"},
    {"id": "2", "description": "Perform technical analysis", "status": "pending"}
  ],
  "created_at": "2024-01-15T10:30:00Z"
}
```

**Usage Example:**
```python
plan = create_workflow_plan(
    task_description="Analyze DeFi protocol for investment potential",
    subtasks=[
        "Research protocol fundamentals",
        "Analyze tokenomics and governance",
        "Assess smart contract security",
        "Evaluate market position",
        "Generate investment recommendation"
    ]
)
```

#### `update_plan_status`

Updates the status of a workflow plan or specific subtasks.

**Parameters:**
- `plan_id` (str): Unique identifier of the plan
- `subtask_id` (Optional[str]): Specific subtask to update
- `status` (str): New status (pending, in_progress, completed, skipped)
- `notes` (Optional[str]): Additional notes or comments

**Returns:**
```json
{
  "success": true,
  "plan_id": "plan_12345",
  "updated_subtask": "1",
  "new_status": "completed",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### Widget Management Tools

Tools for creating, updating, and managing widgets in the conversation interface.

#### `create_widget`

Creates a new widget instance in the conversation.

**Parameters:**
- `widget_type` (str): Type of widget (pumpfun, token_chart, jupiter, phantom, dexscreener)
- `thread_id` (str): Conversation thread identifier
- `initial_data` (Optional[str]): JSON string of initial widget data
- `metadata` (Optional[str]): JSON string of widget metadata

**Returns:**
```json
{
  "success": true,
  "widget_id": "widget_abc123",
  "widget_type": "token_chart",
  "message": "Created token_chart widget with ID widget_abc123"
}
```

**Usage Example:**
```python
widget = create_widget(
    widget_type="token_chart",
    thread_id="conversation_001",
    initial_data='{"token_address": "0x1234...", "chain": "ethereum"}',
    metadata='{"source": "agent_analysis"}'
)
```

#### `update_widget_data`

Updates data for an existing widget.

**Parameters:**
- `widget_id` (str): Unique identifier of the widget
- `data` (str): JSON string of new data to update
- `metadata` (Optional[str]): JSON string of metadata to update

**Returns:**
```json
{
  "success": true,
  "widget_id": "widget_abc123",
  "message": "Updated widget widget_abc123 successfully"
}
```

#### `show_pumpfun_widget`

Displays the PumpFun interactive widget for browsing meme tokens.

**Parameters:**
- `thread_id` (str): Conversation thread identifier
- `category` (Optional[str]): Token category (new, for-you, graduated, runners)
- `filters` (Optional[str]): JSON string of additional filters

**Returns:**
```json
{
  "success": true,
  "widget_type": "pumpfun",
  "widget_id": "widget_pf001",
  "category": "new",
  "message": "Displaying PumpFun widget with new tokens"
}
```

#### `show_token_chart`

Displays a token chart widget for price analysis.

**Parameters:**
- `thread_id` (str): Conversation thread identifier
- `token_address` (str): Contract address of the token
- `chain` (Optional[str]): Blockchain network (solana, ethereum, bsc, etc.)
- `chart_type` (Optional[str]): Chart provider (gmgn, dexscreener)

**Returns:**
```json
{
  "success": true,
  "widget_type": "token_chart",
  "widget_id": "widget_tc001",
  "token_address": "0x1234...",
  "chain": "ethereum",
  "message": "Displaying ethereum token chart for 0x1234..."
}
```

### File System Tools

Tools for persistent storage and file management.

#### `write_file`

Writes content to a file in the agent workspace.

**Parameters:**
- `path` (str): File path relative to workspace root
- `content` (str): Content to write to the file
- `encoding` (Optional[str]): File encoding (default: utf-8)

**Returns:**
```json
{
  "success": true,
  "path": "analysis/token_report.json",
  "bytes_written": 2048,
  "message": "Successfully wrote 2048 bytes to analysis/token_report.json"
}
```

#### `read_file`

Reads content from a file in the agent workspace.

**Parameters:**
- `path` (str): File path relative to workspace root
- `encoding` (Optional[str]): File encoding (default: utf-8)

**Returns:**
```json
{
  "success": true,
  "path": "analysis/token_report.json",
  "content": "...",
  "size": 2048,
  "last_modified": "2024-01-15T10:30:00Z"
}
```

#### `list_files`

Lists files and directories in the agent workspace.

**Parameters:**
- `path` (Optional[str]): Directory path to list (default: root)
- `recursive` (Optional[bool]): Whether to list recursively
- `pattern` (Optional[str]): File pattern to match

**Returns:**
```json
{
  "success": true,
  "path": "analysis/",
  "files": [
    {"name": "token_report.json", "type": "file", "size": 2048},
    {"name": "charts/", "type": "directory", "items": 5}
  ],
  "total_files": 10,
  "total_directories": 3
}
```

### Delegation Tools

Tools for delegating tasks to specialized sub-agents.

#### `delegate_to_crypto_analyst`

Delegates cryptocurrency analysis tasks to the crypto specialist.

**Parameters:**
- `task_type` (str): Type of analysis (token_analysis, portfolio_review, market_research)
- `task_data` (str): JSON string with task-specific data
- `priority` (Optional[str]): Task priority (low, medium, high)

**Returns:**
```json
{
  "success": true,
  "delegation_id": "del_crypto_001",
  "agent_type": "crypto_analyst",
  "task_type": "token_analysis",
  "status": "assigned",
  "estimated_completion": "2024-01-15T11:00:00Z"
}
```

#### `delegate_to_research_specialist`

Delegates research tasks to the research specialist.

**Parameters:**
- `research_type` (str): Type of research (market_research, competitive_analysis, trend_analysis)
- `research_data` (str): JSON string with research parameters
- `scope` (Optional[str]): Research scope (quick, standard, comprehensive)

**Returns:**
```json
{
  "success": true,
  "delegation_id": "del_research_001",
  "agent_type": "research_specialist",
  "research_type": "market_research",
  "status": "assigned"
}
```

#### `delegate_to_developer`

Delegates development tasks to the development assistant.

**Parameters:**
- `dev_task_type` (str): Type of development task (widget_creation, api_integration, code_optimization)
- `task_specifications` (str): JSON string with development specifications
- `complexity` (Optional[str]): Task complexity (simple, moderate, complex)

**Returns:**
```json
{
  "success": true,
  "delegation_id": "del_dev_001",
  "agent_type": "developer_assistant",
  "task_type": "widget_creation",
  "status": "assigned"
}
```

### Platform Integration Tools

Tools for integrating with the Chad GPT platform features.

#### `switch_ai_model`

Switches the active AI model for the conversation.

**Parameters:**
- `model_name` (str): Name of the model to switch to

**Returns:**
```json
{
  "success": true,
  "new_model": "anthropic/claude-3.5-sonnet",
  "message": "Switched to model anthropic/claude-3.5-sonnet successfully"
}
```

#### `get_conversation_state`

Gets current conversation state for a thread.

**Parameters:**
- `thread_id` (str): Conversation thread identifier

**Returns:**
```json
{
  "success": true,
  "conversation_state": {
    "thread_id": "conv_001",
    "message_count": 15,
    "active_widgets": ["widget_001", "widget_002"],
    "workflow_ids": ["plan_001"],
    "user_preferences": {}
  }
}
```

#### `update_user_preferences`

Updates user preferences for the platform.

**Parameters:**
- `user_id` (str): Unique identifier of the user
- `preferences` (str): JSON string of preferences to update

**Returns:**
```json
{
  "success": true,
  "user_id": "user_123",
  "message": "User preferences updated successfully"
}
```

### Sequential Thinking Tools

Tools for structured reasoning and problem-solving.

#### `sequential_thinking`

Performs structured step-by-step thinking for complex problems.

**Parameters:**
- `problem_description` (str): Description of the problem to solve
- `thinking_steps` (int): Number of thinking steps to perform
- `context` (Optional[str]): Additional context for the problem

**Returns:**
```json
{
  "success": true,
  "problem": "Analyze DeFi protocol risks",
  "thinking_steps": [
    {"step": 1, "thought": "Identify key risk categories", "reasoning": "..."},
    {"step": 2, "thought": "Assess smart contract risks", "reasoning": "..."}
  ],
  "conclusion": "Protocol has medium risk profile with specific concerns in...",
  "confidence": 0.85
}
```

## Tool Usage Patterns

### Workflow Orchestration Pattern

```python
# 1. Create a plan
plan = create_workflow_plan(
    task_description="Comprehensive market analysis",
    subtasks=["data_collection", "analysis", "reporting"]
)

# 2. Execute steps with delegation
crypto_analysis = delegate_to_crypto_analyst(
    task_type="market_research",
    task_data='{"market": "defi", "timeframe": "30d"}'
)

# 3. Update progress
update_plan_status(
    plan_id=plan["plan_id"],
    subtask_id="1",
    status="completed"
)

# 4. Save results
write_file(
    path="reports/market_analysis.json",
    content=json.dumps(crypto_analysis)
)
```

### Widget Integration Pattern

```python
# 1. Create widget for visualization
widget = create_widget(
    widget_type="token_chart",
    thread_id=thread_id,
    initial_data='{"token_address": token_address}'
)

# 2. Update widget with analysis results
update_widget_data(
    widget_id=widget["widget_id"],
    data='{"analysis_complete": true, "recommendation": "BUY"}'
)

# 3. Create additional widgets as needed
pumpfun_widget = show_pumpfun_widget(
    thread_id=thread_id,
    category="graduated"
)
```

### Multi-Agent Collaboration Pattern

```python
# 1. Research phase
research_task = delegate_to_research_specialist(
    research_type="competitive_analysis",
    research_data='{"company": "Uniswap", "industry": "DEX"}'
)

# 2. Technical analysis phase
crypto_task = delegate_to_crypto_analyst(
    task_type="protocol_analysis",
    task_data='{"protocol": "Uniswap", "version": "v3"}'
)

# 3. Development phase
dev_task = delegate_to_developer(
    dev_task_type="widget_creation",
    task_specifications='{"widget_name": "UniswapAnalyzer"}'
)

# 4. Synthesize results
final_report = sequential_thinking(
    problem_description="Synthesize multi-agent analysis results",
    thinking_steps=5,
    context=json.dumps([research_task, crypto_task, dev_task])
)
```

## Error Handling

All tools return a consistent error format:

```json
{
  "success": false,
  "error": "Specific error message",
  "error_code": "TOOL_ERROR_001",
  "timestamp": "2024-01-15T10:30:00Z",
  "context": {
    "tool_name": "create_widget",
    "parameters": {...}
  }
}
```

## Best Practices

1. **Always check tool return values** for success/failure status
2. **Use appropriate tool combinations** for complex workflows
3. **Save important results** to the file system for persistence
4. **Update workflow progress** regularly for transparency
5. **Handle errors gracefully** with fallback strategies
6. **Use delegation** for specialized tasks outside core competency
7. **Create widgets** to enhance user experience with visualizations
8. **Maintain conversation state** for context preservation

## Tool Development Guidelines

When creating new tools:

1. Follow the consistent parameter and return value patterns
2. Include comprehensive error handling
3. Provide clear documentation and examples
4. Implement proper input validation
5. Support both synchronous and asynchronous operations
6. Include unit tests for all functionality
7. Follow the platform's security and privacy guidelines
