"""
Quickstart example for Chad GPT Deep Agent platform.
Demonstrates basic usage and key features of the deep agent system.
"""

import asyncio
import os
from datetime import datetime

# Import the core platform agent
from src.core.platform_agent_loop import create_platform_agent
from domains.cryptocurrency.agent import create_crypto_agent
from domains.research.agent import create_research_agent
from domains.development.agent import create_development_agent


async def main():
    """Main quickstart demonstration."""
    print("🚀 Chad GPT Deep Agent Quickstart")
    print("=" * 50)
    
    # Set up environment (you'll need to set your actual API key)
    os.environ["OPENROUTER_API_KEY"] = "your-openrouter-api-key-here"
    
    # Create the main platform agent
    print("\n1. Creating Platform Agent...")
    config = {
        "model_name": "anthropic/claude-3.5-sonnet",
        "temperature": 0.7,
        "max_tokens": 2000
    }
    
    platform_agent = create_platform_agent(config)
    print("✅ Platform agent created successfully!")
    
    # Example 1: Basic conversation with streaming
    print("\n2. Basic Conversation Example")
    print("-" * 30)
    
    thread_id = "quickstart_demo_001"
    message = "Hello! Can you explain what you can do?"
    
    print(f"User: {message}")
    print("Assistant: ", end="", flush=True)
    
    async for response in platform_agent.process_message(
        message=message,
        thread_id=thread_id,
        stream=True
    ):
        if response.get("type") == "content":
            print(response.get("content", ""), end="", flush=True)
    
    print("\n")
    
    # Example 2: Widget creation
    print("\n3. Widget Creation Example")
    print("-" * 30)
    
    widget_message = "Show me a PumpFun widget with new tokens"
    print(f"User: {widget_message}")
    
    async for response in platform_agent.process_message(
        message=widget_message,
        thread_id=thread_id,
        stream=True
    ):
        if response.get("type") == "tool_call":
            print(f"🔧 Tool called: {response.get('tool_name')}")
        elif response.get("type") == "tool_result":
            result = response.get("result", {})
            if result.get("widget_type"):
                print(f"📊 Created widget: {result['widget_type']} (ID: {result.get('widget_id', 'N/A')})")
    
    # Example 3: Cryptocurrency analysis with specialized agent
    print("\n4. Cryptocurrency Analysis Example")
    print("-" * 40)
    
    crypto_agent = create_crypto_agent(platform_agent)
    
    # Analyze a token (using a mock address for demo)
    token_address = "******************************************"
    
    print(f"Analyzing token: {token_address}")
    
    analysis_result = await crypto_agent.analyze_token(
        token_address=token_address,
        chain="ethereum",
        analysis_depth="quick",
        thread_id=thread_id
    )
    
    if analysis_result["success"]:
        print("✅ Token analysis completed!")
        analysis = analysis_result["analysis"]
        
        if "basic_info" in analysis:
            basic_info = analysis["basic_info"]
            print(f"   Token: {basic_info.get('name', 'Unknown')} ({basic_info.get('symbol', 'N/A')})")
        
        if "recommendations" in analysis:
            rec = analysis["recommendations"]
            print(f"   Recommendation: {rec.get('recommendation', 'N/A')} (Confidence: {rec.get('confidence', 0):.1%})")
    else:
        print("❌ Token analysis failed")
    
    # Example 4: Market research with specialized agent
    print("\n5. Market Research Example")
    print("-" * 30)
    
    research_agent = create_research_agent(platform_agent)
    
    research_topic = "DeFi Protocol Trends"
    print(f"Researching: {research_topic}")
    
    research_result = await research_agent.conduct_market_research(
        topic=research_topic,
        scope="quick",
        timeframe="30d",
        thread_id=thread_id
    )
    
    if research_result["success"]:
        print("✅ Market research completed!")
        results = research_result["research_results"]
        
        if "insights" in results:
            insights = results["insights"]
            key_insights = insights.get("key_insights", [])
            if key_insights:
                print(f"   Key Insight: {key_insights[0]}")
    else:
        print("❌ Market research failed")
    
    # Example 5: Custom widget development
    print("\n6. Widget Development Example")
    print("-" * 35)
    
    dev_agent = create_development_agent(platform_agent)
    
    widget_name = "PriceAlertWidget"
    print(f"Creating custom widget: {widget_name}")
    
    widget_result = await dev_agent.create_custom_widget(
        widget_name=widget_name,
        widget_description="Widget for setting price alerts on tokens",
        features=["price_monitoring", "alert_notifications", "threshold_settings"],
        data_sources=["coingecko", "dexscreener"],
        thread_id=thread_id
    )
    
    if widget_result["success"]:
        print("✅ Widget development completed!")
        widget_info = widget_result["widget_results"]
        print(f"   Widget: {widget_info['widget_name']}")
        print(f"   Components generated: {len(widget_info['generated_code'])} files")
    else:
        print("❌ Widget development failed")
    
    # Example 6: File system operations
    print("\n7. File System Operations Example")
    print("-" * 40)
    
    # Save analysis results
    analysis_summary = {
        "session_id": thread_id,
        "timestamp": datetime.now().isoformat(),
        "activities": [
            "Basic conversation",
            "Widget creation",
            "Token analysis",
            "Market research",
            "Widget development"
        ],
        "results": {
            "widgets_created": 2,
            "analyses_completed": 1,
            "research_topics": 1
        }
    }
    
    save_result = await platform_agent.file_manager.write_file(
        path="sessions/quickstart_summary.json",
        content=analysis_summary
    )
    
    if save_result["success"]:
        print("✅ Session summary saved to file system")
        print(f"   File: {save_result['path']}")
        print(f"   Size: {save_result['bytes_written']} bytes")
    
    # List files in the workspace
    files_result = await platform_agent.file_manager.list_files(path="sessions")
    if files_result["success"]:
        print(f"   Files in sessions/: {files_result['total_files']}")
    
    # Example 7: Conversation state and preferences
    print("\n8. Conversation State Example")
    print("-" * 35)
    
    # Get conversation state
    conv_state = await platform_agent.get_conversation_state(thread_id)
    if conv_state:
        context = conv_state.get("context")
        if context:
            print(f"✅ Conversation state retrieved")
            print(f"   Messages: {context.get('message_count', 0)}")
            print(f"   Active widgets: {len(context.get('active_widgets', []))}")
    
    # Update user preferences
    user_preferences = {
        "theme": "dark",
        "default_model": "anthropic/claude-3.5-sonnet",
        "auto_save": True,
        "widget_preferences": {
            "auto_refresh": True,
            "refresh_interval": 30
        }
    }
    
    await platform_agent.update_user_preferences("quickstart_user", user_preferences)
    print("✅ User preferences updated")
    
    # Example 8: Workflow management
    print("\n9. Workflow Management Example")
    print("-" * 35)
    
    # Create a simple workflow
    workflow_definition = {
        "name": "Quickstart Demo Workflow",
        "description": "Demonstration workflow for quickstart",
        "tasks": [
            {
                "name": "Initialize Session",
                "description": "Set up demo session",
                "task_type": "initialization",
                "dependencies": []
            },
            {
                "name": "Run Examples",
                "description": "Execute demonstration examples",
                "task_type": "execution",
                "dependencies": ["initialize_session"]
            },
            {
                "name": "Generate Summary",
                "description": "Create session summary",
                "task_type": "reporting",
                "dependencies": ["run_examples"]
            }
        ]
    }
    
    workflow_id = await platform_agent.create_workflow(
        workflow_definition=workflow_definition,
        thread_id=thread_id
    )
    
    print(f"✅ Workflow created: {workflow_id}")
    
    # Get workflow status
    workflow_status = await platform_agent.get_workflow_status(workflow_id)
    if workflow_status:
        print(f"   Status: {workflow_status['status']}")
        print(f"   Tasks: {len(workflow_status['tasks'])}")
    
    # Example 9: Performance and statistics
    print("\n10. System Statistics")
    print("-" * 25)
    
    # Get workspace statistics
    workspace_stats = await platform_agent.file_manager.get_workspace_stats()
    if workspace_stats["success"]:
        print("✅ Workspace statistics:")
        print(f"   Total files: {workspace_stats['file_count']}")
        print(f"   Total size: {workspace_stats['total_size_mb']} MB")
        print(f"   Cache size: {workspace_stats['cache_size_mb']} MB")
    
    # Get active widgets
    active_widgets = await platform_agent.get_active_widgets(thread_id)
    print(f"   Active widgets: {len(active_widgets)}")
    
    # Cleanup
    print("\n11. Cleanup")
    print("-" * 15)
    
    print("🧹 Performing cleanup...")
    await platform_agent.cleanup()
    print("✅ Cleanup completed!")
    
    print("\n" + "=" * 50)
    print("🎉 Quickstart demonstration completed!")
    print("\nNext steps:")
    print("1. Set your actual OpenRouter API key")
    print("2. Explore the documentation in docs/")
    print("3. Try the domain-specific examples")
    print("4. Create your own custom agents and workflows")
    print("5. Build custom widgets for your use cases")


async def simple_conversation_example():
    """Simple conversation example for quick testing."""
    print("💬 Simple Conversation Example")
    print("-" * 35)
    
    # Create agent with minimal config
    platform_agent = create_platform_agent({
        "model_name": "anthropic/claude-3.5-sonnet"
    })
    
    # Simple conversation
    message = "What can you help me with?"
    thread_id = "simple_test"
    
    print(f"User: {message}")
    print("Assistant: ", end="", flush=True)
    
    try:
        async for response in platform_agent.process_message(
            message=message,
            thread_id=thread_id,
            stream=True
        ):
            if response.get("type") == "content":
                print(response.get("content", ""), end="", flush=True)
        
        print("\n✅ Simple conversation completed!")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure to set your OPENROUTER_API_KEY environment variable")
    
    finally:
        await platform_agent.cleanup()


if __name__ == "__main__":
    print("Chad GPT Deep Agent Platform - Quickstart Examples")
    print("=" * 55)
    
    # Check if API key is set
    if not os.getenv("OPENROUTER_API_KEY"):
        print("⚠️  Warning: OPENROUTER_API_KEY not set")
        print("Set your API key: export OPENROUTER_API_KEY='your-key-here'")
        print("\nRunning simple example without API calls...")
        asyncio.run(simple_conversation_example())
    else:
        print("🔑 API key found, running full demonstration...")
        asyncio.run(main())
