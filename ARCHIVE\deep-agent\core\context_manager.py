"""
Context Manager - Advanced context awareness for the Deep Agent system.
Maintains comprehensive context across conversations, tools, widgets, and workflows.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json


class ContextManager:
    """
    Manages comprehensive context awareness for the Deep Agent system.
    Tracks conversation context, tool usage, widget states, and workflow progress.
    """

    def __init__(self):
        """Initialize context manager."""
        self.conversation_contexts: Dict[str, Dict[str, Any]] = {}
        self.tool_usage_history: Dict[str, List[Dict[str, Any]]] = {}
        self.widget_contexts: Dict[str, Dict[str, Any]] = {}
        self.workflow_contexts: Dict[str, Dict[str, Any]] = {}
        self.user_interaction_patterns: Dict[str, Dict[str, Any]] = {}
        
        # Context analysis settings
        self.context_retention_days = 30
        self.max_context_items = 1000
    
    def update_conversation_context(
        self,
        thread_id: str,
        message: str,
        role: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Update conversation context with new message."""
        if thread_id not in self.conversation_contexts:
            self.conversation_contexts[thread_id] = {
                "thread_id": thread_id,
                "created_at": datetime.now().isoformat(),
                "message_count": 0,
                "topics": [],
                "sentiment": "neutral",
                "complexity_level": "standard",
                "user_preferences": {},
                "active_workflows": [],
                "active_widgets": [],
                "recent_tools": [],
                "context_summary": ""
            }
        
        context = self.conversation_contexts[thread_id]
        context["message_count"] += 1
        context["last_updated"] = datetime.now().isoformat()
        
        # Analyze message for topics and sentiment
        topics = self._extract_topics(message)
        context["topics"].extend(topics)
        context["topics"] = list(set(context["topics"]))  # Remove duplicates
        
        # Update sentiment
        context["sentiment"] = self._analyze_sentiment(message)
        
        # Update complexity level
        context["complexity_level"] = self._assess_complexity(message, context)
        
        # Add metadata if provided
        if metadata:
            context.setdefault("metadata", {}).update(metadata)
        
        # Update context summary
        context["context_summary"] = self._generate_context_summary(context)
    
    def add_tool_usage(
        self,
        thread_id: str,
        tool_name: str,
        tool_args: Dict[str, Any],
        tool_result: Dict[str, Any]
    ):
        """Record tool usage for context awareness."""
        if thread_id not in self.tool_usage_history:
            self.tool_usage_history[thread_id] = []
        
        tool_record = {
            "tool_name": tool_name,
            "tool_args": tool_args,
            "tool_result": tool_result,
            "timestamp": datetime.now().isoformat(),
            "success": tool_result.get("success", False)
        }
        
        self.tool_usage_history[thread_id].append(tool_record)
        
        # Update conversation context with tool usage
        if thread_id in self.conversation_contexts:
            context = self.conversation_contexts[thread_id]
            context["recent_tools"] = [
                record["tool_name"] for record in self.tool_usage_history[thread_id][-5:]
            ]
        
        # Trim history if too long
        if len(self.tool_usage_history[thread_id]) > self.max_context_items:
            self.tool_usage_history[thread_id] = self.tool_usage_history[thread_id][-self.max_context_items:]
    
    def update_widget_context(
        self,
        thread_id: str,
        widget_id: str,
        widget_type: str,
        widget_data: Dict[str, Any]
    ):
        """Update widget context information."""
        if thread_id not in self.widget_contexts:
            self.widget_contexts[thread_id] = {}
        
        self.widget_contexts[thread_id][widget_id] = {
            "widget_id": widget_id,
            "widget_type": widget_type,
            "data": widget_data,
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "interaction_count": 0
        }
        
        # Update conversation context
        if thread_id in self.conversation_contexts:
            context = self.conversation_contexts[thread_id]
            context["active_widgets"] = list(self.widget_contexts[thread_id].keys())
    
    def update_workflow_context(
        self,
        thread_id: str,
        workflow_id: str,
        workflow_status: Dict[str, Any]
    ):
        """Update workflow context information."""
        if thread_id not in self.workflow_contexts:
            self.workflow_contexts[thread_id] = {}
        
        self.workflow_contexts[thread_id][workflow_id] = {
            "workflow_id": workflow_id,
            "status": workflow_status,
            "last_updated": datetime.now().isoformat()
        }
        
        # Update conversation context
        if thread_id in self.conversation_contexts:
            context = self.conversation_contexts[thread_id]
            context["active_workflows"] = list(self.workflow_contexts[thread_id].keys())
    
    def get_comprehensive_context(self, thread_id: str) -> Dict[str, Any]:
        """Get comprehensive context for a conversation thread."""
        return {
            "conversation_context": self.conversation_contexts.get(thread_id, {}),
            "tool_usage_history": self.tool_usage_history.get(thread_id, [])[-10:],  # Last 10 tools
            "widget_contexts": self.widget_contexts.get(thread_id, {}),
            "workflow_contexts": self.workflow_contexts.get(thread_id, {}),
            "user_patterns": self.user_interaction_patterns.get(thread_id, {}),
            "context_insights": self._generate_context_insights(thread_id)
        }
    
    def analyze_user_patterns(self, thread_id: str) -> Dict[str, Any]:
        """Analyze user interaction patterns for personalization."""
        if thread_id not in self.user_interaction_patterns:
            self.user_interaction_patterns[thread_id] = {
                "preferred_tools": {},
                "widget_usage": {},
                "complexity_preference": "standard",
                "response_style": "detailed",
                "domain_interests": [],
                "interaction_frequency": "regular"
            }
        
        patterns = self.user_interaction_patterns[thread_id]
        
        # Analyze tool preferences
        tool_history = self.tool_usage_history.get(thread_id, [])
        tool_counts = {}
        for record in tool_history:
            tool_name = record["tool_name"]
            tool_counts[tool_name] = tool_counts.get(tool_name, 0) + 1
        
        patterns["preferred_tools"] = dict(sorted(tool_counts.items(), key=lambda x: x[1], reverse=True)[:5])
        
        # Analyze widget usage
        widget_contexts = self.widget_contexts.get(thread_id, {})
        widget_types = {}
        for widget_data in widget_contexts.values():
            widget_type = widget_data["widget_type"]
            widget_types[widget_type] = widget_types.get(widget_type, 0) + 1
        
        patterns["widget_usage"] = widget_types
        
        # Infer domain interests
        conversation_context = self.conversation_contexts.get(thread_id, {})
        topics = conversation_context.get("topics", [])
        
        domain_mapping = {
            "crypto": ["token", "cryptocurrency", "defi", "blockchain", "trading"],
            "research": ["research", "analysis", "market", "competitive", "trends"],
            "development": ["widget", "code", "api", "development", "programming"],
            "support": ["help", "guidance", "tutorial", "troubleshooting"]
        }
        
        domain_scores = {}
        for domain, keywords in domain_mapping.items():
            score = sum(1 for topic in topics if any(keyword in topic.lower() for keyword in keywords))
            if score > 0:
                domain_scores[domain] = score
        
        patterns["domain_interests"] = list(dict(sorted(domain_scores.items(), key=lambda x: x[1], reverse=True)).keys())
        
        return patterns
    
    def get_context_recommendations(self, thread_id: str) -> List[str]:
        """Get context-aware recommendations for the user."""
        recommendations = []
        
        context = self.conversation_contexts.get(thread_id, {})
        patterns = self.analyze_user_patterns(thread_id)
        
        # Tool recommendations
        if "delegate_to_crypto_analyst" in patterns.get("preferred_tools", {}):
            recommendations.append("Consider using crypto analysis tools for detailed token evaluation")
        
        if "show_pumpfun_widget" in patterns.get("preferred_tools", {}):
            recommendations.append("Explore trending tokens with the PumpFun widget")
        
        # Widget recommendations
        if "token_chart" not in patterns.get("widget_usage", {}):
            recommendations.append("Try the token chart widget for visual price analysis")
        
        # Domain recommendations
        domain_interests = patterns.get("domain_interests", [])
        if "crypto" in domain_interests and "research" not in domain_interests:
            recommendations.append("Enhance your crypto analysis with market research tools")
        
        # Workflow recommendations
        if context.get("complexity_level") == "complex" and not context.get("active_workflows"):
            recommendations.append("Consider creating a structured workflow for complex tasks")
        
        return recommendations[:3]  # Return top 3 recommendations
    
    def _extract_topics(self, message: str) -> List[str]:
        """Extract topics from a message."""
        topics = []
        message_lower = message.lower()
        
        # Crypto-related topics
        crypto_keywords = ["bitcoin", "ethereum", "token", "crypto", "defi", "nft", "blockchain"]
        for keyword in crypto_keywords:
            if keyword in message_lower:
                topics.append(f"crypto_{keyword}")
        
        # Research topics
        research_keywords = ["research", "analysis", "market", "trends", "competitive"]
        for keyword in research_keywords:
            if keyword in message_lower:
                topics.append(f"research_{keyword}")
        
        # Development topics
        dev_keywords = ["widget", "code", "api", "development", "programming"]
        for keyword in dev_keywords:
            if keyword in message_lower:
                topics.append(f"development_{keyword}")
        
        return topics
    
    def _analyze_sentiment(self, message: str) -> str:
        """Analyze sentiment of a message."""
        positive_words = ["good", "great", "excellent", "amazing", "perfect", "love", "like"]
        negative_words = ["bad", "terrible", "awful", "hate", "dislike", "problem", "issue"]
        
        message_lower = message.lower()
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _assess_complexity(self, message: str, context: Dict[str, Any]) -> str:
        """Assess complexity level of the conversation."""
        complexity_indicators = [
            "comprehensive", "detailed", "complex", "advanced", "multiple", "workflow"
        ]
        
        message_lower = message.lower()
        complexity_score = sum(1 for indicator in complexity_indicators if indicator in message_lower)
        
        # Consider conversation history
        if context.get("message_count", 0) > 10:
            complexity_score += 1
        
        if len(context.get("active_workflows", [])) > 0:
            complexity_score += 2
        
        if complexity_score >= 3:
            return "complex"
        elif complexity_score >= 1:
            return "standard"
        else:
            return "simple"
    
    def _generate_context_summary(self, context: Dict[str, Any]) -> str:
        """Generate a summary of the conversation context."""
        summary_parts = []
        
        if context.get("topics"):
            top_topics = context["topics"][:3]
            summary_parts.append(f"Topics: {', '.join(top_topics)}")
        
        if context.get("recent_tools"):
            summary_parts.append(f"Recent tools: {', '.join(context['recent_tools'][-3:])}")
        
        if context.get("active_widgets"):
            summary_parts.append(f"Active widgets: {len(context['active_widgets'])}")
        
        if context.get("active_workflows"):
            summary_parts.append(f"Active workflows: {len(context['active_workflows'])}")
        
        return "; ".join(summary_parts) if summary_parts else "New conversation"
    
    def _generate_context_insights(self, thread_id: str) -> Dict[str, Any]:
        """Generate insights from the conversation context."""
        context = self.conversation_contexts.get(thread_id, {})
        patterns = self.user_interaction_patterns.get(thread_id, {})
        
        return {
            "conversation_maturity": "experienced" if context.get("message_count", 0) > 20 else "new",
            "user_engagement": "high" if len(patterns.get("preferred_tools", {})) > 3 else "moderate",
            "domain_focus": patterns.get("domain_interests", ["general"])[0] if patterns.get("domain_interests") else "general",
            "complexity_trend": context.get("complexity_level", "standard"),
            "tool_proficiency": "advanced" if len(patterns.get("preferred_tools", {})) > 5 else "beginner"
        }
    
    def cleanup_old_contexts(self):
        """Clean up old context data."""
        cutoff_date = datetime.now() - timedelta(days=self.context_retention_days)
        cutoff_iso = cutoff_date.isoformat()
        
        # Clean conversation contexts
        threads_to_remove = []
        for thread_id, context in self.conversation_contexts.items():
            if context.get("created_at", "") < cutoff_iso:
                threads_to_remove.append(thread_id)
        
        for thread_id in threads_to_remove:
            del self.conversation_contexts[thread_id]
            if thread_id in self.tool_usage_history:
                del self.tool_usage_history[thread_id]
            if thread_id in self.widget_contexts:
                del self.widget_contexts[thread_id]
            if thread_id in self.workflow_contexts:
                del self.workflow_contexts[thread_id]
            if thread_id in self.user_interaction_patterns:
                del self.user_interaction_patterns[thread_id]
