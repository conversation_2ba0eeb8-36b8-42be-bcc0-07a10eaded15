# 🧠 Chad GPT Intelligent Memory Setup (Mem0)

This guide will help you set up Mem0 intelligent memory for ChatGPT-level contextual awareness in Chad GPT.

## 🚀 Quick Setup (5 minutes)

### 1. Install Dependencies

```bash
cd deep-agent
pip install -r requirements.txt
```

### 2. Start Qdrant Vector Database

**Option A: Docker (Recommended)**
```bash
docker run -p 6333:6333 qdrant/qdrant
```

**Option B: Local Installation**
```bash
# Install Qdrant locally
pip install qdrant-client
# Follow Qdrant installation guide
```

### 3. Configure Environment

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` and add your OpenAI API key:
```env
OPENAI_API_KEY=your_actual_openai_api_key_here
```

### 4. Start the Backend

```bash
python main.py
```

## ✅ Verification

1. **Check Memory Manager Status**: Look for this log message:
   ```
   ✅ Mem0 Memory Manager initialized successfully
   ```

2. **Test Memory**: Ask the agent something, then ask a follow-up question. The agent should remember the context!

## 🎯 What You Get

### **Before (Manual Context Hints)**
```
User: "list the top 10 market cap coins from pumpfun [AVAILABLE_DATA: ...]"
Agent: Gives fake templated response
```

### **After (Intelligent Memory)**
```
User: "list the top 10 market cap coins from pumpfun"
Agent: "Based on your current PumpFun widget data, here are the top 10 tokens:
1. 2PAK ($31,849) - 2PAKISTAN
2. TECTONIC ($14,497) - TECTONIC
3. Wako ($8,408) - Wako
..."
```

## 🔧 Advanced Configuration

### Custom Mem0 Configuration

Edit `deep-agent/core/memory_manager.py` to customize:

```python
config = {
    "llm": {
        "provider": "openai",
        "config": {
            "model": "gpt-4o-mini",  # Change model
            "temperature": 0.1,      # Adjust creativity
        }
    },
    "vector_store": {
        "provider": "qdrant",
        "config": {
            "collection_name": "chad_gpt_memory",
            "host": "localhost",
            "port": 6333,
        }
    }
}
```

### Memory Management

The system automatically:
- **Stores conversations** after each turn
- **Stores widget interactions** when widgets are used
- **Retrieves relevant context** based on user questions
- **Provides thread summaries** for long conversations

## 🐛 Troubleshooting

### Memory Manager Not Starting
```
❌ Failed to initialize Mem0: [error]
💡 Falling back to basic memory management
```

**Solutions:**
1. Check Qdrant is running: `curl http://localhost:6333/health`
2. Verify OpenAI API key is set
3. Check network connectivity

### No Context Retrieved
```
⚠️ Fallback to widget context analysis
```

**Solutions:**
1. Ensure conversations are being stored (check logs)
2. Try asking more specific questions
3. Check thread_id consistency

### Performance Issues

**Optimize Memory Retrieval:**
- Reduce `limit` in `get_relevant_context()` calls
- Adjust Mem0 model to faster/cheaper options
- Use local embeddings instead of OpenAI

## 📊 Memory Analytics

Monitor memory usage in logs:
```
💾 Added conversation memory for thread thread_123
🎮 Added pumpfun widget memory for thread thread_123
🔍 Retrieved 3 relevant memories for thread thread_123
```

## 🎉 Success!

Your Chad GPT now has:
- ✅ **Persistent memory** across sessions
- ✅ **Contextual awareness** like ChatGPT
- ✅ **Widget context integration**
- ✅ **Intelligent retrieval** based on user questions
- ✅ **No more context pollution** in user messages

The agent will now remember:
- Previous conversations
- Widget interactions
- User preferences
- Token discussions
- Trading context

Just like ChatGPT! 🚀
