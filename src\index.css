@tailwind base;
@tailwind components;
@tailwind utilities;

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Improved transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.2, 0, 0, 1);
  transition-duration: 200ms;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Improved spacing */
.content-spacing {
  padding: 0 32px;
  max-width: 1280px;
  margin: 0 auto;
}

/* Improved container */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 24px;
}

@media (min-width: 768px) {
  .container {
    padding: 0 32px;
  }
}

/* Improved card hover states */
.hover-card {
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
}

.hover-card:hover {
  transform: scale(1.02) translateZ(0);
  box-shadow: 0 12px 36px rgb(0 0 0 / 0.25);
}

/* Improved button states */
.button-effect {
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
}

.button-effect:hover {
  transform: scale(1.04) translateZ(0);
  box-shadow: 0 8px 24px rgb(34 197 94 / 0.25);
}

.button-effect:active {
  transform: scale(0.96) translateZ(0);
  box-shadow: 0 4px 12px rgb(34 197 94 / 0.15);
}

/* Improved focus states */
:focus-visible {
  outline: 2px solid #22c55e;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Improved scrollbar */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background: transparent;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
  margin: 4px;
}

::-webkit-scrollbar-thumb {
  background: #222;
  border-radius: 2px;
  border: 1px solid #181818;
  min-height: 40px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #444;
}

/* Improved text selection */
::selection {
  background: rgba(34, 197, 94, 0.15);
  color: #fff;
  text-shadow: none;
}

/* Improved placeholder */
::placeholder {
  color: #666;
  opacity: 1;
  font-weight: 500;
  transition: color 0.2s ease;
}

input:focus::placeholder,
textarea:focus::placeholder {
  color: #888;
}

/* Improved text rendering */
.react-flow__node {
  @apply bg-[#111] text-white border-[#222] rounded-xl shadow-lg shadow-black/20;
}

.react-flow__node-input {
  @apply bg-gradient-to-br from-indigo-500 to-purple-500;
}

.react-flow__handle {
  @apply bg-[#22c55e] border-black w-3 h-3;
}

.react-flow__handle:hover {
  @apply bg-emerald-400;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #000000;
  overflow: hidden;
  text-rendering: optimizeLegibility;
  -webkit-tap-highlight-color: transparent;
  font-feature-settings: "kern" 1, "liga" 1;
  letter-spacing: -0.01em;
  line-height: 1.5;
}

/* Improved hover states */
.hover-scale {
  transition: transform 0.2s ease;
  will-change: transform;
}

.hover-scale:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 32px rgb(0 0 0 / 0.2);
}

/* Improved active states */
.active-scale {
  transition: transform 0.1s ease;
  will-change: transform;
}

.active-scale:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.1);
}

/* Improved disabled states */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  user-select: none;
}

/* Improved textarea */
textarea {
  font-family: inherit;
  caret-color: #22c55e;
  outline: none;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.1);
  line-height: 1.6;
  resize: none;
  transition: all 0.2s ease;
  background: #111;
  border: 1px solid transparent;
  border-radius: 1rem;
  padding: 1rem;
  min-height: 100px;
}

textarea:focus {
  border-color: rgba(34, 197, 94, 0.4);
  box-shadow: 0 0 0 1px #22c55e;
  background: #181818;
}

/* Improved animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-12px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fade-in 0.4s cubic-bezier(0.2, 0, 0, 1) forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s cubic-bezier(0.2, 0, 0, 1) forwards;
}

.animate-slide-in {
  animation: slide-in 0.3s cubic-bezier(0.2, 0, 0, 1) forwards;
}