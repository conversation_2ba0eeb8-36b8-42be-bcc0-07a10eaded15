import { Message } from "../types";

// Deep Agent API configuration (replaces LangGraph Agent)
const DEEP_AGENT_API_URL = `${(import.meta as any).env?.VITE_DEEP_AGENT_URL || 'http://localhost:8001'}/api/chat`;

// Fallback OpenRouter configuration
const OPENROUTER_API_KEY = import.meta.env.VITE_OPENROUTER_API_KEY;

// Contract address detection
interface ContractAddressMatch {
  address: string;
  chain: string;
}

function detectContractAddress(text: string): ContractAddressMatch | null {
  // Solana address pattern (32-44 characters, base58)
  const solanaPattern = /\b[1-9A-HJ-NP-Za-km-z]{32,44}\b/g;

  // Ethereum/EVM address pattern (0x followed by 40 hex characters)
  const evmPattern = /\b0x[a-fA-F0-9]{40}\b/g;

  // XRPL address pattern (starts with 'r' followed by 25-34 characters)
  const xrplPattern = /\br[a-zA-Z0-9]{25,34}\b/g;

  // Check for XRPL addresses first (most specific)
  const xrplMatches = text.match(xrplPattern);
  if (xrplMatches && xrplMatches.length > 0) {
    return {
      address: xrplMatches[0],
      chain: 'xrpl'
    };
  }

  // Check for Ethereum/EVM addresses
  const evmMatches = text.match(evmPattern);
  if (evmMatches && evmMatches.length > 0) {
    // Try to detect specific EVM chains based on context
    const lowerText = text.toLowerCase();
    let detectedChain = 'ethereum'; // default

    if (lowerText.includes('bsc') || lowerText.includes('binance')) {
      detectedChain = 'bsc';
    } else if (lowerText.includes('base')) {
      detectedChain = 'base';
    } else if (lowerText.includes('arbitrum')) {
      detectedChain = 'arbitrum';
    } else if (lowerText.includes('polygon') || lowerText.includes('matic')) {
      detectedChain = 'polygon';
    } else if (lowerText.includes('avalanche') || lowerText.includes('avax')) {
      detectedChain = 'avalanche';
    } else if (lowerText.includes('optimism')) {
      detectedChain = 'optimism';
    }

    return {
      address: evmMatches[0],
      chain: detectedChain
    };
  }

  // Check for Solana addresses
  const solanaMatches = text.match(solanaPattern);
  if (solanaMatches && solanaMatches.length > 0) {
    // Filter out common false positives (too short or common words)
    const validSolanaAddresses = solanaMatches.filter(addr => {
      // Must be at least 32 characters
      if (addr.length < 32) return false;

      // Exclude common words that might match the pattern
      const commonWords = ['pump', 'token', 'coin', 'address', 'wallet'];
      if (commonWords.some(word => addr.toLowerCase().includes(word))) return false;

      return true;
    });

    if (validSolanaAddresses.length > 0) {
      return {
        address: validSolanaAddresses[0],
        chain: 'solana'
      };
    }
  }

  return null;
}

interface MessageContent {
  type: 'text' | 'image_url';
  text?: string;
  image_url?: {
    url: string;
  };
}

interface ChatResponse {
  content: string | null;
  thinking?: string | null;  // For models that output thinking process
  function_call?: {
    name: string;
    arguments?: Record<string, any>;
  };
  model_info?: {
    model: string;
    supports_thinking: boolean;
  };
}

interface DeepAgentMessage {
  role: string;
  content: string;
  image?: string;
}

interface DeepAgentRequest {
  messages: DeepAgentMessage[];
  threadId?: string;
  stream?: boolean;
  model?: string;
  user_context?: Record<string, any>;
}

function convertToDeepAgentFormat(messages: Message[]): DeepAgentMessage[] {
  return messages.map(msg => {
    if (msg.type === 'assistant') {
      return {
        role: 'assistant',
        content: msg.content
      };
    }

    if (msg.type === 'user') {
      return {
        role: 'user',
        content: msg.content,
        image: msg.image || undefined
      };
    }

    return null;
  }).filter(Boolean) as DeepAgentMessage[];
}

function convertToOpenRouterFormat(messages: Message[]): any[] {
  return messages.map(msg => {
    if (msg.type === 'assistant') {
      return {
        role: 'assistant',
        content: msg.content
      };
    }

    // Handle user messages with potential images
    if (msg.type === 'user') {
      const content: MessageContent[] = [];

      // Add text content if present
      if (msg.content) {
        content.push({
          type: 'text',
          text: msg.content
        });
      }

      // Add image if present
      if (msg.image) {
        content.push({
          type: 'image_url',
          image_url: {
            url: msg.image
          }
        });
      }

      return {
        role: 'user',
        content: content.length === 1 && !msg.image ? content[0].text : content
      };
    }

    return null;
  }).filter(Boolean);
}

async function callDeepAgent(messages: Message[], threadId?: string): Promise<ChatResponse> {
  try {
    const deepAgentMessages = convertToDeepAgentFormat(messages);

    if (!deepAgentMessages.length) {
      throw new Error('No valid messages to process');
    }

    const request: DeepAgentRequest = {
      messages: deepAgentMessages,
      threadId: threadId || `thread_${Date.now()}`,
      stream: false,
      model: "z-ai/glm-4.5",
      user_context: {
        platform: "chad_gpt",
        frontend_version: "2.0.0",
        deep_agent_integration: true,
        timestamp: Date.now() // Add timestamp to prevent caching
      }
    };

    console.log('📤 Calling Deep Agent:', request);

    const response = await fetch(DEEP_AGENT_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
      },
      body: JSON.stringify(request)
    });

    console.log('📥 Deep Agent response status:', response.status);

    if (!response.ok) {
      throw new Error(`Deep Agent API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Deep Agent response:', data);

    return {
      content: data.content || "I apologize, but I couldn't generate a response.",
      thinking: data.thinking || undefined,
      function_call: data.function_call || undefined,
      model_info: data.model_info || undefined
    };

  } catch (error) {
    console.error('Deep Agent error:', error);
    throw error;
  }
}

async function callOpenRouterFallback(messages: Message[]): Promise<ChatResponse> {
  try {
    const validMessages = convertToOpenRouterFormat(messages);

    // If no valid messages, return early
    if (!validMessages.length) {
      console.error('No valid messages to process');
      return {
        content: "I apologize, but I couldn't process your message. Please try again.",
        function_call: undefined
      };
    }

    if (!OPENROUTER_API_KEY) {
      throw new Error('OpenRouter API key not available for fallback');
    }

    // Add system message
    const systemMessage = {
      role: "system",
      content: "You are Chad GPT, an AI assistant focused on crypto and web3. Keep responses under 100 words. For token/chart requests, immediately respond with /pumpfun or /dexscreener commands without additional explanation."
    };

    const payload = {
      model: "moonshotai/kimi-k2",
      messages: [systemMessage, ...validMessages],
      temperature: 0.7,
      max_tokens: 150,
      top_p: 0.9,
      presence_penalty: 0,
      frequency_penalty: 0
    };

    console.log('Sending fallback request to OpenRouter:', payload);

    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${OPENROUTER_API_KEY}`,
        "HTTP-Referer": window.location.origin,
        "X-Title": "Chad GPT",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      console.error('OpenRouter API error:', {
        status: response.status,
        statusText: response.statusText,
        errorData
      });
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response:', data);

    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      console.error('Empty response from AI');
      return {
        content: "I apologize, but I couldn't generate a response at this time. Please try again.",
        function_call: undefined 
      };
    }

    // Check for contract addresses first (highest priority)
    const contractAddressMatch = detectContractAddress(content);
    if (contractAddressMatch) {
      return {
        content: `Opening chart for token ${contractAddressMatch.address} on ${contractAddressMatch.chain.toUpperCase()}.`,
        function_call: {
          name: "showTokenChartWidget",
          arguments: {
            tokenAddress: contractAddressMatch.address,
            chain: contractAddressMatch.chain
          }
        }
      };
    }

    // Check for widget commands in the response
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('dexscreener') || lowerContent.includes('/dexscreener')) {
      return {
        content: "Opening DexScreener to help you analyze token data.",
        function_call: {
          name: "showDexScreenerWidget"
        }
      };
    }

    if (lowerContent.includes('pumpfun') || lowerContent.includes('/pumpfun')) {
      return {
        content: "Opening PumpFun to help you explore tokens.",
        function_call: {
          name: "showPumpFunWidget"
        }
      };
    }

    if (lowerContent.includes('jupiter') || lowerContent.includes('swap')) {
      return {
        content: "Opening Jupiter to help you swap tokens.",
        function_call: {
          name: "showJupiterWidget",
        }
      };
    }

    if (lowerContent.includes('phantom') || lowerContent.includes('wallet')) {
      return {
        content: "Opening Phantom wallet to help you manage your assets.",
        function_call: {
          name: "showPhantomWidget",
        }
      };
    }
    return {
      content: content,
      function_call: undefined
    };
  } catch (error) {
    console.error('OpenRouter fallback error:', error);
    throw error;
  }
}

export async function getChatCompletion(messages: Message[], threadId?: string): Promise<ChatResponse> {
  try {
    // First, try the Deep Agent system
    console.log('🚀 Attempting to use Deep Agent at:', DEEP_AGENT_API_URL);
    const result = await callDeepAgent(messages, threadId);
    console.log('✅ Deep Agent succeeded:', result);
    return result;

  } catch (deepAgentError) {
    console.warn('❌ Deep Agent failed, falling back to OpenRouter:', deepAgentError);

    try {
      // Fallback to direct OpenRouter call
      return await callOpenRouterFallback(messages);

    } catch (fallbackError) {
      console.error('Both Deep Agent and OpenRouter fallback failed:', fallbackError);

      const errorMessage = fallbackError instanceof Error
        ? fallbackError.message
        : 'An unexpected error occurred';

      // Check for specific error types
      if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
        return {
          content: "I'm currently experiencing high traffic. Please try again in a few seconds.",
          function_call: undefined
        };
      }

      if (errorMessage.includes('401')) {
        return {
          content: "There seems to be an issue with the API authentication. Please try again later.",
          function_call: undefined
        };
      }

      return {
        content: "I apologize, but I encountered an error while processing your request. Please try again in a moment.",
        function_call: undefined
      };
    }
  }
}