"""
Conversation state management for Chad GPT Deep Agents.
Handles context preservation, user preferences, and conversation history.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import uuid


@dataclass
class ConversationContext:
    """Represents the context of a conversation thread."""
    thread_id: str
    user_id: Optional[str]
    created_at: datetime
    last_updated: datetime
    message_count: int
    active_widgets: List[str]
    workflow_ids: List[str]
    user_preferences: Dict[str, Any]
    metadata: Dict[str, Any]


@dataclass
class Message:
    """Represents a single message in a conversation."""
    id: str
    thread_id: str
    role: str  # 'user', 'assistant', 'system'
    content: str
    timestamp: datetime
    metadata: Dict[str, Any]


class ConversationState:
    """
    Manages conversation state, context, and user preferences for the platform.
    Provides persistent storage and retrieval of conversation data.
    """

    def __init__(self, storage_path: str = "agent_workspace/conversations"):
        """Initialize conversation state manager."""
        self.storage_path = storage_path
        self.active_contexts: Dict[str, ConversationContext] = {}
        self.message_history: Dict[str, List[Message]] = {}
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        
        # Ensure storage directory exists
        import os
        os.makedirs(storage_path, exist_ok=True)
        
        # Load existing data
        asyncio.create_task(self._load_existing_data())
    
    async def _load_existing_data(self):
        """Load existing conversation data from storage."""
        try:
            # Load conversation contexts
            contexts_file = f"{self.storage_path}/contexts.json"
            if os.path.exists(contexts_file):
                with open(contexts_file, 'r') as f:
                    contexts_data = json.load(f)
                    for thread_id, context_data in contexts_data.items():
                        context_data['created_at'] = datetime.fromisoformat(context_data['created_at'])
                        context_data['last_updated'] = datetime.fromisoformat(context_data['last_updated'])
                        self.active_contexts[thread_id] = ConversationContext(**context_data)
            
            # Load user preferences
            prefs_file = f"{self.storage_path}/user_preferences.json"
            if os.path.exists(prefs_file):
                with open(prefs_file, 'r') as f:
                    self.user_preferences = json.load(f)
                    
        except Exception as e:
            print(f"Error loading conversation data: {e}")
    
    async def update_context(
        self,
        thread_id: str,
        user_message: str,
        user_context: Dict[str, Any]
    ):
        """Update conversation context with new message and user data."""
        now = datetime.now()
        
        # Get or create conversation context
        if thread_id not in self.active_contexts:
            self.active_contexts[thread_id] = ConversationContext(
                thread_id=thread_id,
                user_id=user_context.get("user_id"),
                created_at=now,
                last_updated=now,
                message_count=0,
                active_widgets=[],
                workflow_ids=[],
                user_preferences=user_context.get("preferences", {}),
                metadata={}
            )
        
        context = self.active_contexts[thread_id]
        
        # Update context
        context.last_updated = now
        context.message_count += 1
        context.metadata.update(user_context.get("metadata", {}))
        
        # Add message to history
        if thread_id not in self.message_history:
            self.message_history[thread_id] = []
        
        message = Message(
            id=str(uuid.uuid4()),
            thread_id=thread_id,
            role="user",
            content=user_message,
            timestamp=now,
            metadata=user_context
        )
        
        self.message_history[thread_id].append(message)
        
        # Trim history if too long (keep last 100 messages)
        if len(self.message_history[thread_id]) > 100:
            self.message_history[thread_id] = self.message_history[thread_id][-100:]
    
    async def add_assistant_message(
        self,
        thread_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Add an assistant message to the conversation history."""
        if thread_id not in self.message_history:
            self.message_history[thread_id] = []
        
        message = Message(
            id=str(uuid.uuid4()),
            thread_id=thread_id,
            role="assistant",
            content=content,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        
        self.message_history[thread_id].append(message)
        
        # Update context
        if thread_id in self.active_contexts:
            self.active_contexts[thread_id].last_updated = datetime.now()
    
    async def get_conversation_history(
        self,
        thread_id: str,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get conversation history for a thread."""
        if thread_id not in self.message_history:
            return []
        
        messages = self.message_history[thread_id]
        if limit:
            messages = messages[-limit:]
        
        return [
            {
                "id": msg.id,
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat(),
                "metadata": msg.metadata
            }
            for msg in messages
        ]
    
    async def get_state(self, thread_id: str) -> Dict[str, Any]:
        """Get complete conversation state for a thread."""
        context = self.active_contexts.get(thread_id)
        history = await self.get_conversation_history(thread_id)
        
        return {
            "thread_id": thread_id,
            "context": asdict(context) if context else None,
            "message_history": history,
            "message_count": len(history)
        }
    
    async def update_user_preferences(
        self,
        user_id: str,
        preferences: Dict[str, Any]
    ):
        """Update user preferences."""
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = {}
        
        self.user_preferences[user_id].update(preferences)
        
        # Update all active contexts for this user
        for context in self.active_contexts.values():
            if context.user_id == user_id:
                context.user_preferences.update(preferences)
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences."""
        return self.user_preferences.get(user_id, {})
    
    async def add_active_widget(self, thread_id: str, widget_id: str):
        """Add an active widget to a conversation thread."""
        if thread_id in self.active_contexts:
            context = self.active_contexts[thread_id]
            if widget_id not in context.active_widgets:
                context.active_widgets.append(widget_id)
                context.last_updated = datetime.now()
    
    async def remove_active_widget(self, thread_id: str, widget_id: str):
        """Remove an active widget from a conversation thread."""
        if thread_id in self.active_contexts:
            context = self.active_contexts[thread_id]
            if widget_id in context.active_widgets:
                context.active_widgets.remove(widget_id)
                context.last_updated = datetime.now()
    
    async def add_workflow(self, thread_id: str, workflow_id: str):
        """Add a workflow to a conversation thread."""
        if thread_id in self.active_contexts:
            context = self.active_contexts[thread_id]
            if workflow_id not in context.workflow_ids:
                context.workflow_ids.append(workflow_id)
                context.last_updated = datetime.now()
    
    async def get_active_threads(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of active conversation threads."""
        threads = []
        
        for thread_id, context in self.active_contexts.items():
            if user_id is None or context.user_id == user_id:
                # Get recent messages for preview
                recent_messages = await self.get_conversation_history(thread_id, limit=3)
                
                threads.append({
                    "thread_id": thread_id,
                    "user_id": context.user_id,
                    "created_at": context.created_at.isoformat(),
                    "last_updated": context.last_updated.isoformat(),
                    "message_count": context.message_count,
                    "active_widgets": context.active_widgets,
                    "workflow_ids": context.workflow_ids,
                    "recent_messages": recent_messages
                })
        
        # Sort by last updated (most recent first)
        threads.sort(key=lambda x: x["last_updated"], reverse=True)
        
        return threads
    
    async def cleanup_old_threads(self, days_old: int = 30):
        """Clean up old conversation threads."""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        threads_to_remove = []
        for thread_id, context in self.active_contexts.items():
            if context.last_updated < cutoff_date:
                threads_to_remove.append(thread_id)
        
        for thread_id in threads_to_remove:
            del self.active_contexts[thread_id]
            if thread_id in self.message_history:
                del self.message_history[thread_id]
    
    async def save_all_states(self):
        """Save all conversation states to persistent storage."""
        try:
            # Save conversation contexts
            contexts_data = {}
            for thread_id, context in self.active_contexts.items():
                context_dict = asdict(context)
                context_dict['created_at'] = context.created_at.isoformat()
                context_dict['last_updated'] = context.last_updated.isoformat()
                contexts_data[thread_id] = context_dict
            
            contexts_file = f"{self.storage_path}/contexts.json"
            with open(contexts_file, 'w') as f:
                json.dump(contexts_data, f, indent=2)
            
            # Save user preferences
            prefs_file = f"{self.storage_path}/user_preferences.json"
            with open(prefs_file, 'w') as f:
                json.dump(self.user_preferences, f, indent=2)
            
            # Save message history (last 50 messages per thread)
            for thread_id, messages in self.message_history.items():
                thread_file = f"{self.storage_path}/thread_{thread_id}.json"
                messages_data = []
                
                for msg in messages[-50:]:  # Keep last 50 messages
                    msg_dict = asdict(msg)
                    msg_dict['timestamp'] = msg.timestamp.isoformat()
                    messages_data.append(msg_dict)
                
                with open(thread_file, 'w') as f:
                    json.dump(messages_data, f, indent=2)
                    
        except Exception as e:
            print(f"Error saving conversation states: {e}")
    
    async def export_conversation(self, thread_id: str) -> Dict[str, Any]:
        """Export a complete conversation for backup or analysis."""
        state = await self.get_state(thread_id)
        
        return {
            "export_timestamp": datetime.now().isoformat(),
            "thread_id": thread_id,
            "conversation_state": state
        }
    
    async def import_conversation(self, conversation_data: Dict[str, Any]):
        """Import a conversation from exported data."""
        thread_id = conversation_data["thread_id"]
        state = conversation_data["conversation_state"]
        
        # Restore context
        if state["context"]:
            context_data = state["context"]
            context_data['created_at'] = datetime.fromisoformat(context_data['created_at'])
            context_data['last_updated'] = datetime.fromisoformat(context_data['last_updated'])
            self.active_contexts[thread_id] = ConversationContext(**context_data)
        
        # Restore message history
        self.message_history[thread_id] = []
        for msg_data in state["message_history"]:
            msg_data['timestamp'] = datetime.fromisoformat(msg_data['timestamp'])
            message = Message(
                id=msg_data['id'],
                thread_id=msg_data['thread_id'],
                role=msg_data['role'],
                content=msg_data['content'],
                timestamp=msg_data['timestamp'],
                metadata=msg_data['metadata']
            )
            self.message_history[thread_id].append(message)
