"""
Comprehensive Moralis MCP integration tools for the Chad GPT Deep Agent.
These tools provide complete blockchain data access through all Moralis APIs.
Includes EVM and Solana endpoints for NFTs, tokens, DeFi, analytics, and more.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional, Union
import requests
import json
import os
from datetime import datetime


# Moralis API configuration
MORALIS_API_KEY = os.getenv("MORALIS_API_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImI1YmVhMTc1LWU5MmEtNGU0ZS1hOTgxLWU3ZjJlNTk5NjYyMCIsIm9yZ0lkIjoiNDA1NzgzIiwidXNlcklkIjoiNDE2OTY2IiwidHlwZUlkIjoiZTYyZmNjOGMtMTZlMi00ZmY1LWJjMjItNzVlYTBhZTRjNDk2IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MjQ1MTYzOTcsImV4cCI6NDg4MDI3NjM5N30.73TV_J52D11itXTgHvc1wr_kavMbyHAcxCya1TNEoeI")
MORALIS_BASE_URL = "https://deep-index.moralis.io/api/v2.2"
SOLANA_BASE_URL = "https://solana-gateway.moralis.io"

# Common headers for Moralis API requests
def get_headers():
    return {
        "X-API-Key": MORALIS_API_KEY,
        "Content-Type": "application/json"
    }

# Helper function for making API requests with error handling
def make_moralis_request(url: str, params: Dict = None, method: str = "GET") -> Dict[str, Any]:
    """Make a request to Moralis API with proper error handling."""
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=get_headers(), params=params or {})
        else:
            response = requests.post(url, headers=get_headers(), json=params or {})

        if response.status_code == 200:
            return {
                "success": True,
                "data": response.json(),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "message": response.text,
                "status_code": response.status_code
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# =============================================================================
# EVM NFT TOOLS
# =============================================================================


@tool
def evm_getwalletnfts(wallet_address: str, chain: str = "eth", token_addresses: Optional[List[str]] = None, limit: int = 100) -> Dict[str, Any]:
    """
    Fetch all NFTs held by a specified wallet address. Use token_addresses to filter by specific contracts.
    Each NFT returned includes on-chain metadata as well as off-chain metadata, floor prices, rarity and more.
    """
    url = f"{MORALIS_BASE_URL}/{wallet_address}/nft"
    params = {"chain": chain, "format": "decimal", "limit": limit}
    if token_addresses:
        params["token_addresses"] = token_addresses

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getmultiplenfts(tokens: List[Dict[str, str]], chain: str = "eth") -> Dict[str, Any]:
    """
    Get NFT metadata for one or many NFTs. Accepts an array of up to 25 tokens,
    each requiring token_address and token_id.
    """
    url = f"{MORALIS_BASE_URL}/nft/getMultipleNFTs"
    params = {"chain": chain, "tokens": tokens[:25]}  # Limit to 25

    return make_moralis_request(url, params, method="POST")

@tool
def evm_getwalletnftcollections(wallet_address: str, chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """
    Fetch all NFT Collections held by a specified wallet address.
    Each Collection returned includes metadata, floor prices and more.
    """
    url = f"{MORALIS_BASE_URL}/{wallet_address}/nft/collections"
    params = {"chain": chain, "limit": limit}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getuniqueownersbycollection(token_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Get unique wallet addresses owning NFTs from a contract."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/owners"
    params = {"chain": chain, "format": "decimal"}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnftcontracttransfers(token_address: str, chain: str = "eth", from_date: Optional[str] = None, to_date: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
    """Get NFT transfers for a contract, with options to filter by date, token, or other parameters."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/transfers"
    params = {"chain": chain, "format": "decimal", "limit": limit}
    if from_date:
        params["from_date"] = from_date
    if to_date:
        params["to_date"] = to_date

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnftbycontracttraits(token_address: str, chain: str = "eth", traits: Dict[str, Any] = None) -> Dict[str, Any]:
    """Find NFTs in a contract matching specific traits, perfect for attribute-based searches."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/search"
    params = {"chain": chain, "format": "decimal"}
    if traits:
        params.update(traits)

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnfttraitsbycollection(token_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Explore the distinct traits of NFTs in a contract, showcasing their unique attributes."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/traits"
    params = {"chain": chain}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnfttrades(token_address: str, chain: str = "eth", marketplace: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
    """Get NFT trades for a given contract with the ability to filter by marketplace."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/trades"
    params = {"chain": chain, "limit": limit}
    if marketplace:
        params["marketplace"] = marketplace

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getnfttradesbywallet(wallet_address: str, chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Get NFT trades for a specific wallet address."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/nft/trades"
    params = {"chain": chain, "limit": limit}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getnftbulkcontractmetadata(token_addresses: List[str], chain: str = "eth") -> Dict[str, Any]:
    """Retrieve metadata (name, symbol) for up to 25 NFT contracts in one call."""
    url = f"{MORALIS_BASE_URL}/nft/getContractMetadata"
    params = {"chain": chain, "token_addresses": token_addresses[:25]}

    return make_moralis_request(url, params, method="POST")

@tool
def evm_getnftmetadata(token_address: str, token_id: str, chain: str = "eth") -> Dict[str, Any]:
    """Fetch metadata for a specific NFT. Includes on-chain and off-chain metadata, floor prices, rarity and more."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/{token_id}"
    params = {"chain": chain, "format": "decimal"}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["token_id"] = token_id
        result["chain"] = chain
    return result

@tool
def evm_getnftcontractsaleprices(token_address: str, chain: str = "eth", days: int = 30) -> Dict[str, Any]:
    """Fetch sale prices for NFTs in a contract over a specified number of days."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/stats"
    params = {"chain": chain, "days": days}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
        result["days"] = days
    return result

@tool
def evm_getnftsaleprices(token_address: str, token_id: str, chain: str = "eth", days: int = 30) -> Dict[str, Any]:
    """Fetch sale prices for a specific NFT over a specified number of days."""
    url = f"{MORALIS_BASE_URL}/nft/{token_address}/{token_id}/stats"
    params = {"chain": chain, "days": days}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["token_id"] = token_id
        result["chain"] = chain
        result["days"] = days
    return result

# =============================================================================
# EVM TOKEN TOOLS
# =============================================================================

@tool
def evm_getswapsbytokenaddress(token_address: str, chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Get all swap transactions (buy/sell) for a specific ERC20 token."""
    url = f"{MORALIS_BASE_URL}/erc20/{token_address}/swaps"
    params = {"chain": chain, "limit": limit}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_gettokenholders(token_address: str, chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Returns total holders for a given token, as well as aggregated stats holder supply, trends, distribution and acquisition metrics."""
    url = f"{MORALIS_BASE_URL}/erc20/{token_address}/holders"
    params = {"chain": chain, "limit": limit}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_gethistoricaltokenholders(token_address: str, chain: str = "eth", from_date: Optional[str] = None, to_date: Optional[str] = None) -> Dict[str, Any]:
    """Track changes in the holder base of an ERC20 token over time."""
    url = f"{MORALIS_BASE_URL}/erc20/{token_address}/holders/historical"
    params = {"chain": chain}
    if from_date:
        params["from_date"] = from_date
    if to_date:
        params["to_date"] = to_date

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_getmultipletokenprices(token_addresses: List[str], chain: str = "eth", to_block: Optional[str] = None) -> Dict[str, Any]:
    """Retrieve the current or historical prices for multiple ERC20 tokens."""
    url = f"{MORALIS_BASE_URL}/erc20/prices"
    params = {"chain": chain, "token_addresses": token_addresses[:100]}  # Limit to 100
    if to_block:
        params["to_block"] = to_block

    return make_moralis_request(url, params, method="POST")

@tool
def evm_gettokenowners(token_address: str, chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Identify the major holders of an ERC20 token and understand their ownership percentages."""
    url = f"{MORALIS_BASE_URL}/erc20/{token_address}/owners"
    params = {"chain": chain, "limit": limit}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_gettokenmetadata(token_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Retrieve metadata (name, symbol, decimals, logo) for an ERC20 token contract."""
    url = f"{MORALIS_BASE_URL}/erc20/{token_address}/metadata"
    params = {"chain": chain}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

@tool
def evm_gettokentransfers(token_address: str, chain: str = "eth", limit: int = 100, from_date: Optional[str] = None) -> Dict[str, Any]:
    """Get all ERC20 token transfers for a contract, ordered by block number (newest first)."""
    url = f"{MORALIS_BASE_URL}/erc20/{token_address}/transfers"
    params = {"chain": chain, "limit": limit}
    if from_date:
        params["from_date"] = from_date

    result = make_moralis_request(url, params)
    if result["success"]:
        result["token_address"] = token_address
        result["chain"] = chain
    return result

# =============================================================================
# EVM WALLET TOOLS
# =============================================================================

@tool
def evm_getnativebalancesforaddresses(wallet_addresses: List[str], chain: str = "eth") -> Dict[str, Any]:
    """Retrieve native token balances (e.g. ETH) for one or many wallet addresses in single request."""
    url = f"{MORALIS_BASE_URL}/wallets/balances"
    params = {"chain": chain, "wallet_addresses": wallet_addresses[:25]}  # Limit to 25

    return make_moralis_request(url, params, method="POST")

@tool
def evm_getwalletapprovals(wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
    """List active ERC20 token approvals for a wallet, showing which contracts have access."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/approvals"
    params = {"chain": chain}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getwallethistory(wallet_address: str, chain: str = "eth", limit: int = 100) -> Dict[str, Any]:
    """Get the complete decoded transaction history for a given wallet."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/history"
    params = {"chain": chain, "limit": limit}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getwallettokenbalancesprice(wallet_address: str, chain: str = "eth", exclude_spam: bool = True) -> Dict[str, Any]:
    """Fetch ERC20 and native token balances for a given wallet address, including their USD prices."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/erc20"
    params = {"chain": chain, "exclude_spam": exclude_spam}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
        result["chain"] = chain
    return result

@tool
def evm_getwalletnetworth(wallet_address: str, chains: Optional[List[str]] = None, exclude_spam: bool = True) -> Dict[str, Any]:
    """Calculate the total net worth of a wallet in USD, with options to exclude spam tokens for accuracy."""
    url = f"{MORALIS_BASE_URL}/{wallet_address}/net-worth"
    params = {"exclude_spam": exclude_spam}
    if chains:
        params["chains"] = chains

    result = make_moralis_request(url, params)
    if result["success"]:
        result["wallet_address"] = wallet_address
    return result

# =============================================================================
# EVM BLOCKCHAIN TOOLS
# =============================================================================

@tool
def evm_gettransactionverbose(transaction_hash: str, chain: str = "eth") -> Dict[str, Any]:
    """Get the ABI-decoded contents of a transaction by the given transaction hash."""
    url = f"{MORALIS_BASE_URL}/transaction/{transaction_hash}/verbose"
    params = {"chain": chain}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["transaction_hash"] = transaction_hash
        result["chain"] = chain
    return result

@tool
def evm_getblock(block_number_or_hash: str, chain: str = "eth") -> Dict[str, Any]:
    """Get the contents of a block given the block hash."""
    url = f"{MORALIS_BASE_URL}/block/{block_number_or_hash}"
    params = {"chain": chain}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["block"] = block_number_or_hash
        result["chain"] = chain
    return result

@tool
def evm_getlatestblocknumber(chain: str = "eth") -> Dict[str, Any]:
    """Get the most recent block number for a specified blockchain."""
    url = f"{MORALIS_BASE_URL}/dateToBlock"
    params = {"chain": chain, "date": datetime.now().isoformat()}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["chain"] = chain
    return result

@tool
def evm_getdatetoblock(date: str, chain: str = "eth") -> Dict[str, Any]:
    """Find the closest block to a specific date on a blockchain."""
    url = f"{MORALIS_BASE_URL}/dateToBlock"
    params = {"chain": chain, "date": date}

    result = make_moralis_request(url, params)
    if result["success"]:
        result["date"] = date
        result["chain"] = chain
    return result

@tool
def evm_resolveaddress(address: str) -> Dict[str, Any]:
    """Convert an Ethereum address to its associated ENS domain, if registered."""
    url = f"{MORALIS_BASE_URL}/resolve/{address}/reverse"

    result = make_moralis_request(url)
    if result["success"]:
        result["address"] = address
    return result

@tool
def evm_resolveensdomain(domain: str) -> Dict[str, Any]:
    """Resolve an ENS domain to its associated Ethereum address."""
    url = f"{MORALIS_BASE_URL}/resolve/{domain}"

    result = make_moralis_request(url)
    if result["success"]:
        result["domain"] = domain
    return result

@tool
def get_wallet_token_balances(wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
    """Legacy function - use evm_getwallettokenbalancesprice instead."""
    return evm_getwallettokenbalancesprice(wallet_address, chain)


@tool
def get_wallet_nft_collection(wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
    """
    Get NFT collections owned by a wallet address.
    
    Args:
        wallet_address: The wallet address to query
        chain: Blockchain to query (eth, polygon, bsc, etc.)
        
    Returns:
        Dictionary containing NFT collections data
    """
    try:
        url = f"{MORALIS_BASE_URL}/{wallet_address}/nft"
        params = {"chain": chain, "format": "decimal"}
        
        response = requests.get(url, headers=get_headers(), params=params)
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "wallet_address": wallet_address,
                "chain": chain,
                "total_nfts": data.get("total", 0),
                "page": data.get("page", 1),
                "page_size": data.get("page_size", 100),
                "nfts": data.get("result", []),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "message": response.text
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "wallet_address": wallet_address,
            "chain": chain
        }


@tool
def get_token_price(token_address: str, chain: str = "eth") -> Dict[str, Any]:
    """
    Get current price data for a specific token.
    
    Args:
        token_address: The token contract address
        chain: Blockchain where the token exists
        
    Returns:
        Dictionary containing price data and metadata
    """
    try:
        url = f"{MORALIS_BASE_URL}/erc20/{token_address}/price"
        params = {"chain": chain}
        
        response = requests.get(url, headers=get_headers(), params=params)
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "token_address": token_address,
                "chain": chain,
                "price_data": data,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "message": response.text
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "token_address": token_address,
            "chain": chain
        }


@tool
def get_wallet_transaction_history(wallet_address: str, chain: str = "eth", limit: int = 10) -> Dict[str, Any]:
    """
    Get transaction history for a wallet address.
    
    Args:
        wallet_address: The wallet address to query
        chain: Blockchain to query
        limit: Number of transactions to return (max 100)
        
    Returns:
        Dictionary containing transaction history
    """
    try:
        url = f"{MORALIS_BASE_URL}/{wallet_address}"
        params = {"chain": chain, "limit": min(limit, 100)}
        
        response = requests.get(url, headers=get_headers(), params=params)
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "wallet_address": wallet_address,
                "chain": chain,
                "transaction_count": data.get("total", 0),
                "transactions": data.get("result", []),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "message": response.text
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "wallet_address": wallet_address,
            "chain": chain
        }


@tool
def get_solana_wallet_tokens(wallet_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """
    Get Solana token balances for a wallet address.
    
    Args:
        wallet_address: The Solana wallet address to query
        network: Solana network (mainnet, devnet, testnet)
        
    Returns:
        Dictionary containing Solana token balances
    """
    try:
        url = f"{SOLANA_BASE_URL}/account/{network}/{wallet_address}/tokens"
        
        response = requests.get(url, headers=get_headers())
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "wallet_address": wallet_address,
                "network": network,
                "tokens": data,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "message": response.text
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "wallet_address": wallet_address,
            "network": network
        }


@tool
def get_solana_token_price(token_address: str, network: str = "mainnet") -> Dict[str, Any]:
    """
    Get Solana token price data.
    
    Args:
        token_address: The Solana token mint address
        network: Solana network (mainnet, devnet, testnet)
        
    Returns:
        Dictionary containing Solana token price data
    """
    try:
        url = f"{SOLANA_BASE_URL}/token/{network}/{token_address}/price"
        
        response = requests.get(url, headers=get_headers())
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "token_address": token_address,
                "network": network,
                "price_data": data,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "message": response.text
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "token_address": token_address,
            "network": network
        }


@tool
def analyze_wallet_portfolio(wallet_address: str, chains: List[str] = None) -> Dict[str, Any]:
    """
    Comprehensive wallet portfolio analysis across multiple chains.
    
    Args:
        wallet_address: The wallet address to analyze
        chains: List of chains to analyze (defaults to major chains)
        
    Returns:
        Dictionary containing comprehensive portfolio analysis
    """
    if chains is None:
        chains = ["eth", "polygon", "bsc", "avalanche"]
    
    portfolio_data = {
        "wallet_address": wallet_address,
        "analysis_timestamp": datetime.now().isoformat(),
        "chains_analyzed": chains,
        "total_tokens": 0,
        "total_nfts": 0,
        "chain_breakdown": {}
    }
    
    for chain in chains:
        try:
            # Get token balances
            tokens_result = get_wallet_token_balances(wallet_address, chain)
            nfts_result = get_wallet_nft_collection(wallet_address, chain)
            
            chain_data = {
                "tokens": tokens_result.get("tokens", []) if tokens_result.get("success") else [],
                "nfts": nfts_result.get("nfts", []) if nfts_result.get("success") else [],
                "token_count": len(tokens_result.get("tokens", [])) if tokens_result.get("success") else 0,
                "nft_count": nfts_result.get("total_nfts", 0) if nfts_result.get("success") else 0
            }
            
            portfolio_data["chain_breakdown"][chain] = chain_data
            portfolio_data["total_tokens"] += chain_data["token_count"]
            portfolio_data["total_nfts"] += chain_data["nft_count"]
            
        except Exception as e:
            portfolio_data["chain_breakdown"][chain] = {
                "error": str(e),
                "tokens": [],
                "nfts": [],
                "token_count": 0,
                "nft_count": 0
            }
    
    return {
        "success": True,
        "portfolio": portfolio_data
    }


# Import extended tools
try:
    from moralis_tools_extended import *
    EXTENDED_TOOLS_AVAILABLE = True
except ImportError:
    EXTENDED_TOOLS_AVAILABLE = False
    print("⚠️ Extended Moralis tools not available")

# Core EVM NFT Tools
EVM_NFT_TOOLS = [
    evm_getwalletnfts,
    evm_getmultiplenfts,
    evm_getwalletnftcollections,
    evm_getuniqueownersbycollection,
    evm_getnftcontracttransfers,
    evm_getnftbycontracttraits,
    evm_getnfttraitsbycollection,
    evm_getnfttrades,
    evm_getnfttradesbywallet,
    evm_getnftbulkcontractmetadata,
    evm_getnftmetadata,
    evm_getnftcontractsaleprices,
    evm_getnftsaleprices,
]

# Core EVM Token Tools
EVM_TOKEN_TOOLS = [
    evm_getswapsbytokenaddress,
    evm_gettokenholders,
    evm_gethistoricaltokenholders,
    evm_getmultipletokenprices,
    evm_gettokenowners,
    evm_gettokenmetadata,
    evm_gettokentransfers,
]

# Core EVM Wallet Tools
EVM_WALLET_TOOLS = [
    evm_getnativebalancesforaddresses,
    evm_getwalletapprovals,
    evm_getwallethistory,
    evm_getwallettokenbalancesprice,
    evm_getwalletnetworth,
]

# Core EVM Blockchain Tools
EVM_BLOCKCHAIN_TOOLS = [
    evm_gettransactionverbose,
    evm_getblock,
    evm_getlatestblocknumber,
    evm_getdatetoblock,
    evm_resolveaddress,
    evm_resolveensdomain,
]

# Legacy tools for backward compatibility
LEGACY_TOOLS = [
    get_wallet_token_balances,
    get_wallet_nft_collection,
    get_token_price,
    get_wallet_transaction_history,
    get_solana_wallet_tokens,
    get_solana_token_price,
    analyze_wallet_portfolio
]

# Combine all core tools
CORE_MORALIS_TOOLS = (
    EVM_NFT_TOOLS +
    EVM_TOKEN_TOOLS +
    EVM_WALLET_TOOLS +
    EVM_BLOCKCHAIN_TOOLS +
    LEGACY_TOOLS
)

# Extended tools (if available)
EXTENDED_MORALIS_TOOLS = []
if EXTENDED_TOOLS_AVAILABLE:
    # Market Data & Discovery Tools
    EXTENDED_MORALIS_TOOLS.extend([
        evm_gettoperc20tokensbymarketcap,
        evm_gettoperc20tokensbypricemovers,
        evm_gettopnftcollectionsbymarketcap,
        evm_gethottestnftcollectionsbytradingvolume,
        evm_gettopcryptocurrenciesbymarketcap,
        evm_gettopcryptocurrenciesbytradingvolume,
    ])

    # DeFi Tools
    EXTENDED_MORALIS_TOOLS.extend([
        evm_getdefisummary,
        evm_getdefipositionsbyprotocol,
        evm_getdefipositionssummary,
    ])

    # Analytics Tools
    EXTENDED_MORALIS_TOOLS.extend([
        evm_getwalletactivechains,
        evm_getwalletstats,
        evm_getnftcollectionstats,
        evm_getnftfloorpricebycontract,
        evm_getnfthistoricalfloorpricebycontract,
    ])

    # Solana Tools
    EXTENDED_MORALIS_TOOLS.extend([
        solana_getaggregatedtokenpairstats,
        solana_getcandlesticks,
        solana_getnftmetadata,
        solana_getnfts,
        solana_getpairstats,
        solana_getportfolio,
        solana_getsnipersbypairaddress,
        solana_getspl,
        solana_getswapsbypairaddress,
        solana_getswapsbytokenaddress,
        solana_getswapsbywalletaddress,
        solana_gettokenmetadata,
        solana_gettokenpairs,
        solana_gettokenprice,
        solana_getmultipletokenprices,
        solana_balance,
    ])

# Complete list of all Moralis tools
MORALIS_TOOLS = CORE_MORALIS_TOOLS + EXTENDED_MORALIS_TOOLS

print(f"✅ Loaded {len(CORE_MORALIS_TOOLS)} core Moralis tools")
if EXTENDED_TOOLS_AVAILABLE:
    print(f"✅ Loaded {len(EXTENDED_MORALIS_TOOLS)} extended Moralis tools")
    print(f"🚀 Total Moralis tools available: {len(MORALIS_TOOLS)}")
else:
    print(f"⚠️ Only core tools available: {len(MORALIS_TOOLS)}")
