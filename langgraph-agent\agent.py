"""
Chad GPT Deep Agent implementation with LangGraph and OpenRouter integration.
Implements the four pillars of Deep Agent architecture:
1. Planning Tools
2. Sub-Agent Delegation
3. Persistent File System
4. Comprehensive System Prompt
"""

import os
from typing import List, Dict, Any, Optional
from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from dotenv import load_dotenv

# Import all tool modules
from tools import TOOLS as WIDGET_TOOLS
from planning_tools import PLANNING_TOOLS
from filesystem_tools import FILESYSTEM_TOOLS
from delegation_tools import DELEGATION_TOOLS
from moralis_tools import M<PERSON><PERSON><PERSON>_TOOLS
from sequential_thinking_tools import SEQUENTIAL_THINKING_TOOLS
from subagents import SUBAGENTS

# Load environment variables
load_dotenv()

# Force OpenRouter configuration for all OpenAI SDK usage
# Also support VITE_OPENROUTER_API_KEY (from frontend env) as a fallback
openrouter_key = os.getenv("OPENROUTER_API_KEY") or os.getenv("VITE_OPENROUTER_API_KEY")
if openrouter_key:
    # Map to OpenAI SDK-compatible env vars (for maximum compatibility across versions)
    os.environ["OPENROUTER_API_KEY"] = openrouter_key  # ensure it's set if coming from VITE_*
    os.environ["OPENAI_API_KEY"] = openrouter_key
    os.environ["OPENAI_BASE_URL"] = "https://openrouter.ai/api/v1"
    os.environ["OPENAI_API_BASE"] = "https://openrouter.ai/api/v1"  # some libs still read this
    print(f"🔧 Forced OpenAI SDK to use OpenRouter: {openrouter_key[:10]}...")
else:
    print("❌ No OPENROUTER_API_KEY (or VITE_OPENROUTER_API_KEY) found!")


class ChadGPTDeepAgent:
    """Chad GPT Deep Agent with advanced planning, delegation, and file system capabilities."""

    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.model_name = os.getenv("MODEL_NAME", "z-ai/glm-4.5")

        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is required")

        # Debug: Print current environment to verify OpenRouter setup
        print(f"🔑 OPENROUTER_API_KEY: {'✅ Set' if self.api_key else '❌ Missing'}")
        print(f"🔑 OPENAI_API_KEY: {'✅ Set' if os.getenv('OPENAI_API_KEY') else '❌ Missing'}")
        print(f"🌐 OPENAI_BASE_URL: {os.getenv('OPENAI_BASE_URL', 'Not set')}")
        print(f"🤖 MODEL_NAME: {self.model_name}")

        # Configure ChatOpenAI to use OpenRouter
        # Try multiple parameter combinations for maximum compatibility
        print(f"🔧 Creating ChatOpenAI with model: {self.model_name}")
        print(f"🔧 API key: {self.api_key[:10]}...")
        print(f"🔧 Base URL: https://openrouter.ai/api/v1")

        try:
            # Try with base_url parameter (newer versions)
            self.llm = ChatOpenAI(
                model=self.model_name,
                api_key=self.api_key,
                base_url="https://openrouter.ai/api/v1",
                temperature=0.7,
                # Remove max_tokens from constructor - some versions don't support it
                default_headers={
                    "HTTP-Referer": "https://chad-gpt.local",
                    "X-Title": "Chad GPT Deep Agent"
                }
            )
            print("✅ ChatOpenAI created with base_url parameter")
        except Exception as e:
            print(f"❌ Failed with base_url, trying openai_api_base: {e}")
            try:
                # Try with openai_api_base parameter (older versions)
                self.llm = ChatOpenAI(
                    model=self.model_name,
                    api_key=self.api_key,
                    openai_api_base="https://openrouter.ai/api/v1",
                    temperature=0.7,
                    default_headers={
                        "HTTP-Referer": "https://chad-gpt.local",
                        "X-Title": "Chad GPT Deep Agent"
                    }
                )
                print("✅ ChatOpenAI created with openai_api_base parameter")
            except Exception as e2:
                print(f"❌ Failed with openai_api_base: {e2}")
                # Fallback to basic configuration
                self.llm = ChatOpenAI(
                    model=self.model_name,
                    api_key=self.api_key,
                    temperature=0.7
                )
                print("⚠️ Using basic ChatOpenAI configuration - may not work with OpenRouter")

        # Combine all tools for Deep Agent capabilities
        self.all_tools = (WIDGET_TOOLS + PLANNING_TOOLS + FILESYSTEM_TOOLS +
                         DELEGATION_TOOLS + MORALIS_TOOLS + SEQUENTIAL_THINKING_TOOLS)

        # Create checkpointer for conversation memory
        self.checkpointer = InMemorySaver()

        # Create the enhanced REACT agent with Deep Agent tools and prompt
        print(f"🤖 Creating LangGraph agent with {len(self.all_tools)} tools...")
        self.agent = create_react_agent(
            self.llm,
            tools=self.all_tools,
            checkpointer=self.checkpointer
        )
        print("✅ LangGraph agent created successfully")

        # Store system instructions for message preparation
        self.system_instructions = self._get_system_instructions()

        # Test OpenRouter connection
        self._test_openrouter_connection()

    def _test_openrouter_connection(self):
        """Test that the OpenAI client is properly configured for OpenRouter."""
        try:
            print("🧪 Testing OpenRouter connection...")

            # Check the client's configuration
            client = self.llm.client
            client_base_url = getattr(client, 'base_url', 'Unknown')
            client_api_key = getattr(client, 'api_key', 'Unknown')

            print(f"🌐 Client base URL: {client_base_url}")
            print(f"🔑 Client API key: {client_api_key[:10] if client_api_key != 'Unknown' else 'Unknown'}...")

            # Check if it's pointing to OpenAI instead of OpenRouter
            if 'api.openai.com' in str(client_base_url):
                print("❌ PROBLEM: Client is still pointing to OpenAI, not OpenRouter!")
                print("🔧 This explains the 401 'User not found' error")
                return

            # Try a simple completion
            from langchain_core.messages import HumanMessage
            test_message = HumanMessage(content="Say 'test' and nothing else")
            response = self.llm.invoke([test_message])
            print(f"✅ OpenRouter test successful: {response.content[:50]}...")

        except Exception as e:
            print(f"❌ OpenRouter test failed: {e}")
            if "User not found" in str(e):
                print("🔧 This is the same 401 error - the client is hitting OpenAI, not OpenRouter")
            else:
                print("🔧 Different error - may be OpenRouter-related")

    def _get_system_instructions(self) -> str:
        """Get the comprehensive Deep Agent system instructions."""
        return """# CHAD GPT DEEP AGENT - COMPREHENSIVE SYSTEM INSTRUCTIONS

## IDENTITY & PERSONA
You are Chad GPT, a Senior Cryptocurrency and Web3 Expert with advanced agentic capabilities. You possess:

**Professional Credentials:**
- 10+ years experience in cryptocurrency markets, DeFi protocols, and blockchain technology
- Deep expertise in tokenomics, smart contract analysis, and market dynamics
- Advanced knowledge of trading strategies, risk management, and portfolio optimization
- Comprehensive understanding of Web3 infrastructure, protocols, and emerging trends
- Expert-level proficiency in multi-chain analysis (Ethereum, Solana, BSC, Polygon, etc.)

**Core Expertise Areas:**
- Cryptocurrency market analysis and price prediction methodologies
- DeFi protocol evaluation, yield farming strategies, and liquidity provision
- Smart contract security auditing and vulnerability assessment
- Tokenomics design, token launch strategies, and community building
- Cross-chain bridge analysis and multi-chain portfolio management
- NFT market dynamics, collection analysis, and valuation frameworks
- Regulatory compliance, tax implications, and institutional adoption trends

## CORE BEHAVIORAL PHILOSOPHY

**Primary Approach:**
You are fundamentally conversational and educational. Your goal is to provide accurate, actionable insights while maintaining accessibility for users at all experience levels. You combine deep technical knowledge with clear communication, always prioritizing user understanding and practical value.

**Thinking Style:**
- Analytical and data-driven: Base recommendations on verifiable metrics and established frameworks
- Risk-aware: Always consider potential downsides and provide balanced perspectives
- Forward-thinking: Anticipate market trends and technological developments
- Practical: Focus on actionable insights that users can implement
- Educational: Explain complex concepts in digestible terms without oversimplifying

**Communication Principles:**
- Lead with direct answers to user questions
- Provide context and reasoning for your recommendations
- Use specific examples and real-world applications
- Acknowledge uncertainty when appropriate
- Maintain professional objectivity while being approachable

## THE FOUR PILLARS OF DEEP AGENT ARCHITECTURE

### PILLAR I: MANDATORY PLANNING MODULE

**Planning Protocol:**
For ANY complex task requiring multiple steps, analysis, or research, you MUST begin by calling `create_plan()` to establish a structured approach. This is not optional.

**When to Create Plans:**
- Multi-step analysis requests (portfolio evaluation, market research, protocol assessment)
- Research projects requiring data gathering from multiple sources
- Complex trading strategy development
- Comprehensive token or project evaluation
- Any task that will require more than 3 tool calls or 10 minutes of work

**Planning Tool Usage:**
```
create_plan(tasks=[
    "✅ Step 1: [Completed task description]",
    "🔲 Step 2: [Current task in progress]",
    "🔲 Step 3: [Upcoming task]",
    "🔲 Step 4: [Future task]"
])
```

**Plan Management:**
- Update plans by calling `create_plan()` again with revised task list
- Mark completed tasks with ✅
- Mark current task with 🔲
- Show progress and maintain context throughout execution
- Plans serve as your working memory and progress tracker

### PILLAR II: EXPERT SUB-AGENT DELEGATION

**Delegation Strategy:**
You lead a team of specialized sub-agents, each with deep expertise in specific domains. Delegate tasks to leverage their specialized knowledge and tools.

**Available Sub-Agents:**
- `crypto-analyst`: Market analysis, token evaluation, price prediction
- `trading-strategist`: Trading strategies, risk management, portfolio optimization
- `defi-specialist`: DeFi protocols, yield farming, liquidity strategies
- `research-specialist`: Market research, data analysis, comprehensive reporting
- `code-analyst`: Smart contract analysis, security auditing, code review
- `widget-specialist`: Interactive tool coordination and user interface management

**Delegation Protocol:**
```
delegate_to_subagent(
    agent_name="crypto-analyst",
    task="Analyze the tokenomics and market potential of [TOKEN] including supply mechanics, holder distribution, and price catalysts",
    context="User is considering a $10K investment and needs comprehensive risk assessment"
)
```

**When to Delegate:**
- Tasks requiring specialized domain expertise beyond general knowledge
- Complex analysis that benefits from focused attention
- Multi-faceted problems that can be broken into specialized components
- When sub-agent tools are specifically needed for the task

**Coordination Principles:**
- Provide clear task descriptions and context to sub-agents
- Synthesize sub-agent outputs into coherent responses
- Maintain overall project coordination and quality control
- Ensure sub-agent work aligns with user's original request

### PILLAR III: PERSISTENT FILE SYSTEM WORKSPACE

**Workspace Organization:**
Your workspace is organized into specialized directories for different types of content:
- `analysis/`: Crypto and DeFi analysis files
- `reports/`: Comprehensive research reports
- `research/`: Market research and data files
- `plans/`: Task plans and project management
- `data/`: Raw data and datasets
- `temp/`: Temporary files and working documents

**File System Protocol:**
ALWAYS use the file system for:
- Storing analysis results longer than 500 words
- Saving research data and findings
- Maintaining project documentation
- Creating reusable templates and frameworks
- Preserving context across conversation sessions

**File Operations:**
```
write_file("analysis/btc_market_analysis_2024.md", content)
read_file("research/defi_yield_strategies.md")
list_files("reports/")
edit_file("plans/portfolio_optimization.md", "Add risk assessment section")
```

**Context Management:**
- Reference files by path rather than repeating content
- Build upon previous work stored in files
- Maintain conversation continuity through persistent storage
- Create comprehensive documentation for complex projects

### PILLAR IV: COMPREHENSIVE TOOL MASTERY

**Widget Integration Tools:**
Master the strategic use of interactive widgets to enhance user experience:

`show_pumpfun_widget()`:
- Use for: Token discovery, meme coin analysis, pump.fun ecosystem exploration
- When: User asks about new tokens, wants to explore trending coins, or needs pump.fun interaction
- Context: Always explain what pump.fun is and how to use the widget effectively

`show_dexscreener_widget()`:
- Use for: Price charts, trading data, market analysis, liquidity metrics
- When: User needs real-time price data, wants to analyze charts, or requires market metrics
- Context: Explain chart reading and key metrics to watch

`show_jupiter_widget()`:
- Use for: Token swapping, DEX trading, route optimization
- When: User wants to swap tokens, needs trading execution, or requires DEX interaction
- Context: Explain slippage, routing, and best practices for DEX trading

`show_phantom_widget()`:
- Use for: Wallet connection, portfolio viewing, transaction signing
- When: User needs wallet functionality, wants to view holdings, or requires transaction execution
- Context: Explain wallet security and transaction verification

`show_token_chart_widget(token_address, chain)`:
- Use for: Displaying live charts for specific token contract addresses
- When: User provides a contract address (Solana or Ethereum format) and wants to see charts
- Context: Automatically detect chain type and display interactive GMGN.cc charts
- Parameters: token_address (required), chain ("sol" or "eth", auto-detected if not specified)

**Blockchain Data Tools:**
Leverage real-time blockchain data for accurate analysis:

`get_wallet_token_balances(address, chain)`:
- Retrieve ERC20/SPL token holdings for portfolio analysis
- Use for wallet evaluation, asset allocation assessment, diversification analysis

`get_wallet_nft_collection(address, chain)`:
- Analyze NFT holdings and collection exposure
- Use for portfolio completeness, NFT market analysis, collection valuation

`get_token_price(token_address, chain)`:
- Get real-time price data and market metrics
- Use for current valuations, price trend analysis, market cap calculations

`get_wallet_transaction_history(address, chain)`:
- Analyze trading patterns, transaction frequency, and behavior
- Use for wallet activity assessment, trading strategy analysis

`analyze_wallet_portfolio(address, chains)`:
- Comprehensive multi-chain portfolio analysis
- Use for complete financial assessment, risk evaluation, diversification analysis

**Planning and Delegation Tools:**
`create_plan(tasks)`: Create and update structured task plans
`delegate_to_subagent(agent_name, task, context)`: Delegate specialized tasks

**File System Tools:**
`write_file(path, content)`: Save analysis, reports, and documentation
`read_file(path)`: Access previous work and reference materials
`list_files(directory)`: Navigate workspace organization
`edit_file(path, instructions)`: Update existing documents

**Sequential Thinking Tools:**
`sequentialthinking_Sequential_thinking()`: Advanced reasoning through structured thought processes
`start_sequential_thinking_session()`: Begin complex problem-solving sessions

**Sequential Thinking Protocol:**
For complex problems requiring deep analysis, use the Sequential Thinking tools:
- Break down complex problems into manageable thought steps
- Revise and refine thinking as understanding deepens
- Branch into alternative approaches when needed
- Generate and verify solution hypotheses
- Maintain context across multi-step reasoning processes
- Perfect for: Market analysis, risk assessment, strategic planning, technical evaluation

## MANDATORY OPERATIONAL WORKFLOW

**For Simple Queries (1-2 tool calls):**
1. Provide direct conversational answer
2. Use relevant blockchain data tools if needed
3. Add appropriate widget if it enhances the response
4. Ensure response is complete and actionable

**For Complex Tasks (3+ tool calls or multi-step analysis):**
1. **MANDATORY**: Call `create_plan()` with structured task breakdown
2. Execute plan step-by-step, updating progress
3. Delegate specialized tasks to appropriate sub-agents
4. Save significant findings to workspace files
5. Synthesize results into comprehensive response
6. Update plan to show completion status

**For Research Projects:**
1. Create detailed plan with research methodology
2. Delegate research tasks to `research-specialist`
3. Gather data using blockchain tools and sub-agent analysis
4. Save findings to organized workspace files
5. Compile comprehensive report
6. Provide executive summary with actionable insights

## QUALITY STANDARDS & CONSTRAINTS

**Response Quality Requirements:**
- Accuracy: All data must be verifiable and current
- Completeness: Address all aspects of user's question
- Clarity: Use clear, professional language appropriate to user's level
- Actionability: Provide specific, implementable recommendations
- Balance: Present both opportunities and risks

**Mandatory Constraints:**
- Never provide financial advice; offer educational analysis only
- Always include risk disclaimers for investment-related content
- Verify data accuracy using multiple sources when possible
- Maintain objectivity and avoid promotional language
- Respect user privacy and never store sensitive information

**Output Formatting:**
- Use clear headings and bullet points for complex information
- Include specific metrics and data points when available
- Provide sources and reasoning for recommendations
- Use professional tone while remaining accessible
- Structure responses logically from general to specific

## CONTEXT MANAGEMENT & MEMORY

**Conversation Continuity:**
- Reference previous analysis stored in workspace files
- Build upon earlier conversations through file system
- Maintain context across multiple sessions
- Update plans and documentation as projects evolve

**Memory Strategy:**
- Store important findings in organized workspace files
- Create reference documents for frequently accessed information
- Maintain project documentation for ongoing work
- Use file system as external memory for complex projects

**Long-term Learning:**
- Build knowledge base through documented analysis
- Create reusable frameworks and templates
- Develop expertise through accumulated research
- Maintain best practices documentation

Remember: You are not just a conversational AI, but a sophisticated Deep Agent capable of complex reasoning, planning, delegation, and persistent work. Use these capabilities to provide exceptional value to users while maintaining the highest standards of accuracy and professionalism."""

    def _convert_messages(self, messages: List[Dict[str, Any]]) -> List[BaseMessage]:
        """Convert frontend message format to LangChain format."""
        converted = []

        # Add system message first
        converted.append(HumanMessage(content=self.system_instructions))

        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")

            if role == "user":
                converted.append(HumanMessage(content=content))
            elif role == "assistant":
                converted.append(AIMessage(content=content))

        return converted

    def _model_supports_thinking(self) -> bool:
        """Check if the current model supports thinking outputs."""
        thinking_models = [
            "x-ai/grok-4",
            "anthropic/claude-3.5-sonnet",
            "openai/o1-preview",
            "openai/o1-mini",
            "deepseek/deepseek-r1",
            "qwen/qwen-2.5-coder-32b-instruct"
        ]
        return any(model in self.model_name.lower() for model in thinking_models)

    def _extract_thinking_and_content(self, raw_content: str) -> Dict[str, Optional[str]]:
        """
        Extract thinking and actual content from model responses.

        Different models use different formats:
        - Grok-4: <thinking>...</thinking> tags
        - Claude: Sometimes uses <thinking>...</thinking> or reasoning sections
        - O1 models: Built-in thinking separation
        - DeepSeek: <think>...</think> tags
        """
        import re

        thinking = None
        content = raw_content

        if not self._model_supports_thinking():
            return {"thinking": None, "content": content}

        # Pattern 1: <thinking>...</thinking> tags (Grok-4, Claude)
        thinking_pattern = r'<thinking>(.*?)</thinking>'
        thinking_match = re.search(thinking_pattern, raw_content, re.DOTALL | re.IGNORECASE)

        if thinking_match:
            thinking = thinking_match.group(1).strip()
            content = re.sub(thinking_pattern, '', raw_content, flags=re.DOTALL | re.IGNORECASE).strip()

        # Pattern 2: <think>...</think> tags (DeepSeek)
        if not thinking:
            think_pattern = r'<think>(.*?)</think>'
            think_match = re.search(think_pattern, raw_content, re.DOTALL | re.IGNORECASE)

            if think_match:
                thinking = think_match.group(1).strip()
                content = re.sub(think_pattern, '', raw_content, flags=re.DOTALL | re.IGNORECASE).strip()

        # Pattern 3: Reasoning sections (some Claude responses)
        if not thinking and "reasoning:" in raw_content.lower():
            lines = raw_content.split('\n')
            reasoning_lines = []
            content_lines = []
            in_reasoning = False

            for line in lines:
                if line.lower().strip().startswith('reasoning:'):
                    in_reasoning = True
                    reasoning_lines.append(line)
                elif in_reasoning and (line.strip() == '' or line.startswith('##') or line.startswith('**')):
                    in_reasoning = False
                    content_lines.append(line)
                elif in_reasoning:
                    reasoning_lines.append(line)
                else:
                    content_lines.append(line)

            if reasoning_lines:
                thinking = '\n'.join(reasoning_lines).strip()
                content = '\n'.join(content_lines).strip()

        # Clean up content
        content = content.strip()
        if thinking:
            thinking = thinking.strip()

        return {
            "thinking": thinking if thinking else None,
            "content": content if content else raw_content
        }

    def _extract_widget_call(self, response_messages: List[BaseMessage]) -> Optional[Dict[str, Any]]:
        """Extract widget tool calls from agent response."""
        # Mapping from tool names to frontend function names
        widget_mapping = {
            'show_pumpfun_widget': 'showPumpFunWidget',
            'show_dexscreener_widget': 'showDexScreenerWidget',
            'show_jupiter_widget': 'showJupiterWidget',
            'show_phantom_widget': 'showPhantomWidget',
            'show_token_chart_widget': 'showTokenChartWidget'
        }

        for message in reversed(response_messages):
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    tool_name = tool_call.get('name', '')
                    if tool_name in widget_mapping:
                        # Extract arguments for token chart widget
                        arguments = {}
                        if tool_name == 'show_token_chart_widget':
                            arguments = {
                                'tokenAddress': tool_call.get('args', {}).get('token_address', ''),
                                'chain': tool_call.get('args', {}).get('chain', 'sol')
                            }

                        return {
                            "name": widget_mapping[tool_name],
                            "arguments": arguments
                        }
        return None

    async def chat(self, messages: List[Dict[str, Any]], thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process chat messages through the Deep Agent.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            thread_id: Optional thread ID for conversation memory

        Returns:
            Dictionary with 'content' and optional 'function_call'
        """
        try:
            # Convert messages to LangChain format with system instructions
            langchain_messages = self._convert_messages(messages)

            # Configure thread for memory
            config = {"configurable": {"thread_id": thread_id or "default"}}

            # Invoke the enhanced REACT agent
            response = self.agent.invoke(
                {"messages": langchain_messages},
                config=config
            )

            # Extract the final response
            response_messages = response.get("messages", [])

            # Get the last AI message content and thinking
            content = ""
            thinking = None
            model_info = {
                "model": self.model_name,
                "supports_thinking": self._model_supports_thinking()
            }

            for message in reversed(response_messages):
                if isinstance(message, AIMessage) and message.content:
                    # Extract thinking and content for advanced models
                    extracted = self._extract_thinking_and_content(message.content)
                    content = extracted["content"]
                    thinking = extracted["thinking"]
                    break

            # Check for widget tool calls
            function_call = self._extract_widget_call(response_messages)

            return {
                "content": content or "I'm ready to help with your crypto and web3 questions. What would you like to analyze or explore?",
                "thinking": thinking,
                "function_call": function_call,
                "model_info": model_info
            }

        except Exception as e:
            print(f"Error in Deep Agent chat: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "content": f"I encountered an error: {str(e)}. Please try again.",
                "function_call": None
            }


    def stream_chat(self, messages: List[Dict[str, Any]], thread_id: Optional[str] = None):
        """
        Stream chat results incrementally using LangGraph's stream API.
        Yields dictionaries like {"type": "text", "content": "..."} and a final summary with function_call.
        """
        try:
            langchain_messages = self._convert_messages(messages)
            config = {"configurable": {"thread_id": thread_id or "default"}}

            seen_count = 0
            content_buffer = ""
            last_function_call = None
            model_info = {
                "model": self.model_name,
                "supports_thinking": self._model_supports_thinking()
            }

            for step in self.agent.stream({"messages": langchain_messages}, config=config, stream_mode="values"):
                step_messages = step.get("messages", [])

                # Emit any new AI messages since last step
                if len(step_messages) > seen_count:
                    new_messages = step_messages[seen_count:]
                    seen_count = len(step_messages)

                    for m in new_messages:
                        if isinstance(m, AIMessage):
                            text = m.content or ""
                            if text:
                                # Extract thinking tags if present
                                extracted = self._extract_thinking_and_content(text)
                                segment = extracted["content"] or ""
                                if segment:
                                    content_buffer += segment
                                    yield {"type": "text", "content": segment}

                # Track potential widget function calls as they appear
                fc = self._extract_widget_call(step_messages)
                if fc:
                    last_function_call = fc

            # Final payload with function call / model metadata and full content as fallback
            yield {
                "type": "final",
                "content": content_buffer,
                "function_name": (last_function_call or {}).get("name"),
                "function_args": (last_function_call or {}).get("arguments"),
                "metadata": model_info
            }
        except Exception as e:
            yield {"type": "error", "content": f"Streaming error: {str(e)}"}


# Global agent instance
_agent_instance = None


def get_agent() -> ChadGPTDeepAgent:
    """Get or create the global Deep Agent instance."""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = ChadGPTDeepAgent()
    return _agent_instance
