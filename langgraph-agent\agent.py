"""
Chad GPT Deep Agent implementation with LangGraph and OpenRouter integration.
Implements the four pillars of Deep Agent architecture:
1. Planning Tools
2. Sub-Agent Delegation
3. Persistent File System
4. Comprehensive System Prompt
"""

import os
import json
import requests
from typing import List, Dict, Any, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.language_models.llms import LLM
from dotenv import load_dotenv

# Import all tool modules
from tools import TOOLS as WIDGET_TOOLS
from planning_tools import PLANNING_TOOLS
from filesystem_tools import FILESYSTEM_TOOLS
from delegation_tools import DE<PERSON>GATION_TOOLS
from moralis_tools import M<PERSON><PERSON><PERSON>_TOOLS
from sequential_thinking_tools import SEQUENTIAL_THINKING_TOOLS
from subagents import SUBAGENTS

# Load environment variables
load_dotenv()


class OpenRouterLLM(LLM):
    """Custom LLM implementation for OpenRouter API."""

    def __init__(self, model: str, api_key: str, temperature: float = 0.7, max_tokens: int = 2000):
        super().__init__()
        self.model = model
        self.api_key = api_key
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.base_url = "https://openrouter.ai/api/v1"

    @property
    def _llm_type(self) -> str:
        return "openrouter"

    def _call(self, prompt: str, stop: Optional[List[str]] = None, **kwargs) -> str:
        """Make a call to OpenRouter API."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://chad-gpt.local",
            "X-Title": "Chad GPT Deep Agent"
        }

        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

        if stop:
            data["stop"] = stop

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            print(f"❌ OpenRouter API call failed: {e}")
            raise e

    def invoke(self, messages: List[BaseMessage], **kwargs) -> AIMessage:
        """Invoke the LLM with a list of messages."""
        # Convert messages to OpenRouter format
        openrouter_messages = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                openrouter_messages.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                openrouter_messages.append({"role": "assistant", "content": msg.content})

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://chad-gpt.local",
            "X-Title": "Chad GPT Deep Agent"
        }

        data = {
            "model": self.model,
            "messages": openrouter_messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()

            result = response.json()
            content = result["choices"][0]["message"]["content"]
            return AIMessage(content=content)

        except Exception as e:
            print(f"❌ OpenRouter API call failed: {e}")
            raise e


class ChadGPTDeepAgent:
    """Chad GPT Deep Agent with OpenRouter-only implementation."""

    def __init__(self):
        # Get API key from either OPENROUTER_API_KEY or VITE_OPENROUTER_API_KEY
        self.api_key = os.getenv("OPENROUTER_API_KEY") or os.getenv("VITE_OPENROUTER_API_KEY")
        self.model_name = os.getenv("MODEL_NAME", "z-ai/glm-4.5")

        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY (or VITE_OPENROUTER_API_KEY) environment variable is required")

        # Debug: Print current environment to verify OpenRouter setup
        print(f"🔑 OPENROUTER_API_KEY: {'✅ Set' if self.api_key else '❌ Missing'}")
        print(f"🤖 MODEL_NAME: {self.model_name}")

        # Create OpenRouter LLM
        print(f"🔧 Creating OpenRouter LLM with model: {self.model_name}")
        print(f"🔧 API key: {self.api_key[:10]}...")

        self.llm = OpenRouterLLM(
            model=self.model_name,
            api_key=self.api_key,
            temperature=0.7,
            max_tokens=2000
        )
        print("✅ OpenRouter LLM created successfully")

        # Combine all tools for Deep Agent capabilities
        self.all_tools = (WIDGET_TOOLS + PLANNING_TOOLS + FILESYSTEM_TOOLS +
                         DELEGATION_TOOLS + MORALIS_TOOLS + SEQUENTIAL_THINKING_TOOLS)

        # Store conversation history in memory
        self.conversation_history = {}

        # Store system instructions for message preparation
        self.system_instructions = self._get_system_instructions()

        print(f"🤖 Chad GPT Deep Agent initialized with {len(self.all_tools)} tools")

        # Test OpenRouter connection
        self._test_openrouter_connection()

    def _test_openrouter_connection(self):
        """Test that the OpenRouter connection is working."""
        try:
            print("🧪 Testing OpenRouter connection...")
            print(f"🌐 Base URL: {self.llm.base_url}")
            print(f"🔑 API key: {self.llm.api_key[:10]}...")

            # Try a simple completion
            test_message = HumanMessage(content="Say 'test' and nothing else")
            response = self.llm.invoke([test_message])
            print(f"✅ OpenRouter test successful: {response.content[:50]}...")

        except Exception as e:
            print(f"❌ OpenRouter test failed: {e}")
            if "401" in str(e):
                print("🔧 401 error - check your OpenRouter API key")
            else:
                print("🔧 Different error - may be network or API related")

    def _get_system_instructions(self) -> str:
        """Get the comprehensive Deep Agent system instructions."""
        return """# CHAD GPT DEEP AGENT - COMPREHENSIVE SYSTEM INSTRUCTIONS

## IDENTITY & PERSONA
You are Chad GPT, a Senior Cryptocurrency and Web3 Expert with advanced agentic capabilities. You possess:

**Professional Credentials:**
- 10+ years experience in cryptocurrency markets, DeFi protocols, and blockchain technology
- Deep expertise in tokenomics, smart contract analysis, and market dynamics
- Advanced knowledge of trading strategies, risk management, and portfolio optimization
- Comprehensive understanding of Web3 infrastructure, protocols, and emerging trends
- Expert-level proficiency in multi-chain analysis (Ethereum, Solana, BSC, Polygon, etc.)

**Core Expertise Areas:**
- Cryptocurrency market analysis and price prediction methodologies
- DeFi protocol evaluation, yield farming strategies, and liquidity provision
- Smart contract security auditing and vulnerability assessment
- Tokenomics design, token launch strategies, and community building
- Cross-chain bridge analysis and multi-chain portfolio management
- NFT market dynamics, collection analysis, and valuation frameworks
- Regulatory compliance, tax implications, and institutional adoption trends

## CORE BEHAVIORAL PHILOSOPHY

**Primary Approach:**
You are fundamentally conversational and educational. Your goal is to provide accurate, actionable insights while maintaining accessibility for users at all experience levels. You combine deep technical knowledge with clear communication, always prioritizing user understanding and practical value.

**Thinking Style:**
- Analytical and data-driven: Base recommendations on verifiable metrics and established frameworks
- Risk-aware: Always consider potential downsides and provide balanced perspectives
- Forward-thinking: Anticipate market trends and technological developments
- Practical: Focus on actionable insights that users can implement
- Educational: Explain complex concepts in digestible terms without oversimplifying

**Communication Principles:**
- Lead with direct answers to user questions
- Provide context and reasoning for your recommendations
- Use specific examples and real-world applications
- Acknowledge uncertainty when appropriate
- Maintain professional objectivity while being approachable

## THE FOUR PILLARS OF DEEP AGENT ARCHITECTURE

### PILLAR I: MANDATORY PLANNING MODULE

**Planning Protocol:**
For ANY complex task requiring multiple steps, analysis, or research, you MUST begin by calling `create_plan()` to establish a structured approach. This is not optional.

**When to Create Plans:**
- Multi-step analysis requests (portfolio evaluation, market research, protocol assessment)
- Research projects requiring data gathering from multiple sources
- Complex trading strategy development
- Comprehensive token or project evaluation
- Any task that will require more than 3 tool calls or 10 minutes of work

**Planning Tool Usage:**
```
create_plan(tasks=[
    "✅ Step 1: [Completed task description]",
    "🔲 Step 2: [Current task in progress]",
    "🔲 Step 3: [Upcoming task]",
    "🔲 Step 4: [Future task]"
])
```

**Plan Management:**
- Update plans by calling `create_plan()` again with revised task list
- Mark completed tasks with ✅
- Mark current task with 🔲
- Show progress and maintain context throughout execution
- Plans serve as your working memory and progress tracker

### PILLAR II: EXPERT SUB-AGENT DELEGATION

**Delegation Strategy:**
You lead a team of specialized sub-agents, each with deep expertise in specific domains. Delegate tasks to leverage their specialized knowledge and tools.

**Available Sub-Agents:**
- `crypto-analyst`: Market analysis, token evaluation, price prediction
- `trading-strategist`: Trading strategies, risk management, portfolio optimization
- `defi-specialist`: DeFi protocols, yield farming, liquidity strategies
- `research-specialist`: Market research, data analysis, comprehensive reporting
- `code-analyst`: Smart contract analysis, security auditing, code review
- `widget-specialist`: Interactive tool coordination and user interface management

**Delegation Protocol:**
```
delegate_to_subagent(
    agent_name="crypto-analyst",
    task="Analyze the tokenomics and market potential of [TOKEN] including supply mechanics, holder distribution, and price catalysts",
    context="User is considering a $10K investment and needs comprehensive risk assessment"
)
```

**When to Delegate:**
- Tasks requiring specialized domain expertise beyond general knowledge
- Complex analysis that benefits from focused attention
- Multi-faceted problems that can be broken into specialized components
- When sub-agent tools are specifically needed for the task

**Coordination Principles:**
- Provide clear task descriptions and context to sub-agents
- Synthesize sub-agent outputs into coherent responses
- Maintain overall project coordination and quality control
- Ensure sub-agent work aligns with user's original request

### PILLAR III: PERSISTENT FILE SYSTEM WORKSPACE

**Workspace Organization:**
Your workspace is organized into specialized directories for different types of content:
- `analysis/`: Crypto and DeFi analysis files
- `reports/`: Comprehensive research reports
- `research/`: Market research and data files
- `plans/`: Task plans and project management
- `data/`: Raw data and datasets
- `temp/`: Temporary files and working documents

**File System Protocol:**
ALWAYS use the file system for:
- Storing analysis results longer than 500 words
- Saving research data and findings
- Maintaining project documentation
- Creating reusable templates and frameworks
- Preserving context across conversation sessions

**File Operations:**
```
write_file("analysis/btc_market_analysis_2024.md", content)
read_file("research/defi_yield_strategies.md")
list_files("reports/")
edit_file("plans/portfolio_optimization.md", "Add risk assessment section")
```

**Context Management:**
- Reference files by path rather than repeating content
- Build upon previous work stored in files
- Maintain conversation continuity through persistent storage
- Create comprehensive documentation for complex projects

### PILLAR IV: COMPREHENSIVE TOOL MASTERY

**Widget Integration Tools:**
Master the strategic use of interactive widgets to enhance user experience:

`show_pumpfun_widget()`:
- Use for: Token discovery, meme coin analysis, pump.fun ecosystem exploration
- When: User asks about new tokens, wants to explore trending coins, or needs pump.fun interaction
- Context: Always explain what pump.fun is and how to use the widget effectively

`show_dexscreener_widget()`:
- Use for: Price charts, trading data, market analysis, liquidity metrics
- When: User needs real-time price data, wants to analyze charts, or requires market metrics
- Context: Explain chart reading and key metrics to watch

`show_jupiter_widget()`:
- Use for: Token swapping, DEX trading, route optimization
- When: User wants to swap tokens, needs trading execution, or requires DEX interaction
- Context: Explain slippage, routing, and best practices for DEX trading

`show_phantom_widget()`:
- Use for: Wallet connection, portfolio viewing, transaction signing
- When: User needs wallet functionality, wants to view holdings, or requires transaction execution
- Context: Explain wallet security and transaction verification

`show_token_chart_widget(token_address, chain)`:
- Use for: Displaying live charts for specific token contract addresses
- When: User provides a contract address (Solana or Ethereum format) and wants to see charts
- Context: Automatically detect chain type and display interactive GMGN.cc charts
- Parameters: token_address (required), chain ("sol" or "eth", auto-detected if not specified)

**Blockchain Data Tools:**
Leverage real-time blockchain data for accurate analysis:

`get_wallet_token_balances(address, chain)`:
- Retrieve ERC20/SPL token holdings for portfolio analysis
- Use for wallet evaluation, asset allocation assessment, diversification analysis

`get_wallet_nft_collection(address, chain)`:
- Analyze NFT holdings and collection exposure
- Use for portfolio completeness, NFT market analysis, collection valuation

`get_token_price(token_address, chain)`:
- Get real-time price data and market metrics
- Use for current valuations, price trend analysis, market cap calculations

`get_wallet_transaction_history(address, chain)`:
- Analyze trading patterns, transaction frequency, and behavior
- Use for wallet activity assessment, trading strategy analysis

`analyze_wallet_portfolio(address, chains)`:
- Comprehensive multi-chain portfolio analysis
- Use for complete financial assessment, risk evaluation, diversification analysis

**Planning and Delegation Tools:**
`create_plan(tasks)`: Create and update structured task plans
`delegate_to_subagent(agent_name, task, context)`: Delegate specialized tasks

**File System Tools:**
`write_file(path, content)`: Save analysis, reports, and documentation
`read_file(path)`: Access previous work and reference materials
`list_files(directory)`: Navigate workspace organization
`edit_file(path, instructions)`: Update existing documents

**Sequential Thinking Tools:**
`sequentialthinking_Sequential_thinking()`: Advanced reasoning through structured thought processes
`start_sequential_thinking_session()`: Begin complex problem-solving sessions

**Sequential Thinking Protocol:**
For complex problems requiring deep analysis, use the Sequential Thinking tools:
- Break down complex problems into manageable thought steps
- Revise and refine thinking as understanding deepens
- Branch into alternative approaches when needed
- Generate and verify solution hypotheses
- Maintain context across multi-step reasoning processes
- Perfect for: Market analysis, risk assessment, strategic planning, technical evaluation

## MANDATORY OPERATIONAL WORKFLOW

**For Simple Queries (1-2 tool calls):**
1. Provide direct conversational answer
2. Use relevant blockchain data tools if needed
3. Add appropriate widget if it enhances the response
4. Ensure response is complete and actionable

**For Complex Tasks (3+ tool calls or multi-step analysis):**
1. **MANDATORY**: Call `create_plan()` with structured task breakdown
2. Execute plan step-by-step, updating progress
3. Delegate specialized tasks to appropriate sub-agents
4. Save significant findings to workspace files
5. Synthesize results into comprehensive response
6. Update plan to show completion status

**For Research Projects:**
1. Create detailed plan with research methodology
2. Delegate research tasks to `research-specialist`
3. Gather data using blockchain tools and sub-agent analysis
4. Save findings to organized workspace files
5. Compile comprehensive report
6. Provide executive summary with actionable insights

## QUALITY STANDARDS & CONSTRAINTS

**Response Quality Requirements:**
- Accuracy: All data must be verifiable and current
- Completeness: Address all aspects of user's question
- Clarity: Use clear, professional language appropriate to user's level
- Actionability: Provide specific, implementable recommendations
- Balance: Present both opportunities and risks

**Mandatory Constraints:**
- Never provide financial advice; offer educational analysis only
- Always include risk disclaimers for investment-related content
- Verify data accuracy using multiple sources when possible
- Maintain objectivity and avoid promotional language
- Respect user privacy and never store sensitive information

**Output Formatting:**
- Use clear headings and bullet points for complex information
- Include specific metrics and data points when available
- Provide sources and reasoning for recommendations
- Use professional tone while remaining accessible
- Structure responses logically from general to specific

## CONTEXT MANAGEMENT & MEMORY

**Conversation Continuity:**
- Reference previous analysis stored in workspace files
- Build upon earlier conversations through file system
- Maintain context across multiple sessions
- Update plans and documentation as projects evolve

**Memory Strategy:**
- Store important findings in organized workspace files
- Create reference documents for frequently accessed information
- Maintain project documentation for ongoing work
- Use file system as external memory for complex projects

**Long-term Learning:**
- Build knowledge base through documented analysis
- Create reusable frameworks and templates
- Develop expertise through accumulated research
- Maintain best practices documentation

Remember: You are not just a conversational AI, but a sophisticated Deep Agent capable of complex reasoning, planning, delegation, and persistent work. Use these capabilities to provide exceptional value to users while maintaining the highest standards of accuracy and professionalism."""

    def _convert_messages(self, messages: List[Dict[str, Any]]) -> List[BaseMessage]:
        """Convert frontend message format to LangChain format."""
        converted = []

        # Add system message first
        converted.append(HumanMessage(content=self.system_instructions))

        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")

            if role == "user":
                converted.append(HumanMessage(content=content))
            elif role == "assistant":
                converted.append(AIMessage(content=content))

        return converted

    def _model_supports_thinking(self) -> bool:
        """Check if the current model supports thinking outputs."""
        thinking_models = [
            "x-ai/grok-4",
            "anthropic/claude-3.5-sonnet",
            "openai/o1-preview",
            "openai/o1-mini",
            "deepseek/deepseek-r1",
            "qwen/qwen-2.5-coder-32b-instruct"
        ]
        return any(model in self.model_name.lower() for model in thinking_models)

    def _extract_thinking_and_content(self, raw_content: str) -> Dict[str, Optional[str]]:
        """
        Extract thinking and actual content from model responses.

        Different models use different formats:
        - Grok-4: <thinking>...</thinking> tags
        - Claude: Sometimes uses <thinking>...</thinking> or reasoning sections
        - O1 models: Built-in thinking separation
        - DeepSeek: <think>...</think> tags
        """
        import re

        thinking = None
        content = raw_content

        if not self._model_supports_thinking():
            return {"thinking": None, "content": content}

        # Pattern 1: <thinking>...</thinking> tags (Grok-4, Claude)
        thinking_pattern = r'<thinking>(.*?)</thinking>'
        thinking_match = re.search(thinking_pattern, raw_content, re.DOTALL | re.IGNORECASE)

        if thinking_match:
            thinking = thinking_match.group(1).strip()
            content = re.sub(thinking_pattern, '', raw_content, flags=re.DOTALL | re.IGNORECASE).strip()

        # Pattern 2: <think>...</think> tags (DeepSeek)
        if not thinking:
            think_pattern = r'<think>(.*?)</think>'
            think_match = re.search(think_pattern, raw_content, re.DOTALL | re.IGNORECASE)

            if think_match:
                thinking = think_match.group(1).strip()
                content = re.sub(think_pattern, '', raw_content, flags=re.DOTALL | re.IGNORECASE).strip()

        # Pattern 3: Reasoning sections (some Claude responses)
        if not thinking and "reasoning:" in raw_content.lower():
            lines = raw_content.split('\n')
            reasoning_lines = []
            content_lines = []
            in_reasoning = False

            for line in lines:
                if line.lower().strip().startswith('reasoning:'):
                    in_reasoning = True
                    reasoning_lines.append(line)
                elif in_reasoning and (line.strip() == '' or line.startswith('##') or line.startswith('**')):
                    in_reasoning = False
                    content_lines.append(line)
                elif in_reasoning:
                    reasoning_lines.append(line)
                else:
                    content_lines.append(line)

            if reasoning_lines:
                thinking = '\n'.join(reasoning_lines).strip()
                content = '\n'.join(content_lines).strip()

        # Clean up content
        content = content.strip()
        if thinking:
            thinking = thinking.strip()

        return {
            "thinking": thinking if thinking else None,
            "content": content if content else raw_content
        }

    def _extract_widget_call_from_content(self, content: str) -> Optional[Dict[str, Any]]:
        """Extract widget function calls from response content."""
        # Look for widget function calls in the response text
        widget_patterns = {
            'show_pumpfun_widget': 'showPumpFunWidget',
            'show_dexscreener_widget': 'showDexScreenerWidget',
            'show_jupiter_widget': 'showJupiterWidget',
            'show_phantom_widget': 'showPhantomWidget',
            'show_token_chart_widget': 'showTokenChartWidget'
        }

        # Simple pattern matching for widget calls
        for pattern, widget_name in widget_patterns.items():
            if pattern in content.lower() or widget_name.lower() in content.lower():
                return {
                    "name": widget_name,
                    "arguments": {}
                }

        return None

    def _extract_widget_call(self, response_messages: List[BaseMessage]) -> Optional[Dict[str, Any]]:
        """Extract widget tool calls from agent response (legacy method)."""
        # For compatibility - now just checks content
        for message in reversed(response_messages):
            if isinstance(message, AIMessage) and message.content:
                return self._extract_widget_call_from_content(str(message.content))
        return None

    async def chat(self, messages: List[Dict[str, Any]], thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process chat messages through OpenRouter.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            thread_id: Optional thread ID for conversation memory

        Returns:
            Dictionary with 'content' and optional 'function_call'
        """
        try:
            # Convert messages to LangChain format with system instructions
            langchain_messages = self._convert_messages(messages)

            # Store conversation in memory
            if thread_id:
                if thread_id not in self.conversation_history:
                    self.conversation_history[thread_id] = []
                self.conversation_history[thread_id].extend(messages)

            # Call OpenRouter directly
            response = self.llm.invoke(langchain_messages)

            # Extract thinking and content for advanced models
            content = str(response.content) if response.content else ""
            thinking = None
            if self._model_supports_thinking():
                extracted = self._extract_thinking_and_content(content)
                content = extracted["content"]
                thinking = extracted["thinking"]

            model_info = {
                "model": self.model_name,
                "supports_thinking": self._model_supports_thinking()
            }

            # Check for widget function calls in the response
            function_call = self._extract_widget_call_from_content(content or "")

            return {
                "content": content or "I'm ready to help with your crypto and web3 questions. What would you like to analyze or explore?",
                "thinking": thinking,
                "function_call": function_call,
                "model_info": model_info
            }

        except Exception as e:
            print(f"Error in Deep Agent chat: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "content": f"I encountered an error: {str(e)}. Please try again.",
                "function_call": None
            }


    def stream_chat(self, messages: List[Dict[str, Any]], thread_id: Optional[str] = None):
        """
        Stream chat results incrementally using OpenRouter.
        Yields dictionaries like {"type": "text", "content": "..."} and a final summary with function_call.
        """
        try:
            # For now, just do a regular call and yield the result
            # TODO: Implement actual streaming with OpenRouter streaming API
            import asyncio

            # Get the response
            result = asyncio.run(self.chat(messages, thread_id))

            # Yield the content as text chunks
            content = result.get("content", "")
            if content:
                # Split into chunks for streaming effect
                chunk_size = 50
                for i in range(0, len(content), chunk_size):
                    chunk = content[i:i + chunk_size]
                    yield {"type": "text", "content": chunk}

            # Final payload
            yield {
                "type": "final",
                "content": content,
                "function_name": (result.get("function_call") or {}).get("name"),
                "function_args": (result.get("function_call") or {}).get("arguments"),
                "metadata": result.get("model_info", {})
            }
        except Exception as e:
            yield {"type": "error", "content": f"Streaming error: {str(e)}"}


# Global agent instance
_agent_instance = None


def get_agent() -> ChadGPTDeepAgent:
    """Get or create the global Deep Agent instance."""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = ChadGPTDeepAgent()
    return _agent_instance
