import React, { useState, useEffect } from 'react';
import { TrendingUp, ExternalLink, RefreshCw, AlertCircle } from 'lucide-react';

interface TokenChartWidgetProps {
  tokenAddress: string;
  chain?: string; // DexScreener chain ID (e.g., 'ethereum', 'solana', 'xrpl')
  isActive?: boolean;
  onClose?: () => void;
}

// Map DexScreener chain IDs to DexScreener URL format
function mapChainToDexScreener(chainId: string): string {
  const chainMap: { [key: string]: string } = {
    'ethereum': 'ethereum',
    'solana': 'solana',
    'bsc': 'bsc',
    'base': 'base',
    'arbitrum': 'arbitrum',
    'polygon': 'polygon',
    'avalanche': 'avalanche',
    'optimism': 'optimism',
    'fantom': 'fantom',
    'cronos': 'cronos',
    'xrpl': 'xrpl',
    'tron': 'tron',
    'sui': 'sui',
    'aptos': 'aptos'
  };

  return chainMap[chainId.toLowerCase()] || 'solana'; // Default to Solana
}

// Build DexScreener embed URL
function buildDexScreenerEmbedUrl(tokenAddress: string, chainId: string): string {
  const chain = mapChainToDexScreener(chainId);

  // Build the embed URL with all the proper parameters
  const embedParams = new URLSearchParams({
    'embed': '1',
    'loadChartSettings': '0',
    'tabs': '0',
    'info': '0',
    'chartLeftToolbar': '0',
    'chartDefaultOnMobile': '1',
    'chartTheme': 'dark',
    'theme': 'dark',
    'chartStyle': '1',
    'chartType': 'usd',
    'interval': '15',
    'hideBranding': '1',
    'hideFooter': '1',
    'minimal': '1',
    'trades': '0'
  });

  // Handle special cases for different chains
  if (chain === 'xrpl') {
    // XRPL uses a different format: token.address_BASE format
    return `https://dexscreener.com/${chain}/${tokenAddress}?${embedParams.toString()}`;
  } else if (chain === 'solana') {
    return `https://dexscreener.com/${chain}/${tokenAddress}?${embedParams.toString()}`;
  } else {
    // EVM chains use standard format
    return `https://dexscreener.com/${chain}/${tokenAddress}?${embedParams.toString()}`;
  }
}

// Chain detection based on token address format
function detectChain(address: string): string {
  // XRPL addresses start with 'r' and are 25-34 characters
  if (/^r[a-zA-Z0-9]{25,34}$/.test(address)) {
    return 'xrpl';
  }

  // Solana addresses are typically 32-44 characters, base58 encoded
  if (/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)) {
    return 'solana';
  }

  // Ethereum addresses start with 0x and are 42 characters
  if (/^0x[a-fA-F0-9]{40}$/.test(address)) {
    return 'ethereum';
  }

  // Default to Solana for pump.fun style addresses
  return 'solana';
}

// Validate if string looks like a contract address
function isValidContractAddress(address: string): boolean {
  // Solana address pattern
  const solanaPattern = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;

  // Ethereum/EVM address pattern
  const evmPattern = /^0x[a-fA-F0-9]{40}$/;

  return solanaPattern.test(address) || evmPattern.test(address);
}

interface TokenInfo {
  name: string;
  symbol: string;
  image_uri?: string;
}

export function TokenChartWidget({
  tokenAddress,
  chain,
  isActive = true,
  onClose
}: TokenChartWidgetProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [detectedChain, setDetectedChain] = useState<string>('');
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [isLoadingTokenInfo, setIsLoadingTokenInfo] = useState(true);

  // Fetch token information
  const fetchTokenInfo = async (address: string, chainType: string) => {
    try {
      // Try pump.fun API for Solana tokens
      if (chainType === 'solana') {
        try {
          const pumpResponse = await fetch(`https://frontend-api-v3.pump.fun/coins/${address}`);
          if (pumpResponse.ok) {
            const pumpData = await pumpResponse.json();
            return {
              name: pumpData.name || 'Unknown Token',
              symbol: pumpData.symbol || 'UNKNOWN',
              image_uri: pumpData.image_uri
            };
          }
        } catch (error) {
          console.warn('Pump.fun API failed:', error);
        }
      }

      // For Ethereum tokens, we could add other APIs here
      // For now, return a generic name with the address
      return {
        name: `Token ${address.slice(0, 8)}...${address.slice(-6)}`,
        symbol: chainType.toUpperCase(),
        image_uri: undefined
      };
    } catch (error) {
      console.error('Failed to fetch token info:', error);
      return {
        name: `Token ${address.slice(0, 8)}...${address.slice(-6)}`,
        symbol: chainType.toUpperCase(),
        image_uri: undefined
      };
    }
  };

  useEffect(() => {
    const initializeToken = async () => {
      if (!tokenAddress) {
        setError('No token address provided');
        setIsLoading(false);
        return;
      }

      if (!isValidContractAddress(tokenAddress)) {
        setError('Invalid contract address format');
        setIsLoading(false);
        return;
      }

      // If chain is provided, use it directly, otherwise detect from address
      const finalChain = chain || detectChain(tokenAddress);
      setDetectedChain(finalChain);
      setError(null);

      // Fetch token information
      setIsLoadingTokenInfo(true);
      try {
        const info = await fetchTokenInfo(tokenAddress, finalChain);
        setTokenInfo(info);
      } catch (error) {
        console.error('Failed to fetch token info:', error);
        // Set fallback info
        setTokenInfo({
          name: `Token ${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-6)}`,
          symbol: finalChain.toUpperCase(),
          image_uri: undefined
        });
      }
      setIsLoadingTokenInfo(false);

      setIsLoading(false);
    };

    initializeToken();
  }, [tokenAddress, chain]);

  // Scroll to top when chart becomes active
  useEffect(() => {
    if (isActive) {
      setTimeout(() => window.scrollTo({ top: 0, behavior: 'smooth' }), 50);
    }
  }, [isActive]);

  const handleRefresh = () => {
    setIsLoading(true);
    // Re-fetch token info and reload chart
    const refreshData = async () => {
      if (tokenAddress && detectedChain) {
        try {
          const info = await fetchTokenInfo(tokenAddress, detectedChain);
          setTokenInfo(info);
        } catch (error) {
          console.error('Failed to refresh token info:', error);
        }
      }
      setIsLoading(false);
    };
    refreshData();
  };

  const openInNewTab = () => {
    const chain = mapChainToDexScreener(detectedChain);
    const url = `https://dexscreener.com/${chain}/${tokenAddress}`;
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (error) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[95%] sm:max-w-[90%] mx-auto overflow-hidden">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <h3 className="font-semibold text-white">
              {tokenInfo ? `${tokenInfo.name} - Chart Error` : 'Token Chart Error'}
            </h3>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-[#666] hover:text-white transition-colors p-1"
            >
              ×
            </button>
          )}
        </div>
        <div className="text-red-400 text-sm">
          {error}
        </div>
        <div className="mt-2 text-[#666] text-xs">
          Please provide a valid contract address (Solana, Ethereum, XRPL, BSC, Base, etc.).
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[95%] sm:max-w-[90%] mx-auto overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {tokenInfo?.image_uri ? (
            <img
              src={tokenInfo.image_uri}
              alt={tokenInfo.name}
              className="w-8 h-8 rounded-lg object-cover ring-1 ring-[#181818]"
              onError={(e) => {
                // Fallback to icon if image fails
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`w-8 h-8 bg-gradient-to-br from-[#22c55e] to-emerald-400 rounded-lg flex items-center justify-center ${tokenInfo?.image_uri ? 'hidden' : ''}`}>
            <TrendingUp className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-white">
              {isLoadingTokenInfo ? (
                <div className="flex items-center gap-2">
                  <span>Loading token...</span>
                  <div className="w-3 h-3 border border-[#22c55e] border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : tokenInfo ? (
                tokenInfo.name
              ) : (
                'Unknown Token'
              )}
            </h3>
            <div className="flex items-center gap-2 text-xs text-[#666]">
              {tokenInfo && !isLoadingTokenInfo && (
                <>
                  <span className="text-[#22c55e] font-medium">{tokenInfo.symbol}</span>
                  <span>•</span>
                </>
              )}
              <span className="uppercase font-medium">{detectedChain}</span>
              <span>•</span>
              <span className="font-mono">{tokenAddress.slice(0, 8)}...{tokenAddress.slice(-6)}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200 disabled:opacity-50"
            title="Refresh Chart"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>

          <button
            onClick={openInNewTab}
            className="p-2 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200"
            title="Open in New Tab"
          >
            <ExternalLink className="w-4 h-4" />
          </button>

          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200"
              title="Close"
            >
              ×
            </button>
          )}
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative bg-[#0A0A0A] rounded-xl overflow-hidden">
        {isLoading && (
          <div className="absolute inset-0 bg-[#111] bg-opacity-90 flex items-center justify-center z-10">
            <div className="flex items-center gap-2 text-[#666]">
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span className="text-sm">Loading chart...</span>
            </div>
          </div>
        )}

        <div className="relative w-full h-[600px]">
          <style>{`
            .dexscreener-embed {
              position: relative;
              width: 100%;
              height: 100%;
            }
            .dexscreener-embed iframe {
              position: absolute;
              width: 100%;
              height: 100%;
              top: 0;
              left: 0;
              border: 0;
              border-radius: 12px;
            }
          `}</style>
          <div className="dexscreener-embed">
            <iframe
              src={buildDexScreenerEmbedUrl(tokenAddress, detectedChain)}
              title={`DexScreener Chart for ${tokenInfo ? `${tokenInfo.name} (${tokenInfo.symbol})` : tokenAddress}`}
              loading="lazy"
              sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
              onLoad={() => setIsLoading(false)}
              onError={() => {
                setError('Failed to load chart');
                setIsLoading(false);
              }}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-4 p-3 bg-[#0A0A0A] rounded-xl">
        <div className="flex items-center justify-between text-xs text-[#666]">
          <span>Powered by DexScreener</span>
          <button
            onClick={openInNewTab}
            className="flex items-center gap-1 hover:text-white transition-colors"
          >
            <span>View Full Chart</span>
            <ExternalLink className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
}

// Utility functions for external use
export { detectChain, isValidContractAddress };
