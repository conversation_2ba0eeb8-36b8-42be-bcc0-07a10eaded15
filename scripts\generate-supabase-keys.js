#!/usr/bin/env node
const crypto = require('crypto');

function b64url(input) {
  return Buffer.from(input).toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
}

function signHS256(secret, data) {
  return crypto.createHmac('sha256', secret).update(data).digest('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
}

function makeJwt(secret, role, expSeconds) {
  const header = { alg: 'HS256', typ: 'JWT' };
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    role,
    iss: 'supabase',
    iat: now,
    exp: now + expSeconds,
    aud: 'authenticated'
  };
  const encodedHeader = b64url(JSON.stringify(header));
  const encodedPayload = b64url(JSON.stringify(payload));
  const unsigned = `${encodedHeader}.${encodedPayload}`;
  const signature = signHS256(secret, unsigned);
  return `${unsigned}.${signature}`;
}

function main() {
  const [,, secret, outPath] = process.argv;
  if (!secret || !outPath) {
    process.stderr.write('Usage: node generate-supabase-keys.js <jwt_secret> <out_file>\n');
    process.exit(1);
  }
  // 10-year expiry
  const exp = 60 * 60 * 24 * 365 * 10;
  const anon = makeJwt(secret, 'anon', exp);
  const service = makeJwt(secret, 'service_role', exp);
  const fs = require('fs');
  fs.writeFileSync(outPath, JSON.stringify({ anon, service }, null, 2));
}

main();

