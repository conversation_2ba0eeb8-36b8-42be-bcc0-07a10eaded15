/**
 * React Hook for Agent Integration
 * 
 * Provides a React interface to the agent integration layer with
 * automatic state management and lifecycle handling.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { agentIntegration, AgentResponse, AgentMessage } from '../agentIntegration';

export interface UseAgentIntegrationOptions {
  threadId: string;
  autoConnect?: boolean;
  enableStreaming?: boolean;
  enableMemory?: boolean;
}

export interface UseAgentIntegrationReturn {
  // Connection state
  isConnected: boolean;
  connectionStatus: string;
  
  // Message handling
  sendMessage: (message: string, options?: SendMessageOptions) => Promise<void>;
  messages: AgentMessage[];
  isLoading: boolean;
  
  // Streaming
  streamingContent: string;
  isStreaming: boolean;
  
  // Error handling
  error: string | null;
  clearError: () => void;
  
  // History management
  clearHistory: () => void;
  
  // Configuration
  updateConfig: (config: any) => void;
}

export interface SendMessageOptions {
  includeWidgetContext?: boolean;
  includeHistory?: boolean;
  streaming?: boolean;
  image?: string;
}

export function useAgentIntegration(options: UseAgentIntegrationOptions): UseAgentIntegrationReturn {
  const { threadId, autoConnect = true, enableStreaming = true, enableMemory = true } = options;
  
  // State management
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Refs for cleanup
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Initialize connection and event listeners
  useEffect(() => {
    if (!autoConnect) return;
    
    // Connection status listener
    const handleConnectionChange = (status: string) => {
      setConnectionStatus(status);
      setIsConnected(status === 'connected');
    };
    
    // Message received listener
    const handleMessageReceived = (response: AgentResponse) => {
      const message: AgentMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        threadId,
        content: response.content,
        type: 'assistant',
        timestamp: response.timestamp,
        metadata: response.metadata
      };
      
      setMessages(prev => [...prev, message]);
    };
    
    // Streaming chunk listener
    const handleMessageChunk = (response: AgentResponse) => {
      if (response.type === 'text' || response.type === 'content') {
        setStreamingContent(prev => prev + response.content);
      }
    };
    
    // Error listener
    const handleError = (errorData: any) => {
      setError(errorData.error?.message || 'An error occurred');
      setIsLoading(false);
      setIsStreaming(false);
    };
    
    // Register listeners
    agentIntegration.on('connectionStatusChanged', handleConnectionChange);
    agentIntegration.on('messageReceived', handleMessageReceived);
    agentIntegration.on('messageChunk', handleMessageChunk);
    agentIntegration.on('error', handleError);
    
    // Initial connection status
    setConnectionStatus(agentIntegration.getConnectionStatus());
    setIsConnected(agentIntegration.getConnectionStatus() === 'connected');
    
    // Load existing message history
    const existingMessages = agentIntegration.getMessageHistory(threadId);
    setMessages(existingMessages);
    
    // Cleanup
    return () => {
      agentIntegration.off('connectionStatusChanged', handleConnectionChange);
      agentIntegration.off('messageReceived', handleMessageReceived);
      agentIntegration.off('messageChunk', handleMessageChunk);
      agentIntegration.off('error', handleError);
      
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [threadId, autoConnect]);
  
  // Send message function
  const sendMessage = useCallback(async (
    message: string, 
    sendOptions: SendMessageOptions = {}
  ) => {
    if (!message.trim()) return;
    
    // Clear previous error
    setError(null);
    
    // Add user message to state immediately
    const userMessage: AgentMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      threadId,
      content: message,
      type: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    
    try {
      setIsLoading(true);
      
      const options = {
        includeWidgetContext: true,
        includeHistory: true,
        streaming: enableStreaming,
        ...sendOptions
      };
      
      if (options.streaming) {
        setIsStreaming(true);
        setStreamingContent('');
        
        const responseStream = await agentIntegration.sendMessage(message, threadId, options);
        
        if (Symbol.asyncIterator in responseStream) {
          for await (const chunk of responseStream as AsyncIterable<AgentResponse>) {
            // Streaming chunks are handled by the event listener
            if (chunk.type === 'function_call') {
              // Handle function calls if needed
              console.log('Function call:', chunk.functionName, chunk.functionArgs);
            }
          }
        }
        
        // Add final streaming message to history
        if (streamingContent) {
          const assistantMessage: AgentMessage = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            threadId,
            content: streamingContent,
            type: 'assistant',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, assistantMessage]);
        }
        
        setIsStreaming(false);
        setStreamingContent('');
        
      } else {
        const response = await agentIntegration.sendMessage(message, threadId, options) as AgentResponse;
        
        // Non-streaming response is handled by the event listener
        if (response.type === 'function_call') {
          console.log('Function call:', response.functionName, response.functionArgs);
        }
      }
      
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setError(err.message || 'Failed to send message');
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
    }
  }, [threadId, enableStreaming, streamingContent]);
  
  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Clear history function
  const clearHistory = useCallback(() => {
    setMessages([]);
    agentIntegration.clearMessageHistory(threadId);
  }, [threadId]);
  
  // Update configuration
  const updateConfig = useCallback((config: any) => {
    agentIntegration.updateConfig(config);
  }, []);
  
  return {
    // Connection state
    isConnected,
    connectionStatus,
    
    // Message handling
    sendMessage,
    messages,
    isLoading,
    
    // Streaming
    streamingContent,
    isStreaming,
    
    // Error handling
    error,
    clearError,
    
    // History management
    clearHistory,
    
    // Configuration
    updateConfig
  };
}
