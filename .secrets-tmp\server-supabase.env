# AP3X-PumP Docker Environment Configuration
# Copy this to .env and customize for your deployment

# Application Configuration
NODE_ENV=production
PORT=9000

# TokenService Configuration
MORALIS_API_KEY=your-moralis-api-key-here
MORALIS_BASE_URL=https://solana-gateway.moralis.io
DEFAULT_LIMIT=50

# Authentication & Security
JWT_SECRET=your-super-secure-jwt-secret-change-this-in-production
BCRYPT_ROUNDS=12

# CORS Configuration
ALLOWED_ORIGINS=*

# Database Configuration (MongoDB)
MONGODB_URI=*****************************************************************
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=changeme
MONGO_DATABASE=ap3x_pump

# Redis Configuration
REDIS_URL=redis://:changeme@redis:6379
REDIS_PASSWORD=changeme

# SSL/HTTPS Configuration
SSL_CERT=/app/ssl/certificate.crt
SSL_KEY=/app/ssl/private.key

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS_PUBLIC=50
RATE_LIMIT_MAX_REQUESTS_BASIC=100
RATE_LIMIT_MAX_REQUESTS_PREMIUM=500
RATE_LIMIT_MAX_REQUESTS_ENTERPRISE=2000

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000

# Logging Configuration
LOG_LEVEL=info
VERBOSE_LOGGING=false

# Feature Flags
ENABLE_ANALYTICS=true
API_DOCS_ENABLED=true
HEALTH_CHECK_ENABLED=true
COMPRESSION_ENABLED=true
HELMET_ENABLED=true
DEBUG_MODE=false

# External Services
DEXSCREENER_API_URL=https://api.dexscreener.com
PUMPFUN_DIRECT_API_URL=https://frontend-api-v3.pump.fun

# Performance Configuration
REQUEST_TIMEOUT_MS=30000
MAX_REQUEST_SIZE=10mb
