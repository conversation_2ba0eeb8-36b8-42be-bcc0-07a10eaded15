"""
Phantom Widget Specialist Agent
Handles all Phantom wallet widget interactions, balance checking, and wallet management
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI


class PhantomWidgetAgent:
    """Specialized agent for Phantom wallet widget management."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get("model_name", "z-ai/glm-4.5")
        self.api_key = config.get("openrouter_api_key")
        
        # Configure LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=self.api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=0.7,
            max_tokens=2000
        )
        
        # Initialize tools
        self.tools = self._get_phantom_tools()

        # Create simple LLM without agent complexity
        # We'll handle tool calling manually to avoid validation issues
        
        # Widget state
        self.connected_wallets = {}
        self.balance_cache = {}
        
    def _get_system_prompt(self) -> str:
        """Get the Phantom specialist system prompt."""
        return """You are the Phantom Widget Specialist, an expert in Solana wallet management and Phantom wallet functionality.

## Your Expertise
- Phantom wallet features and capabilities
- Solana wallet security and best practices
- Token balance management and portfolio tracking
- Wallet connection and transaction signing

## Your Responsibilities
1. **Widget Management**: Launch Phantom widgets for wallet access
2. **Wallet Guidance**: Help users connect and manage their wallets
3. **Balance Tracking**: Assist with portfolio and balance monitoring
4. **Security Advice**: Provide wallet security recommendations

## When to Launch Widgets
ALWAYS call show_phantom_widget() when users:
- Want to "connect wallet" or "check balance"
- Ask about "Phantom" or "wallet"
- Need to see their "holdings" or "portfolio"
- Want to manage wallet settings

## Response Style
- Be security-conscious and helpful
- Provide clear wallet guidance
- Always launch the widget when appropriate
- Focus on user safety and best practices
- Use wallet terminology and security focus

Remember: You MUST actually call the show_phantom_widget() function when users need wallet access!"""

    def _get_phantom_tools(self) -> List:
        """Get Phantom-specific tools."""
        
        @tool
        def show_phantom_widget() -> Dict[str, Any]:
            """
            Display the Phantom wallet widget for wallet connection and management.
            
            Use this when users want to:
            - Connect their Phantom wallet
            - View wallet balance and holdings
            - Manage wallet settings
            - Access wallet functionality
            """
            return {
                "widget_type": "phantom",
                "description": "Displaying Phantom wallet widget"
            }
        
        @tool
        def get_wallet_guidance() -> Dict[str, Any]:
            """Provide guidance on wallet security and best practices."""
            return {
                "security_tips": [
                    "Never share your seed phrase with anyone",
                    "Always verify transaction details before signing",
                    "Use hardware wallets for large amounts",
                    "Keep your wallet software updated"
                ],
                "best_practices": [
                    "Regularly backup your wallet",
                    "Use strong passwords",
                    "Enable additional security features",
                    "Be cautious with DApp connections"
                ]
            }
        
        @tool
        def analyze_portfolio_health() -> Dict[str, Any]:
            """Analyze wallet portfolio health and diversification."""
            return {
                "analysis": "Portfolio health assessment",
                "diversification": "Checking token distribution and risk exposure",
                "recommendations": [
                    "Consider diversifying across different sectors",
                    "Monitor large position concentrations",
                    "Regular portfolio rebalancing"
                ]
            }
        
        return [show_phantom_widget, get_wallet_guidance, analyze_portfolio_health]
    
    async def process_request(
        self,
        message: str,
        thread_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a Phantom-related request."""
        try:
            # Determine if we should show widget based on message content
            should_show_widget = self._should_show_widget(message)

            # Generate response using direct LLM call
            system_prompt = self._get_system_prompt()

            response = await self.llm.ainvoke([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ])

            # Prepare function call if widget should be shown
            function_call = None
            if should_show_widget:
                function_call = {
                    "name": "showPhantomWidget",
                    "arguments": {}
                }

            return {
                "content": response.content,
                "function_call": function_call,
                "specialist": "phantom",
                "metadata": {
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent_type": "phantom_specialist"
                }
            }

        except Exception as e:
            return {
                "content": f"I encountered an error with wallet data: {str(e)}",
                "error": str(e),
                "specialist": "phantom"
            }

    def _should_show_widget(self, message: str) -> bool:
        """Determine if widget should be shown based on message content."""
        message_lower = message.lower()
        widget_triggers = [
            "show", "display", "open", "launch", "widget",
            "wallet", "balance", "phantom", "connect"
        ]
        return any(trigger in message_lower for trigger in widget_triggers)
    
    def _extract_widget_call(self, messages) -> Optional[Dict[str, Any]]:
        """Extract widget calls from agent response."""
        for message in reversed(messages):
            if hasattr(message, 'tool_calls') and message.tool_calls:
                for tool_call in message.tool_calls:
                    if tool_call.get('name') == 'show_phantom_widget':
                        return {
                            "name": "showPhantomWidget",
                            "arguments": {}
                        }
        return None
