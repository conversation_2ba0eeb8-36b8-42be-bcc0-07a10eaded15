#!/usr/bin/env python3
"""
Test script for Chad GPT LangGraph Agent.
"""

import asyncio
import json
from agent import get_agent


async def test_basic_chat():
    """Test basic chat functionality."""
    print("🧪 Testing basic chat...")
    
    agent = get_agent()
    
    messages = [
        {"role": "user", "content": "Hello, what can you help me with?"}
    ]
    
    response = await agent.chat(messages)
    print(f"✅ Basic chat response: {response['content']}")
    return response


async def test_pumpfun_widget():
    """Test PumpFun widget trigger."""
    print("🧪 Testing PumpFun widget trigger...")
    
    agent = get_agent()
    
    messages = [
        {"role": "user", "content": "Show me pump.fun data"}
    ]
    
    response = await agent.chat(messages)
    print(f"✅ PumpFun response: {response['content']}")
    if response.get('function_call'):
        print(f"✅ Function call: {response['function_call']}")
    return response


async def test_dexscreener_widget():
    """Test DexScreener widget trigger."""
    print("🧪 Testing DexScreener widget trigger...")
    
    agent = get_agent()
    
    messages = [
        {"role": "user", "content": "Show me dexscreener charts"}
    ]
    
    response = await agent.chat(messages)
    print(f"✅ DexScreener response: {response['content']}")
    if response.get('function_call'):
        print(f"✅ Function call: {response['function_call']}")
    return response


async def test_conversation_memory():
    """Test conversation memory with thread ID."""
    print("🧪 Testing conversation memory...")
    
    agent = get_agent()
    thread_id = "test_thread_123"
    
    # First message
    messages1 = [
        {"role": "user", "content": "My name is Alice"}
    ]
    
    response1 = await agent.chat(messages1, thread_id)
    print(f"✅ First message response: {response1['content']}")
    
    # Second message - should remember the name
    messages2 = [
        {"role": "user", "content": "What's my name?"}
    ]
    
    response2 = await agent.chat(messages2, thread_id)
    print(f"✅ Second message response: {response2['content']}")
    
    return response1, response2


async def test_crypto_knowledge():
    """Test crypto-specific knowledge."""
    print("🧪 Testing crypto knowledge...")
    
    agent = get_agent()
    
    messages = [
        {"role": "user", "content": "What is DeFi?"}
    ]
    
    response = await agent.chat(messages)
    print(f"✅ Crypto knowledge response: {response['content']}")
    return response


async def run_all_tests():
    """Run all tests."""
    print("🚀 Starting Chad GPT LangGraph Agent Tests")
    print("=" * 50)
    
    try:
        # Test basic functionality
        await test_basic_chat()
        print()
        
        # Test widget triggers
        await test_pumpfun_widget()
        print()
        
        await test_dexscreener_widget()
        print()
        
        # Test conversation memory
        await test_conversation_memory()
        print()
        
        # Test crypto knowledge
        await test_crypto_knowledge()
        print()
        
        print("🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_all_tests())
