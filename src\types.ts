export interface Message {
  id: string;
  content: string;
  type: 'user' | 'assistant' | 'widget';
  timestamp: Date;
  image?: string | null;
  threadId?: string;
  thinking?: string | null;  // For models that output thinking process
  model_info?: {
    model: string;
    supports_thinking: boolean;
  };
  // Widget-specific data
  widgetData?: {
    tokenAddress?: string;
    chain?: string;
    [key: string]: any;
  };
}

export interface ChatThread {
  id: string;
  title: string;
  lastMessage: string;
  lastActivity: Date;
  messageCount: number;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  image?: string | null;
}