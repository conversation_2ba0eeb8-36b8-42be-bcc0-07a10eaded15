/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      animation: {
        'fade-in': 'fade-in 0.3s ease-out forwards',
        'scale-in': 'scale-in 0.2s ease-out forwards',
        'slide-in': 'slide-in 0.2s ease-out forwards',
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(8px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'scale-in': {
          '0%': { opacity: '0', transform: 'scale(0.95)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        'slide-in': {
          '0%': { opacity: '0', transform: 'translateX(-8px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
      },
      screens: {
        'xs': '475px',
      },
      animation: {
        'fade-in': 'fade-in 0.3s ease-out forwards',
        'scale-in': 'scale-in 0.2s ease-out forwards',
        'slide-in': 'slide-in 0.2s ease-out forwards',
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(8px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'scale-in': {
          '0%': { opacity: '0', transform: 'scale(0.95)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        'slide-in': {
          '0%': { opacity: '0', transform: 'translateX(-8px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
      },
      colors: {
        'neur-dark': '#1A1A1A',
        'neur-darker': '#111111',
        'neur-gray': '#222222',
        'neur-text': '#9CA3AF',
      },
      boxShadow: {
        'glow': '0 0 0 1px rgba(34, 197, 94, 0.2), 0 2px 4px rgba(34, 197, 94, 0.1)',
        'glow-lg': '0 0 0 1px rgba(34, 197, 94, 0.2), 0 4px 6px rgba(34, 197, 94, 0.1)',
      },
      boxShadow: {
        'glow': '0 0 0 1px rgba(34, 197, 94, 0.2), 0 2px 4px rgba(34, 197, 94, 0.1)',
        'glow-lg': '0 0 0 1px rgba(34, 197, 94, 0.2), 0 4px 6px rgba(34, 197, 94, 0.1)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}