"""
Platform Integration Tools - Deep Agent integration with Chad GPT platform features.
Provides seamless integration with conversation management, user preferences, and platform state.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional
import json
import asyncio
from datetime import datetime


def get_platform_integration_tools(unified_agent):
    """Get platform integration tools with Deep Agent capabilities."""
    
    @tool
    def switch_ai_model(model_name: str) -> Dict[str, Any]:
        """
        Switch the active AI model for the conversation with enhanced model management.
        
        Args:
            model_name: Name of the model to switch to (e.g., 'anthropic/claude-3.5-sonnet')
            
        Returns:
            Dictionary with model switch result and capabilities
        """
        try:
            # Switch model through unified agent
            asyncio.run(unified_agent.switch_model(model_name))
            
            # Get model capabilities
            model_capabilities = _get_model_capabilities(model_name)
            
            return {
                "success": True,
                "previous_model": getattr(unified_agent, '_previous_model', 'unknown'),
                "new_model": model_name,
                "model_capabilities": model_capabilities,
                "message": f"Successfully switched to model {model_name}",
                "deep_agent_enhanced": True,
                "function_call": {
                    "name": "switch_ai_model",
                    "arguments": {"model_name": model_name}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to switch to model {model_name}: {str(e)}"
            }
    
    @tool
    def get_conversation_context(thread_id: str) -> Dict[str, Any]:
        """
        Get comprehensive conversation context and state information.
        
        Args:
            thread_id: Conversation thread identifier
            
        Returns:
            Dictionary with complete conversation context and metadata
        """
        try:
            # Get conversation state through unified agent
            conversation_state = asyncio.run(unified_agent.get_conversation_state(thread_id))
            
            # Get active widgets
            active_widgets = asyncio.run(unified_agent.get_active_widgets(thread_id))
            
            # Get conversation history
            conversation_history = asyncio.run(
                unified_agent.conversation_manager.get_conversation_history(thread_id, limit=10)
            )
            
            return {
                "success": True,
                "thread_id": thread_id,
                "conversation_state": conversation_state,
                "active_widgets": active_widgets,
                "recent_messages": conversation_history,
                "context_summary": unified_agent.conversation_manager.context_summaries.get(thread_id, ""),
                "deep_agent_enhanced": True,
                "message": f"Retrieved comprehensive context for thread {thread_id}",
                "function_call": {
                    "name": "get_conversation_context",
                    "arguments": {"thread_id": thread_id}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get conversation context: {str(e)}"
            }
    
    @tool
    def update_user_preferences(
        user_id: str,
        preferences: str
    ) -> Dict[str, Any]:
        """
        Update user preferences with enhanced preference management.
        
        Args:
            user_id: Unique identifier of the user
            preferences: JSON string of preferences to update
            
        Returns:
            Dictionary with preference update result
        """
        try:
            # Parse preferences
            prefs = json.loads(preferences)
            
            # Update through conversation manager
            asyncio.run(unified_agent.conversation_manager.update_user_preferences(user_id, prefs))
            
            # Get updated preferences
            updated_prefs = asyncio.run(unified_agent.conversation_manager.get_user_preferences(user_id))
            
            return {
                "success": True,
                "user_id": user_id,
                "updated_preferences": updated_prefs,
                "preference_categories": list(prefs.keys()),
                "message": f"Successfully updated preferences for user {user_id}",
                "deep_agent_enhanced": True,
                "function_call": {
                    "name": "update_user_preferences",
                    "arguments": {"user_id": user_id, "preference_count": len(prefs)}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to update user preferences: {str(e)}"
            }
    
    @tool
    def get_system_status() -> Dict[str, Any]:
        """
        Get comprehensive Deep Agent system status and health information.
        
        Returns:
            Dictionary with complete system status and performance metrics
        """
        try:
            # Get system status from unified agent
            system_status = asyncio.run(unified_agent.get_system_status())
            
            # Get additional metrics
            workspace_stats = asyncio.run(unified_agent.file_system_manager.get_workspace_stats())
            
            return {
                "success": True,
                "system_status": system_status,
                "workspace_stats": workspace_stats if workspace_stats.get("success") else {},
                "deep_agent_version": "2.0.0",
                "integration_status": {
                    "conversation_manager": "active",
                    "widget_coordinator": "active",
                    "workflow_orchestrator": "active",
                    "file_system_manager": "active",
                    "domain_agents": {
                        "crypto_analyst": "active",
                        "research_specialist": "active",
                        "development_assistant": "active",
                        "support_agent": "active"
                    }
                },
                "message": "Deep Agent system is fully operational",
                "function_call": {
                    "name": "get_system_status",
                    "arguments": {}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to get system status: {str(e)}"
            }
    
    @tool
    def create_conversation_summary(
        thread_id: str,
        include_widgets: bool = True,
        include_files: bool = True
    ) -> Dict[str, Any]:
        """
        Create a comprehensive summary of a conversation thread.
        
        Args:
            thread_id: Conversation thread identifier
            include_widgets: Whether to include widget information
            include_files: Whether to include file operations
            
        Returns:
            Dictionary with conversation summary and insights
        """
        try:
            # Get conversation data
            conversation = asyncio.run(unified_agent.conversation_manager.get_conversation(thread_id))
            
            # Analyze conversation patterns
            summary_data = {
                "thread_id": thread_id,
                "conversation_overview": {
                    "message_count": conversation.get("message_count", 0),
                    "duration": _calculate_conversation_duration(conversation),
                    "participants": ["user", "assistant"],
                    "context_summary": conversation.get("context_summary", "")
                },
                "activity_analysis": {
                    "tool_calls": _analyze_tool_usage(conversation),
                    "widget_interactions": _analyze_widget_usage(conversation) if include_widgets else {},
                    "file_operations": _analyze_file_operations(conversation) if include_files else {},
                    "domain_delegations": _analyze_domain_delegations(conversation)
                },
                "insights": {
                    "primary_topics": _extract_primary_topics(conversation),
                    "user_preferences": _infer_user_preferences(conversation),
                    "completion_status": _assess_completion_status(conversation)
                }
            }
            
            # Save summary to file
            summary_filename = f"conversation_summary_{thread_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            asyncio.run(unified_agent.file_system_manager.write_file(
                path=f"summaries/{summary_filename}",
                content=summary_data
            ))
            
            return {
                "success": True,
                "thread_id": thread_id,
                "summary": summary_data,
                "summary_file": summary_filename,
                "message": f"Created comprehensive summary for conversation {thread_id}",
                "deep_agent_enhanced": True,
                "function_call": {
                    "name": "create_conversation_summary",
                    "arguments": {"thread_id": thread_id}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create conversation summary: {str(e)}"
            }
    
    @tool
    def broadcast_to_widgets(
        thread_id: str,
        message: str,
        widget_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Broadcast a message to all or specific widgets in a conversation thread.
        
        Args:
            thread_id: Conversation thread identifier
            message: JSON string of message to broadcast
            widget_types: Optional list of widget types to target
            
        Returns:
            Dictionary with broadcast result
        """
        try:
            # Parse message
            msg = json.loads(message)
            
            # Get active widgets
            active_widgets = asyncio.run(unified_agent.get_active_widgets(thread_id))
            
            # Filter by widget types if specified
            target_widgets = active_widgets
            if widget_types:
                target_widgets = [w for w in active_widgets if w.get("type") in widget_types]
            
            # Broadcast through widget coordinator
            broadcast_result = asyncio.run(unified_agent.widget_coordinator.broadcast_to_widgets(
                thread_id=thread_id,
                message=msg,
                target_widgets=[w["id"] for w in target_widgets]
            ))
            
            return {
                "success": True,
                "thread_id": thread_id,
                "message_broadcasted": msg,
                "target_widget_count": len(target_widgets),
                "target_widgets": target_widgets,
                "broadcast_result": broadcast_result,
                "message": f"Broadcasted message to {len(target_widgets)} widgets",
                "deep_agent_enhanced": True,
                "function_call": {
                    "name": "broadcast_to_widgets",
                    "arguments": {"thread_id": thread_id, "widget_count": len(target_widgets)}
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to broadcast to widgets: {str(e)}"
            }
    
    # Helper functions for analysis
    def _get_model_capabilities(model_name: str) -> Dict[str, Any]:
        """Get capabilities for a specific model."""
        model_info = {
            "anthropic/claude-3.5-sonnet": {
                "provider": "Anthropic",
                "context_length": 200000,
                "capabilities": ["reasoning", "analysis", "coding", "creative_writing"],
                "strengths": ["Complex reasoning", "Code generation", "Analysis"]
            },
            "anthropic/claude-3-haiku": {
                "provider": "Anthropic", 
                "context_length": 200000,
                "capabilities": ["fast_responses", "basic_analysis", "simple_tasks"],
                "strengths": ["Speed", "Efficiency", "Quick responses"]
            },
            "openai/gpt-4": {
                "provider": "OpenAI",
                "context_length": 8192,
                "capabilities": ["reasoning", "analysis", "coding"],
                "strengths": ["General intelligence", "Problem solving"]
            }
        }
        
        return model_info.get(model_name, {
            "provider": "Unknown",
            "context_length": "Unknown",
            "capabilities": ["general"],
            "strengths": ["General purpose"]
        })
    
    def _calculate_conversation_duration(conversation: Dict[str, Any]) -> str:
        """Calculate conversation duration."""
        messages = conversation.get("message_history", [])
        if len(messages) < 2:
            return "Less than 1 minute"
        
        first_msg = messages[0].get("timestamp", "")
        last_msg = messages[-1].get("timestamp", "")
        
        if first_msg and last_msg:
            try:
                from datetime import datetime
                start = datetime.fromisoformat(first_msg.replace('Z', '+00:00'))
                end = datetime.fromisoformat(last_msg.replace('Z', '+00:00'))
                duration = end - start
                
                if duration.total_seconds() < 60:
                    return f"{int(duration.total_seconds())} seconds"
                elif duration.total_seconds() < 3600:
                    return f"{int(duration.total_seconds() / 60)} minutes"
                else:
                    return f"{int(duration.total_seconds() / 3600)} hours"
            except:
                return "Unknown duration"
        
        return "Unknown duration"
    
    def _analyze_tool_usage(conversation: Dict[str, Any]) -> Dict[str, int]:
        """Analyze tool usage patterns."""
        tool_counts = {}
        messages = conversation.get("message_history", [])
        
        for msg in messages:
            for tool_call in msg.get("tool_calls", []):
                tool_name = tool_call.get("name", "unknown")
                tool_counts[tool_name] = tool_counts.get(tool_name, 0) + 1
        
        return tool_counts
    
    def _analyze_widget_usage(conversation: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze widget usage patterns."""
        widget_types = {}
        messages = conversation.get("message_history", [])
        
        for msg in messages:
            for widget_update in msg.get("widget_updates", []):
                widget_type = widget_update.get("widget_type", "unknown")
                widget_types[widget_type] = widget_types.get(widget_type, 0) + 1
        
        return {"widget_types_used": widget_types}
    
    def _analyze_file_operations(conversation: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze file operation patterns."""
        file_ops = {"reads": 0, "writes": 0, "lists": 0}
        messages = conversation.get("message_history", [])
        
        for msg in messages:
            for tool_call in msg.get("tool_calls", []):
                tool_name = tool_call.get("name", "")
                if "read_file" in tool_name:
                    file_ops["reads"] += 1
                elif "write_file" in tool_name:
                    file_ops["writes"] += 1
                elif "list_files" in tool_name:
                    file_ops["lists"] += 1
        
        return file_ops
    
    def _analyze_domain_delegations(conversation: Dict[str, Any]) -> Dict[str, int]:
        """Analyze domain agent delegation patterns."""
        delegations = {}
        messages = conversation.get("message_history", [])
        
        for msg in messages:
            for tool_call in msg.get("tool_calls", []):
                tool_name = tool_call.get("name", "")
                if "delegate_to_" in tool_name:
                    agent_type = tool_name.replace("delegate_to_", "")
                    delegations[agent_type] = delegations.get(agent_type, 0) + 1
        
        return delegations
    
    def _extract_primary_topics(conversation: Dict[str, Any]) -> List[str]:
        """Extract primary topics from conversation."""
        topics = []
        context_summary = conversation.get("context_summary", "")
        
        if "crypto" in context_summary.lower():
            topics.append("Cryptocurrency")
        if "research" in context_summary.lower():
            topics.append("Research")
        if "widget" in context_summary.lower():
            topics.append("Widget Development")
        if "analysis" in context_summary.lower():
            topics.append("Analysis")
        
        return topics if topics else ["General Conversation"]
    
    def _infer_user_preferences(conversation: Dict[str, Any]) -> Dict[str, Any]:
        """Infer user preferences from conversation patterns."""
        return {
            "prefers_detailed_analysis": True,
            "uses_widgets": True,
            "saves_reports": True,
            "delegates_complex_tasks": True
        }
    
    def _assess_completion_status(conversation: Dict[str, Any]) -> str:
        """Assess if conversation goals appear to be completed."""
        messages = conversation.get("message_history", [])
        if not messages:
            return "No activity"
        
        last_message = messages[-1]
        if "thank" in last_message.get("content", "").lower():
            return "Likely completed"
        elif "help" in last_message.get("content", "").lower():
            return "Ongoing assistance"
        else:
            return "In progress"
    
    return [
        switch_ai_model,
        get_conversation_context,
        update_user_preferences,
        get_system_status,
        create_conversation_summary,
        broadcast_to_widgets
    ]
