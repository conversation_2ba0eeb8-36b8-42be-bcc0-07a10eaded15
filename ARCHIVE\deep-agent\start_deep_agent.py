#!/usr/bin/env python3
"""
Chad GPT Deep Agent Startup Script
Initializes and starts the complete Deep Agent system.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# Add the deep-agent directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def check_requirements():
    """Check if all required dependencies are installed."""
    print("🔍 Checking requirements...")
    
    required_packages = [
        "fastapi", "uvicorn", "langchain", "langgraph", 
        "aiohttp", "aiofiles", "websockets", "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ All packages installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False
    else:
        print("✅ All required packages are installed!")
    
    return True

def setup_environment():
    """Setup environment variables and configuration."""
    print("🔧 Setting up environment...")

    # Load environment variables from current directory and parent directory
    load_dotenv()
    load_dotenv(Path("../.env"))  # Check parent directory for .env

    # Also try loading from the project root
    project_root = Path(__file__).parent.parent
    load_dotenv(project_root / ".env")

    # Handle VITE_OPENROUTER_API_KEY -> OPENROUTER_API_KEY mapping
    vite_api_key = os.getenv("VITE_OPENROUTER_API_KEY")
    if vite_api_key and not os.getenv("OPENROUTER_API_KEY"):
        os.environ["OPENROUTER_API_KEY"] = vite_api_key
        print("✅ Found VITE_OPENROUTER_API_KEY, mapped to OPENROUTER_API_KEY")

    # Set default model to z-ai/glm-4.5
    if not os.getenv("DEFAULT_MODEL"):
        os.environ["DEFAULT_MODEL"] = "z-ai/glm-4.5"
        print("✅ Set default model to z-ai/glm-4.5")

    # Check for required environment variables
    required_env_vars = [
        "OPENROUTER_API_KEY"
    ]

    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("📝 Please set these in your .env file or environment")
        
        # Create .env template if it doesn't exist
        env_file = Path(".env")
        if not env_file.exists():
            print("📄 Creating .env template...")
            with open(env_file, "w") as f:
                f.write("""# Chad GPT Deep Agent Environment Configuration

# OpenRouter API Key (Required)
# Note: VITE_OPENROUTER_API_KEY will be automatically mapped to OPENROUTER_API_KEY
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Moralis API Key (Optional - for blockchain data)
MORALIS_API_KEY=your_moralis_api_key_here

# Model Configuration - Using z-ai/glm-4.5 as default
DEFAULT_MODEL=z-ai/glm-4.5
TEMPERATURE=0.7
MAX_TOKENS=4000

# Server Configuration
HOST=0.0.0.0
PORT=8001
DEBUG=false

# Workspace Configuration
WORKSPACE_ROOT=agent_workspace
CACHE_SIZE_MB=100
CACHE_AGE_HOURS=24

# Widget Configuration
WIDGET_WEBSOCKET_PORT=8002
AUTO_REFRESH_INTERVAL=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=deep_agent.log
""")
            print("✅ Created .env template file")
            print("📝 Please edit .env with your actual API keys")
    else:
        print("✅ Environment configuration is complete!")
    
    return len(missing_vars) == 0

def create_workspace():
    """Create workspace directories."""
    print("📁 Creating workspace directories...")
    
    workspace_root = Path(os.getenv("WORKSPACE_ROOT", "agent_workspace"))
    
    directories = [
        workspace_root,
        workspace_root / "analysis" / "crypto",
        workspace_root / "analysis" / "research",
        workspace_root / "analysis" / "development",
        workspace_root / "analysis" / "support",
        workspace_root / "conversations",
        workspace_root / "workflows",
        workspace_root / "reports",
        workspace_root / "summaries",
        workspace_root / "widgets",
        workspace_root / "integrations",
        workspace_root / "temp",
        workspace_root / "cache"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ Workspace created at {workspace_root}")
    return True

def start_deep_agent():
    """Start the Deep Agent system."""
    print("🚀 Starting Chad GPT Deep Agent System...")
    
    try:
        # Import and run the main application
        from main import app
        import uvicorn
        
        # Get configuration from environment
        host = os.getenv("HOST", "0.0.0.0")
        port = int(os.getenv("PORT", "8003"))  # Use port 8003 to avoid conflict
        debug = os.getenv("DEBUG", "false").lower() == "true"
        
        print(f"🌐 Starting server on {host}:{port}")
        print(f"🔧 Debug mode: {debug}")
        print(f"📊 Model: {os.getenv('DEFAULT_MODEL', 'z-ai/glm-4.5')}")
        print(f"🔑 API Key: {'✅ Found' if os.getenv('OPENROUTER_API_KEY') else '❌ Missing'}")
        print("")
        print("🎯 Deep Agent Features:")
        print("   • Unified Platform Agent with 4-pillar architecture")
        print("   • Domain-specific agents (Crypto, Research, Development, Support)")
        print("   • Advanced widget management with real-time updates")
        print("   • Comprehensive conversation and context management")
        print("   • Workflow orchestration and planning capabilities")
        print("   • Enhanced file system with caching and organization")
        print("   • Sequential thinking and reasoning tools")
        print("   • Moralis blockchain data integration")
        print("")
        print("🔗 API Endpoints:")
        print(f"   • Main Chat: http://{host}:{port}/api/chat")
        print(f"   • Health Check: http://{host}:{port}/api/health")
        print(f"   • Agent Status: http://{host}:{port}/api/agent/status")
        print(f"   • API Docs: http://{host}:{port}/docs")
        print("")
        print("🎮 Widget WebSocket: ws://localhost:8004")
        print("")
        print("✨ Chad GPT Deep Agent is now running!")
        print("🔄 The frontend will automatically connect to this endpoint")
        print("")
        
        # Start the server
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=debug,
            log_level="info" if not debug else "debug"
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down Deep Agent system...")
        print("✅ Deep Agent stopped successfully!")
    except Exception as e:
        print(f"❌ Failed to start Deep Agent: {e}")
        return False
    
    return True

def main():
    """Main startup function."""
    print("=" * 60)
    print("🤖 Chad GPT Deep Agent System Startup")
    print("=" * 60)
    print("")
    
    # Step 1: Check requirements
    if not check_requirements():
        print("❌ Requirements check failed!")
        sys.exit(1)
    
    print("")
    
    # Step 2: Setup environment
    env_ok = setup_environment()
    if not env_ok:
        print("⚠️  Environment setup incomplete - some features may not work")
        print("🔄 You can continue, but please configure missing variables")
        
        response = input("\n❓ Continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("🛑 Startup cancelled")
            sys.exit(1)
    
    print("")
    
    # Step 3: Create workspace
    if not create_workspace():
        print("❌ Workspace creation failed!")
        sys.exit(1)
    
    print("")
    
    # Step 4: Start Deep Agent
    if not start_deep_agent():
        print("❌ Deep Agent startup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
