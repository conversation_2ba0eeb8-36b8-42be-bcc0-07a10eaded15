{"name": "chad-gpt", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "agent": "cd langgraph-agent && python start.py", "agent:test": "cd langgraph-agent && python test_agent.py"}, "dependencies": {"@phantom/browser-sdk": "^0.2.2", "@phantom/react-sdk": "^0.2.2", "@solana/web3.js": "^1.98.4", "@tailwindcss/typography": "^0.5.16", "clsx": "^2.1.0", "cors": "^2.8.5", "express": "^5.1.0", "langchain": "^0.1.21", "lucide-react": "^0.344.0", "node-fetch": "^3.3.2", "openai": "^4.28.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.1", "recharts": "^2.12.2", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.4"}}