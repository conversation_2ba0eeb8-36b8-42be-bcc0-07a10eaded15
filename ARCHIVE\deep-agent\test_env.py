#!/usr/bin/env python3
"""
Test Environment Configuration for Deep Agent
Quick script to verify environment setup before starting the system.
"""

import os
from dotenv import load_dotenv
from pathlib import Path

def test_environment():
    """Test environment configuration."""
    print("🧪 Testing Deep Agent Environment Configuration")
    print("=" * 50)
    
    # Load environment variables
    print("📁 Loading environment variables...")
    load_dotenv()
    load_dotenv(Path("../.env"))  # Check parent directory
    
    # Handle VITE_OPENROUTER_API_KEY -> OPENROUTER_API_KEY mapping
    vite_api_key = os.getenv("VITE_OPENROUTER_API_KEY")
    if vite_api_key and not os.getenv("OPENROUTER_API_KEY"):
        os.environ["OPENROUTER_API_KEY"] = vite_api_key
        print("✅ Mapped VITE_OPENROUTER_API_KEY to OPENROUTER_API_KEY")
    
    # Set default model
    if not os.getenv("DEFAULT_MODEL"):
        os.environ["DEFAULT_MODEL"] = "z-ai/glm-4.5"
    
    print("\n🔍 Environment Variables:")
    print("-" * 30)
    
    # Check API Key
    api_key = os.getenv("OPENROUTER_API_KEY")
    if api_key:
        print(f"✅ OPENROUTER_API_KEY: Found ({api_key[:10]}...)")
    else:
        print("❌ OPENROUTER_API_KEY: Missing")
    
    # Check VITE API Key
    vite_key = os.getenv("VITE_OPENROUTER_API_KEY")
    if vite_key:
        print(f"✅ VITE_OPENROUTER_API_KEY: Found ({vite_key[:10]}...)")
    else:
        print("❌ VITE_OPENROUTER_API_KEY: Missing")
    
    # Check Model
    model = os.getenv("DEFAULT_MODEL", "z-ai/glm-4.5")
    print(f"✅ DEFAULT_MODEL: {model}")
    
    # Check other settings
    print(f"✅ TEMPERATURE: {os.getenv('TEMPERATURE', '0.7')}")
    print(f"✅ MAX_TOKENS: {os.getenv('MAX_TOKENS', '4000')}")
    print(f"✅ HOST: {os.getenv('HOST', '0.0.0.0')}")
    print(f"✅ PORT: {os.getenv('PORT', '8001')}")
    
    # Check Moralis (optional)
    moralis_key = os.getenv("MORALIS_API_KEY")
    if moralis_key:
        print(f"✅ MORALIS_API_KEY: Found ({moralis_key[:10]}...)")
    else:
        print("⚠️  MORALIS_API_KEY: Missing (optional)")
    
    print("\n🎯 Configuration Summary:")
    print("-" * 30)
    
    if api_key or vite_key:
        print("✅ API Key: Ready")
    else:
        print("❌ API Key: Missing - Deep Agent will not work")
        return False
    
    print(f"✅ Model: {model}")
    print("✅ Server: Ready to start")
    
    print("\n🚀 Environment Status: READY")
    print("✨ You can now start the Deep Agent with: python start_deep_agent.py")
    
    return True

if __name__ == "__main__":
    success = test_environment()
    if not success:
        print("\n❌ Environment setup failed!")
        print("📝 Please check your .env file and ensure OPENROUTER_API_KEY is set")
        exit(1)
    else:
        print("\n✅ Environment setup successful!")
        exit(0)
