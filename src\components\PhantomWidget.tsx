import { useState, useEffect } from 'react';
import { <PERSON>et, ArrowLeft, Send, QrCode, Copy, Clock, ArrowDownUp, Plus, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';
import clsx from 'clsx';
import { Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';

type Tab = 'wallet' | 'activity' | 'settings';

interface WalletState {
  isConnected: boolean;
  address: string | null;
  balance: number;
  isLoading: boolean;
  error: string | null;
}

interface TransactionHistory {
  signature: string;
  slot: number;
  blockTime: number | null;
  confirmationStatus: string | null;
  err: any;
  memo: string | null;
}

interface ParsedTransaction {
  signature: string;
  blockTime: number | null;
  slot: number;
  transaction: any; // Use any for now to avoid complex type issues
  meta: any;
}

// Solana connection for balance fetching and transaction history
// Using Helius RPC for better reliability and higher rate limits
const connection = new Connection('https://mainnet.helius-rpc.com/?api-key=demo', 'confirmed');

// Phantom wallet interface
interface PhantomWallet {
  isPhantom: boolean;
  connect(): Promise<{ publicKey: PublicKey }>;
  disconnect(): Promise<void>;
  signMessage(message: Uint8Array): Promise<{ signature: Uint8Array }>;
  signTransaction(transaction: any): Promise<any>;
  publicKey: PublicKey | null;
  isConnected: boolean;
  // Event listeners (optional, not all versions support these)
  on?: (event: string, callback: (...args: any[]) => void) => void;
  off?: (event: string, callback: (...args: any[]) => void) => void;
}

declare global {
  interface Window {
    phantom?: {
      solana?: PhantomWallet;
    };
  }
}

// Transaction item component
function TransactionItem({ transaction, walletAddress }: { transaction: ParsedTransaction; walletAddress: string }) {
  const getTransactionType = () => {
    if (!transaction.transaction?.message?.instructions) return 'Unknown';

    const instruction = transaction.transaction.message.instructions[0];
    if (instruction?.parsed?.type) {
      return instruction.parsed.type;
    }

    // Check if it's a transfer by looking at the program ID
    const systemProgramId = '********************************';
    if (instruction?.programId?.toString() === systemProgramId) {
      return 'transfer';
    }

    return 'Unknown';
  };

  const getTransactionAmount = () => {
    if (!transaction.transaction?.message?.instructions) return null;

    const instruction = transaction.transaction.message.instructions[0];
    if (instruction?.parsed?.info?.lamports) {
      return instruction.parsed.info.lamports / LAMPORTS_PER_SOL;
    }

    return null;
  };

  const getTransactionDirection = () => {
    if (!transaction.transaction?.message?.instructions) return 'unknown';

    const instruction = transaction.transaction.message.instructions[0];
    if (instruction?.parsed?.info) {
      const { source, destination } = instruction.parsed.info;
      if (source === walletAddress) return 'sent';
      if (destination === walletAddress) return 'received';
    }

    return 'unknown';
  };

  const formatDate = (timestamp: number | null) => {
    if (!timestamp) return 'Unknown time';
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const type = getTransactionType();
  const amount = getTransactionAmount();
  const direction = getTransactionDirection();
  const isSuccess = !transaction.meta?.err;

  return (
    <div className="bg-[#0A0A0A] rounded-xl p-4 hover:bg-[#111] transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={clsx(
            "w-10 h-10 rounded-xl flex items-center justify-center",
            isSuccess ? "bg-[#22c55e]/10" : "bg-red-500/10"
          )}>
            {direction === 'sent' ? (
              <ArrowDownUp className={clsx("w-5 h-5", isSuccess ? "text-[#22c55e]" : "text-red-500")} />
            ) : direction === 'received' ? (
              <ArrowDownUp className={clsx("w-5 h-5 rotate-180", isSuccess ? "text-[#22c55e]" : "text-red-500")} />
            ) : (
              <Clock className={clsx("w-5 h-5", isSuccess ? "text-[#22c55e]" : "text-red-500")} />
            )}
          </div>

          <div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium capitalize">
                {type === 'transfer' ? (direction === 'sent' ? 'Sent' : 'Received') : type}
              </span>
              {!isSuccess && (
                <span className="text-xs bg-red-500/10 text-red-500 px-2 py-0.5 rounded-full">
                  Failed
                </span>
              )}
            </div>
            <p className="text-xs text-[#666]">
              {formatDate(transaction.blockTime)}
            </p>
          </div>
        </div>

        <div className="text-right">
          {amount && (
            <p className={clsx(
              "text-sm font-medium",
              direction === 'sent' ? "text-red-400" : "text-[#22c55e]"
            )}>
              {direction === 'sent' ? '-' : '+'}{amount.toFixed(4)} SOL
            </p>
          )}
          <button
            onClick={() => window.open(`https://solscan.io/tx/${transaction.signature}`, '_blank')}
            className="text-xs text-[#666] hover:text-[#22c55e] transition-colors"
          >
            View on Solscan
          </button>
        </div>
      </div>
    </div>
  );
}

// Internal wallet component
function WalletContent() {
  const [activeTab, setActiveTab] = useState<Tab>('wallet');
  const [transactionHistory, setTransactionHistory] = useState<ParsedTransaction[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [showReceive, setShowReceive] = useState(false);
  const [walletState, setWalletState] = useState<WalletState>({
    isConnected: false,
    address: null,
    balance: 0,
    isLoading: false,
    error: null,
  });

  // Get Phantom wallet
  const getPhantomWallet = () => {
    if (typeof window !== 'undefined' && window.phantom?.solana?.isPhantom) {
      return window.phantom.solana;
    }
    return null;
  };

  // Check if Phantom is installed
  const isPhantomInstalled = () => {
    return typeof window !== 'undefined' && window.phantom?.solana?.isPhantom;
  };

  // Check wallet connection on mount and set up event listeners
  useEffect(() => {
    const checkWalletConnection = async () => {
      const phantom = getPhantomWallet();

      if (!phantom) {
        // Wait for Phantom to load if not immediately available
        const checkForPhantom = () => {
          if (window.phantom?.solana) {
            const phantom = window.phantom.solana;
            if (phantom.isConnected && phantom.publicKey) {
              setWalletState(prev => ({
                ...prev,
                isConnected: true,
                address: phantom.publicKey!.toString(),
                isLoading: false,
                error: null,
              }));
            }
          }
        };

        // Check every 100ms for up to 3 seconds
        const interval = setInterval(checkForPhantom, 100);
        setTimeout(() => clearInterval(interval), 3000);
        return;
      }

      // Check if already connected
      if (phantom.isConnected && phantom.publicKey) {
        setWalletState(prev => ({
          ...prev,
          isConnected: true,
          address: phantom.publicKey!.toString(),
          isLoading: false,
          error: null,
        }));
      }

      // Set up event listeners for wallet changes
      const handleAccountChanged = (publicKey: PublicKey | null) => {
        if (publicKey) {
          setWalletState(prev => ({
            ...prev,
            isConnected: true,
            address: publicKey.toString(),
            error: null,
          }));
        } else {
          setWalletState(prev => ({
            ...prev,
            isConnected: false,
            address: null,
            balance: 0,
            error: null,
          }));
        }
      };

      const handleDisconnect = () => {
        setWalletState({
          isConnected: false,
          address: null,
          balance: 0,
          isLoading: false,
          error: null,
        });
      };

      // Add event listeners if phantom supports them
      if (phantom.on) {
        phantom.on('accountChanged', handleAccountChanged);
        phantom.on('disconnect', handleDisconnect);
      }

      // Cleanup function
      return () => {
        if (phantom.off) {
          phantom.off('accountChanged', handleAccountChanged);
          phantom.off('disconnect', handleDisconnect);
        }
      };
    };

    checkWalletConnection();

    // Try eager connection after a short delay
    setTimeout(() => {
      tryEagerConnection();
    }, 500);
  }, []);

  // Fetch balance when connected
  useEffect(() => {
    const fetchBalance = async () => {
      if (walletState.address) {
        try {
          const publicKey = new PublicKey(walletState.address);
          const balance = await connection.getBalance(publicKey);
          setWalletState(prev => ({ ...prev, balance: balance / LAMPORTS_PER_SOL }));
        } catch (error) {
          console.error('Failed to fetch balance:', error);
        }
      }
    };

    fetchBalance();
  }, [walletState.address]);

  const handleConnect = async () => {
    const phantom = getPhantomWallet();
    if (!phantom) {
      // Provide helpful instructions for installing Phantom
      setWalletState(prev => ({
        ...prev,
        error: 'Phantom wallet not found. Please install the Phantom wallet extension from phantom.app',
      }));

      // Open Phantom website in new tab
      window.open('https://phantom.app/', '_blank');
      return;
    }

    try {
      setWalletState(prev => ({ ...prev, isLoading: true, error: null }));

      // Request connection
      const response = await phantom.connect();

      if (response.publicKey) {
        setWalletState(prev => ({
          ...prev,
          isConnected: true,
          address: response.publicKey.toString(),
          isLoading: false,
          error: null,
        }));

        // Store connection preference
        localStorage.setItem('phantomWalletConnected', 'true');

        console.log('Phantom wallet connected:', response.publicKey.toString());

        // Fetch transaction history after successful connection
        console.log('handleConnect: About to fetch transaction history for address:', response.publicKey.toString());
        try {
          await fetchTransactionHistory(response.publicKey.toString());
          console.log('handleConnect: Transaction history fetch completed successfully');
        } catch (historyError) {
          console.error('handleConnect: Transaction history fetch failed:', historyError);
          // Don't fail the connection if transaction history fails
        }
      } else {
        throw new Error('No public key received from wallet');
      }
    } catch (error: any) {
      console.error('Connection error:', error);

      let errorMessage = 'Failed to connect to Phantom wallet';
      if (error.code === 4001) {
        errorMessage = 'Connection request was rejected by user';
      } else if (error.code === -32002) {
        errorMessage = 'Connection request is already pending. Please check your Phantom wallet.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setWalletState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  };

  const handleDisconnect = async () => {
    const phantom = getPhantomWallet();
    if (!phantom) return;

    try {
      await phantom.disconnect();

      // Clear connection preference
      localStorage.removeItem('phantomWalletConnected');

      setWalletState({
        isConnected: false,
        address: null,
        balance: 0,
        isLoading: false,
        error: null,
      });

      console.log('Phantom wallet disconnected');
    } catch (error) {
      console.error('Failed to disconnect:', error);
      setWalletState(prev => ({
        ...prev,
        error: 'Failed to disconnect wallet',
      }));
    }
  };

  // Eager connection - try to connect automatically if user previously connected
  const tryEagerConnection = async () => {
    const phantom = getPhantomWallet();
    const wasConnected = localStorage.getItem('phantomWalletConnected');

    if (!phantom || !wasConnected) return;

    try {
      // Check if wallet is already connected
      if (phantom.isConnected && phantom.publicKey) {
        setWalletState(prev => ({
          ...prev,
          isConnected: true,
          address: phantom.publicKey!.toString(),
          error: null,
        }));
        console.log('Eager connection successful');

        // Fetch transaction history after successful connection
        await fetchTransactionHistory(phantom.publicKey.toString());
      }
    } catch (error) {
      console.log('Eager connection failed:', error);
      // Don't show error for eager connection failures
    }
  };

  // Fetch transaction history for the connected wallet
  const fetchTransactionHistory = async (address: string) => {
    if (!address) {
      console.log('fetchTransactionHistory: No address provided');
      return;
    }

    console.log('fetchTransactionHistory: Starting fetch for address:', address);

    try {
      setIsLoadingHistory(true);
      const publicKey = new PublicKey(address);
      console.log('fetchTransactionHistory: Created PublicKey:', publicKey.toString());

      // Try multiple RPC endpoints for better reliability
      const rpcEndpoints = [
        'https://mainnet.helius-rpc.com/?api-key=demo',
        'https://api.mainnet-beta.solana.com',
        'https://solana-api.projectserum.com',
      ];

      let signatures: any[] = [];
      let currentConnection = connection;

      for (const endpoint of rpcEndpoints) {
        try {
          console.log('fetchTransactionHistory: Trying RPC endpoint:', endpoint);
          currentConnection = new Connection(endpoint, 'confirmed');

          // Get signatures for the address (last 10 transactions)
          console.log('fetchTransactionHistory: Fetching signatures...');
          signatures = await currentConnection.getSignaturesForAddress(publicKey, { limit: 10 });
          console.log('fetchTransactionHistory: Found', signatures.length, 'signatures');
          break; // Success, exit the loop
        } catch (rpcError) {
          console.warn('fetchTransactionHistory: RPC endpoint failed:', endpoint, rpcError);
          continue; // Try next endpoint
        }
      }

      if (signatures.length === 0) {
        console.log('fetchTransactionHistory: No transactions found');
        setTransactionHistory([]);
        return;
      }

      // Get parsed transactions for these signatures
      const signatureStrings = signatures.map(sig => sig.signature);
      console.log('fetchTransactionHistory: Fetching parsed transactions for signatures:', signatureStrings);

      const transactions = await currentConnection.getParsedTransactions(signatureStrings, {
        maxSupportedTransactionVersion: 0,
      });
      console.log('fetchTransactionHistory: Received', transactions.length, 'transactions');

      // Filter out null transactions and format them
      const parsedTransactions: ParsedTransaction[] = transactions
        .map((tx, index) => {
          if (!tx) {
            console.log('fetchTransactionHistory: Null transaction at index', index);
            return null;
          }

          return {
            signature: signatures[index].signature,
            blockTime: tx.blockTime,
            slot: tx.slot,
            transaction: tx.transaction,
            meta: tx.meta,
          };
        })
        .filter((tx): tx is ParsedTransaction => tx !== null);

      setTransactionHistory(parsedTransactions);
      console.log('fetchTransactionHistory: Successfully set', parsedTransactions.length, 'parsed transactions');

    } catch (error) {
      console.error('fetchTransactionHistory: Error occurred:', error);
      setWalletState(prev => ({
        ...prev,
        error: 'Failed to load transaction history',
      }));
    } finally {
      setIsLoadingHistory(false);
      console.log('fetchTransactionHistory: Finished (loading set to false)');
    }
  };

  const handleSendTransaction = async () => {
    const phantom = getPhantomWallet();
    if (!phantom || !phantom.isConnected || !walletState.address) {
      setWalletState(prev => ({
        ...prev,
        error: 'Wallet not connected',
      }));
      return;
    }

    try {
      setWalletState(prev => ({ ...prev, isLoading: true, error: null }));

      // Create a simple transfer transaction (0.001 SOL to self as demo)
      const { Transaction, SystemProgram } = await import('@solana/web3.js');

      const fromPubkey = new PublicKey(walletState.address);
      const toPubkey = fromPubkey; // Send to self for demo
      const lamports = 1000000; // 0.001 SOL

      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey,
          toPubkey,
          lamports,
        })
      );

      // Get recent blockhash
      const { blockhash } = await connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = fromPubkey;

      // Sign and send transaction
      const signedTransaction = await phantom.signTransaction(transaction);
      const signature = await connection.sendRawTransaction(signedTransaction.serialize());

      // Wait for confirmation
      await connection.confirmTransaction({
        signature,
        blockhash,
        lastValidBlockHeight: (await connection.getLatestBlockhash()).lastValidBlockHeight,
      });

      alert(`Transaction successful! Signature: ${signature.slice(0, 20)}...`);

      // Refresh balance
      const balance = await connection.getBalance(fromPubkey);
      setWalletState(prev => ({
        ...prev,
        balance: balance / LAMPORTS_PER_SOL,
        isLoading: false
      }));

    } catch (error: any) {
      console.error('Transaction failed:', error);

      let errorMessage = 'Transaction failed';
      if (error.code === 4001) {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setWalletState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
    }
  };

  const handleSignMessage = async () => {
    const phantom = getPhantomWallet();
    if (!phantom || !phantom.isConnected) {
      setWalletState(prev => ({
        ...prev,
        error: 'Wallet not connected',
      }));
      return;
    }

    try {
      const message = 'Hello from Chad GPT! This is a test message.';
      const encodedMessage = new TextEncoder().encode(message);
      const signedMessage = await phantom.signMessage(encodedMessage);

      // Show success message
      setWalletState(prev => ({
        ...prev,
        error: null,
      }));

      alert(`Message signed successfully! Signature: ${Buffer.from(signedMessage.signature).toString('hex').slice(0, 20)}...`);
    } catch (error) {
      console.error('Message signing failed:', error);
      setWalletState(prev => ({
        ...prev,
        error: 'Failed to sign message',
      }));
    }
  };

  // Show receive modal
  if (showReceive && walletState.address) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={() => setShowReceive(false)}
            className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 group-hover:translate-x-[-2px] transition-transform" />
          </button>
          <h2 className="text-lg font-semibold">Receive SOL</h2>
        </div>

        <div className="space-y-6">
          <div className="bg-[#0A0A0A] rounded-xl p-6 text-center">
            <QrCode className="w-40 h-40 mx-auto mb-4 text-[#22c55e]" />
            <p className="text-sm font-mono mb-2 break-all">{walletState.address}</p>
            <button
              onClick={() => navigator.clipboard.writeText(walletState.address!)}
              className="text-sm text-[#666] hover:text-[#22c55e] transition-all duration-200 flex items-center gap-2 justify-center"
            >
              <Copy size={14} />
              Copy Address
            </button>
          </div>

          <div className="bg-[#0A0A0A] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-4">Network</h3>
            <div className="flex items-center justify-between p-2 bg-[#111] rounded-lg">
              <div className="flex items-center gap-2">
                <img
                  src="https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So111111111********************************/logo.png"
                  alt="Solana"
                  className="w-6 h-6 rounded-full"
                />
                <span className="text-sm">Solana Mainnet</span>
              </div>
              <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full">Connected</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Not connected state
  if (!walletState.isConnected) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
        <div className="flex items-center gap-3 mb-6">
          <div className="relative">
            <img
              src="https://i.postimg.cc/mgVdkZsV/phantom.png"
              alt="Phantom"
              className="w-8 h-8 rounded-lg object-contain bg-[#222]"
            />
          </div>
          <h2 className="text-lg font-semibold">Phantom Wallet</h2>
          <span className="text-xs bg-blue-500/10 text-blue-500 px-2 py-0.5 rounded-full font-medium">REAL</span>
        </div>

        <div className="text-center py-12">
          <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-4">
            <Wallet className="w-8 h-8 text-[#666]" />
          </div>
          <h3 className="text-lg font-medium mb-2">Connect Your Wallet</h3>
          <p className="text-sm text-[#666] mb-6">
            {isPhantomInstalled()
              ? "Connect your Phantom wallet to view your balance and send transactions"
              : "Install Phantom wallet extension to get started"
            }
          </p>

          <button
            onClick={handleConnect}
            disabled={walletState.isLoading}
            className={clsx(
              "px-6 py-3 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",
              isPhantomInstalled()
                ? "bg-[#22c55e] text-black hover:bg-[#16a34a] hover:scale-105 active:scale-95"
                : "bg-purple-600 text-white hover:bg-purple-700 hover:scale-105 active:scale-95"
            )}
          >
            {walletState.isLoading
              ? 'Connecting...'
              : isPhantomInstalled()
              ? 'Connect Phantom'
              : 'Install Phantom'
            }
          </button>

          {walletState.error && (
            <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertCircle size={16} />
                {walletState.error}
              </div>
            </div>
          )}

          {!isPhantomInstalled() && (
            <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle size={16} className="text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-left">
                  <p className="text-sm text-blue-400 mb-1">Phantom wallet not detected</p>
                  <p className="text-xs text-blue-300">
                    Install the Phantom browser extension from{' '}
                    <a
                      href="https://phantom.app"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline hover:text-blue-200"
                    >
                      phantom.app
                    </a>
                  </p>
                </div>
              </div>
            </div>
          )}




        </div>
      </div>
    );
  }

  // Connected state
  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[57%] sm:max-w-[54%] mx-auto">
      <div className="flex items-center gap-3 mb-6">
        <div className="relative">
          <img
            src="https://i.postimg.cc/mgVdkZsV/phantom.png"
            alt="Phantom"
            className="w-8 h-8 rounded-lg object-contain bg-[#222]"
          />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#22c55e] rounded-full border-2 border-[#111]"></div>
        </div>
        <h2 className="text-lg font-semibold">Phantom Wallet</h2>
        <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">CONNECTED</span>
      </div>

      {/* Wallet Overview */}
      <div className="bg-[#181818] rounded-xl p-6 mb-6">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-semibold mb-1">{walletState.balance.toFixed(4)} SOL</h3>
          <p className="text-sm text-[#666] font-mono break-all">
            {walletState.address?.slice(0, 4)}...{walletState.address?.slice(-4)}
          </p>
        </div>

        <div className="flex items-center justify-center gap-4">
          <button
            onClick={() => setShowReceive(true)}
            className="flex items-center gap-2 bg-[#111] rounded-lg px-4 py-2 hover:bg-[#181818] transition-all duration-200"
          >
            <QrCode size={16} />
            <span className="text-sm">Receive</span>
          </button>
          <button
            onClick={handleSendTransaction}
            disabled={walletState.isLoading}
            className="flex items-center gap-2 bg-[#111] rounded-lg px-4 py-2 hover:bg-[#181818] transition-all duration-200 disabled:opacity-50"
          >
            <Send size={16} />
            <span className="text-sm">{walletState.isLoading ? 'Sending...' : 'Send'}</span>
          </button>
          <button
            onClick={handleSignMessage}
            className="flex items-center gap-2 bg-[#111] rounded-lg px-4 py-2 hover:bg-[#181818] transition-all duration-200"
          >
            <ArrowDownUp size={16} />
            <span className="text-sm">Sign</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-2 p-1 bg-[#0A0A0A] rounded-lg mb-6">
        <button
          onClick={() => setActiveTab('wallet')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'wallet' ? "bg-[#22c55e] text-black" : "text-[#666] hover:text-white"
          )}
        >
          <Wallet className="w-4 h-4 inline-block mr-2" />
          Wallet
        </button>
        <button
          onClick={() => setActiveTab('activity')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'activity' ? "bg-[#22c55e] text-black" : "text-[#666] hover:text-white"
          )}
        >
          <Clock className="w-4 h-4 inline-block mr-2" />
          Activity
        </button>
        <button
          onClick={() => setActiveTab('settings')}
          className={clsx(
            "flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
            activeTab === 'settings' ? "bg-[#22c55e] text-black" : "text-[#666] hover:text-white"
          )}
        >
          <Plus className="w-4 h-4 inline-block mr-2" />
          Settings
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'wallet' && (
        <div className="space-y-4">
          <div className="bg-[#0A0A0A] rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <img
                  src="https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So111111111********************************/logo.png"
                  alt="SOL"
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">SOL</span>
                    <span className="text-xs text-[#666]">Solana</span>
                  </div>
                  <div className="text-sm text-[#666]">{walletState.balance.toFixed(4)}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">Native Token</div>
                <div className="text-sm text-[#666]">Mainnet</div>
              </div>
            </div>
          </div>

          <div className="bg-[#0A0A0A] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Wallet Actions</h3>
            <div className="space-y-2">
              <button
                onClick={handleSendTransaction}
                disabled={walletState.isLoading}
                className="w-full flex items-center gap-3 p-3 bg-[#111] rounded-lg hover:bg-[#181818] transition-all duration-200 disabled:opacity-50"
              >
                <Send size={16} className="text-[#22c55e]" />
                <span className="text-sm">{walletState.isLoading ? 'Processing...' : 'Send Test Transaction'}</span>
              </button>
              <button
                onClick={handleSignMessage}
                className="w-full flex items-center gap-3 p-3 bg-[#111] rounded-lg hover:bg-[#181818] transition-all duration-200"
              >
                <ArrowDownUp size={16} className="text-blue-500" />
                <span className="text-sm">Sign Message</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'activity' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Recent Activity</h3>
            <button
              onClick={() => {
                console.log('Manual refresh clicked for address:', walletState.address);
                fetchTransactionHistory(walletState.address!);
              }}
              disabled={isLoadingHistory || !walletState.address}
              className="text-sm text-[#22c55e] hover:text-[#16a34a] disabled:opacity-50"
              title="Refresh transaction history"
            >
              {isLoadingHistory ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          {isLoadingHistory ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-2 border-[#22c55e] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-sm text-[#666]">Loading transaction history...</p>
            </div>
          ) : transactionHistory.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-[#0A0A0A] rounded-xl flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-[#666]" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Activity Yet</h3>
              <p className="text-sm text-[#666] mb-6">
                Your transaction history will appear here
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {transactionHistory.map((tx) => (
                <TransactionItem key={tx.signature} transaction={tx} walletAddress={walletState.address!} />
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'settings' && (
        <div className="space-y-4">
          <div className="bg-[#0A0A0A] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Wallet Information</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-[#666]">Address</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-mono">
                    {walletState.address?.slice(0, 4)}...{walletState.address?.slice(-4)}
                  </span>
                  <button
                    onClick={() => navigator.clipboard.writeText(walletState.address!)}
                    className="p-1 hover:bg-[#222] rounded transition-colors"
                  >
                    <Copy size={14} />
                  </button>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-[#666]">Network</span>
                <span className="text-sm">Solana Mainnet</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-[#666]">Balance</span>
                <span className="text-sm">{walletState.balance.toFixed(4)} SOL</span>
              </div>
            </div>
          </div>

          <div className="bg-[#0A0A0A] rounded-xl p-4">
            <h3 className="text-sm font-medium mb-3">Actions</h3>
            <button
              onClick={handleDisconnect}
              className="w-full flex items-center gap-3 p-3 bg-red-500/10 text-red-500 rounded-lg hover:bg-red-500/20 transition-all duration-200"
            >
              <ExternalLink size={16} />
              <span className="text-sm">Disconnect Wallet</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// Main export component
export function PhantomWidget() {
  return <WalletContent />;
}