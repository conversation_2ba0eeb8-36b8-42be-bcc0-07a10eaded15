"""
Enhanced Sequential Thinking Tools - Integrates existing sequential thinking with Deep Agent reasoning.
Maintains backward compatibility while adding advanced reasoning capabilities.
"""

from langchain_core.tools import tool
from typing import Dict, Any, List, Optional, Union
import json
import uuid
from datetime import datetime


def get_sequential_thinking_tools():
    """Get enhanced sequential thinking tools with Deep Agent integration."""
    
    # Global thinking session storage
    thinking_sessions = {}
    
    @tool
    def start_thinking_session(
        problem_description: str,
        thinking_approach: str = "analytical",
        estimated_thoughts: int = 5
    ) -> Dict[str, Any]:
        """
        Start a new sequential thinking session for complex problem solving.
        
        Args:
            problem_description: Clear description of the problem to solve
            thinking_approach: Approach to use (analytical, creative, systematic, exploratory)
            estimated_thoughts: Initial estimate of thoughts needed
            
        Returns:
            Session information and first thinking step
        """
        session_id = str(uuid.uuid4())[:8]
        
        session = {
            "session_id": session_id,
            "problem_description": problem_description,
            "thinking_approach": thinking_approach,
            "estimated_thoughts": estimated_thoughts,
            "current_thought": 0,
            "thoughts": [],
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "deep_agent_enhanced": True
        }
        
        thinking_sessions[session_id] = session
        
        return {
            "success": True,
            "session_id": session_id,
            "session": session,
            "message": f"Started thinking session for: {problem_description}",
            "next_action": "Use add_thought to begin reasoning process",
            "function_call": {
                "name": "start_thinking_session",
                "arguments": {"problem_description": problem_description}
            }
        }
    
    @tool
    def add_thought(
        session_id: str,
        thought_content: str,
        thought_type: str = "analysis",
        builds_on_thought: Optional[int] = None,
        confidence_level: float = 0.7
    ) -> Dict[str, Any]:
        """
        Add a thought to an active thinking session.
        
        Args:
            session_id: ID of the thinking session
            thought_content: The actual thought or reasoning step
            thought_type: Type of thought (analysis, hypothesis, question, insight, conclusion)
            builds_on_thought: Number of previous thought this builds on
            confidence_level: Confidence in this thought (0.0 to 1.0)
            
        Returns:
            Updated session state and thinking progress
        """
        if session_id not in thinking_sessions:
            return {
                "success": False,
                "error": "Session not found",
                "message": f"Thinking session {session_id} does not exist"
            }
        
        session = thinking_sessions[session_id]
        
        if session["status"] != "active":
            return {
                "success": False,
                "error": "Session not active",
                "message": f"Session {session_id} is {session['status']}"
            }
        
        thought_number = len(session["thoughts"]) + 1
        
        thought = {
            "thought_number": thought_number,
            "content": thought_content,
            "type": thought_type,
            "builds_on_thought": builds_on_thought,
            "confidence_level": confidence_level,
            "timestamp": datetime.now().isoformat(),
            "insights_generated": [],
            "questions_raised": []
        }
        
        # Analyze thought for insights and questions
        if "?" in thought_content:
            thought["questions_raised"] = [q.strip() for q in thought_content.split("?") if q.strip()]
        
        if any(keyword in thought_content.lower() for keyword in ["insight", "realize", "understand", "conclude"]):
            thought["insights_generated"].append(f"Insight from thought {thought_number}")
        
        session["thoughts"].append(thought)
        session["current_thought"] = thought_number
        session["last_updated"] = datetime.now().isoformat()
        
        # Determine if more thoughts are needed
        progress_ratio = thought_number / session["estimated_thoughts"]
        needs_more_thoughts = progress_ratio < 1.0 or thought_type not in ["conclusion", "solution"]
        
        return {
            "success": True,
            "session_id": session_id,
            "thought_added": thought,
            "progress": {
                "current_thought": thought_number,
                "estimated_thoughts": session["estimated_thoughts"],
                "progress_percentage": min(progress_ratio * 100, 100),
                "needs_more_thoughts": needs_more_thoughts
            },
            "session_status": session["status"],
            "message": f"Added thought {thought_number}: {thought_content[:50]}...",
            "function_call": {
                "name": "add_thought",
                "arguments": {"session_id": session_id, "thought_number": thought_number}
            }
        }
    
    @tool
    def revise_thought(
        session_id: str,
        thought_number: int,
        revised_content: str,
        revision_reason: str
    ) -> Dict[str, Any]:
        """
        Revise a previous thought in the thinking session.
        
        Args:
            session_id: ID of the thinking session
            thought_number: Number of the thought to revise
            revised_content: New content for the thought
            revision_reason: Reason for the revision
            
        Returns:
            Updated session with revised thought
        """
        if session_id not in thinking_sessions:
            return {
                "success": False,
                "error": "Session not found",
                "message": f"Thinking session {session_id} does not exist"
            }
        
        session = thinking_sessions[session_id]
        
        if thought_number < 1 or thought_number > len(session["thoughts"]):
            return {
                "success": False,
                "error": "Invalid thought number",
                "message": f"Thought {thought_number} does not exist"
            }
        
        original_thought = session["thoughts"][thought_number - 1]
        
        # Create revision record
        revision = {
            "original_content": original_thought["content"],
            "revised_content": revised_content,
            "revision_reason": revision_reason,
            "revised_at": datetime.now().isoformat()
        }
        
        # Update the thought
        session["thoughts"][thought_number - 1]["content"] = revised_content
        session["thoughts"][thought_number - 1]["revisions"] = session["thoughts"][thought_number - 1].get("revisions", [])
        session["thoughts"][thought_number - 1]["revisions"].append(revision)
        session["last_updated"] = datetime.now().isoformat()
        
        return {
            "success": True,
            "session_id": session_id,
            "thought_revised": thought_number,
            "revision": revision,
            "message": f"Revised thought {thought_number}: {revision_reason}",
            "function_call": {
                "name": "revise_thought",
                "arguments": {"session_id": session_id, "thought_number": thought_number}
            }
        }
    
    @tool
    def branch_thinking(
        session_id: str,
        from_thought: int,
        branch_description: str,
        branch_approach: str = "alternative"
    ) -> Dict[str, Any]:
        """
        Create a thinking branch to explore alternative reasoning paths.
        
        Args:
            session_id: ID of the thinking session
            from_thought: Thought number to branch from
            branch_description: Description of the alternative path
            branch_approach: Type of branch (alternative, deeper_analysis, what_if)
            
        Returns:
            New branch information and session update
        """
        if session_id not in thinking_sessions:
            return {
                "success": False,
                "error": "Session not found",
                "message": f"Thinking session {session_id} does not exist"
            }
        
        session = thinking_sessions[session_id]
        
        if from_thought < 1 or from_thought > len(session["thoughts"]):
            return {
                "success": False,
                "error": "Invalid thought number",
                "message": f"Cannot branch from thought {from_thought}"
            }
        
        branch_id = str(uuid.uuid4())[:6]
        
        branch = {
            "branch_id": branch_id,
            "from_thought": from_thought,
            "description": branch_description,
            "approach": branch_approach,
            "created_at": datetime.now().isoformat(),
            "thoughts": [],
            "status": "active"
        }
        
        # Initialize branches if not exists
        if "branches" not in session:
            session["branches"] = {}
        
        session["branches"][branch_id] = branch
        session["active_branch"] = branch_id
        session["last_updated"] = datetime.now().isoformat()
        
        return {
            "success": True,
            "session_id": session_id,
            "branch_id": branch_id,
            "branch": branch,
            "message": f"Created thinking branch: {branch_description}",
            "next_action": "Add thoughts to this branch using add_branch_thought",
            "function_call": {
                "name": "branch_thinking",
                "arguments": {"session_id": session_id, "from_thought": from_thought}
            }
        }
    
    @tool
    def add_branch_thought(
        session_id: str,
        branch_id: str,
        thought_content: str,
        thought_type: str = "analysis"
    ) -> Dict[str, Any]:
        """
        Add a thought to a specific thinking branch.
        
        Args:
            session_id: ID of the thinking session
            branch_id: ID of the branch to add thought to
            thought_content: The thought content
            thought_type: Type of thought
            
        Returns:
            Updated branch information
        """
        if session_id not in thinking_sessions:
            return {
                "success": False,
                "error": "Session not found",
                "message": f"Thinking session {session_id} does not exist"
            }
        
        session = thinking_sessions[session_id]
        
        if "branches" not in session or branch_id not in session["branches"]:
            return {
                "success": False,
                "error": "Branch not found",
                "message": f"Branch {branch_id} does not exist"
            }
        
        branch = session["branches"][branch_id]
        thought_number = len(branch["thoughts"]) + 1
        
        thought = {
            "thought_number": thought_number,
            "content": thought_content,
            "type": thought_type,
            "timestamp": datetime.now().isoformat(),
            "branch_id": branch_id
        }
        
        branch["thoughts"].append(thought)
        branch["last_updated"] = datetime.now().isoformat()
        session["last_updated"] = datetime.now().isoformat()
        
        return {
            "success": True,
            "session_id": session_id,
            "branch_id": branch_id,
            "thought_added": thought,
            "branch_progress": len(branch["thoughts"]),
            "message": f"Added thought to branch {branch_id}: {thought_content[:50]}...",
            "function_call": {
                "name": "add_branch_thought",
                "arguments": {"session_id": session_id, "branch_id": branch_id}
            }
        }
    
    @tool
    def conclude_thinking(
        session_id: str,
        conclusion: str,
        confidence_level: float = 0.8,
        key_insights: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Conclude a thinking session with final insights and solution.
        
        Args:
            session_id: ID of the thinking session
            conclusion: Final conclusion or solution
            confidence_level: Confidence in the conclusion (0.0 to 1.0)
            key_insights: List of key insights discovered
            
        Returns:
            Complete thinking session summary and analysis
        """
        if session_id not in thinking_sessions:
            return {
                "success": False,
                "error": "Session not found",
                "message": f"Thinking session {session_id} does not exist"
            }
        
        session = thinking_sessions[session_id]
        
        # Create conclusion record
        conclusion_record = {
            "conclusion": conclusion,
            "confidence_level": confidence_level,
            "key_insights": key_insights or [],
            "concluded_at": datetime.now().isoformat(),
            "total_thoughts": len(session["thoughts"]),
            "thinking_duration": _calculate_thinking_duration(session),
            "branches_explored": len(session.get("branches", {}))
        }
        
        session["conclusion"] = conclusion_record
        session["status"] = "completed"
        session["completed_at"] = datetime.now().isoformat()
        
        # Generate thinking summary
        summary = _generate_thinking_summary(session)
        
        return {
            "success": True,
            "session_id": session_id,
            "conclusion": conclusion_record,
            "thinking_summary": summary,
            "session_complete": True,
            "message": f"Thinking session concluded: {conclusion[:100]}...",
            "function_call": {
                "name": "conclude_thinking",
                "arguments": {"session_id": session_id}
            }
        }
    
    @tool
    def get_thinking_session(session_id: str) -> Dict[str, Any]:
        """
        Get complete information about a thinking session.
        
        Args:
            session_id: ID of the thinking session
            
        Returns:
            Complete session data and analysis
        """
        if session_id not in thinking_sessions:
            return {
                "success": False,
                "error": "Session not found",
                "message": f"Thinking session {session_id} does not exist"
            }
        
        session = thinking_sessions[session_id]
        
        # Generate session analysis
        analysis = {
            "total_thoughts": len(session["thoughts"]),
            "thought_types": _analyze_thought_types(session["thoughts"]),
            "confidence_trend": _analyze_confidence_trend(session["thoughts"]),
            "thinking_depth": _assess_thinking_depth(session),
            "branch_count": len(session.get("branches", {})),
            "session_duration": _calculate_thinking_duration(session)
        }
        
        return {
            "success": True,
            "session_id": session_id,
            "session": session,
            "analysis": analysis,
            "message": f"Retrieved thinking session {session_id}",
            "function_call": {
                "name": "get_thinking_session",
                "arguments": {"session_id": session_id}
            }
        }
    
    # Helper functions
    def _calculate_thinking_duration(session: Dict[str, Any]) -> str:
        """Calculate thinking session duration."""
        if "completed_at" in session:
            # Calculate actual duration
            return "Session completed"
        else:
            return "Session in progress"
    
    def _generate_thinking_summary(session: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of the thinking session."""
        return {
            "problem_solved": session["problem_description"],
            "approach_used": session["thinking_approach"],
            "total_thoughts": len(session["thoughts"]),
            "key_thought_types": _analyze_thought_types(session["thoughts"]),
            "branches_explored": len(session.get("branches", {})),
            "final_confidence": session.get("conclusion", {}).get("confidence_level", 0),
            "thinking_quality": "high" if len(session["thoughts"]) >= 5 else "moderate"
        }
    
    def _analyze_thought_types(thoughts: List[Dict[str, Any]]) -> Dict[str, int]:
        """Analyze distribution of thought types."""
        type_counts = {}
        for thought in thoughts:
            thought_type = thought.get("type", "analysis")
            type_counts[thought_type] = type_counts.get(thought_type, 0) + 1
        return type_counts
    
    def _analyze_confidence_trend(thoughts: List[Dict[str, Any]]) -> str:
        """Analyze confidence trend across thoughts."""
        if len(thoughts) < 2:
            return "insufficient_data"
        
        confidences = [t.get("confidence_level", 0.5) for t in thoughts]
        if confidences[-1] > confidences[0]:
            return "increasing"
        elif confidences[-1] < confidences[0]:
            return "decreasing"
        else:
            return "stable"
    
    def _assess_thinking_depth(session: Dict[str, Any]) -> str:
        """Assess the depth of thinking in the session."""
        thought_count = len(session["thoughts"])
        branch_count = len(session.get("branches", {}))
        revision_count = sum(len(t.get("revisions", [])) for t in session["thoughts"])
        
        depth_score = thought_count + (branch_count * 2) + (revision_count * 1.5)
        
        if depth_score >= 15:
            return "deep"
        elif depth_score >= 8:
            return "moderate"
        else:
            return "shallow"
    
    return [
        start_thinking_session,
        add_thought,
        revise_thought,
        branch_thinking,
        add_branch_thought,
        conclude_thinking,
        get_thinking_session
    ]
