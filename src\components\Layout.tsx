import { Outlet } from 'react-router-dom';
import { Home, Users, AlertCircle } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';
import { SettingsModal } from './SettingsModal';
import { ChatHistoryBar } from './ChatHistoryBar';

import { useMessages } from '../contexts/MessagesContext';
import { useAlert } from '../contexts/AlertContext';
import clsx from 'clsx';
import type { Message } from '../types';

export default function Layout() {
  const { setMessages, setActiveWidgetId, getChatThreads, setCurrentThreadId, currentThreadId } = useMessages();
  const { showAlert } = useAlert();

  const location = useLocation();

  // Helper function to create widgets properly
  const createWidget = (widgetType: string) => {
    console.log(`📱 Layout: Creating ${widgetType} widget`);

    // Get or create thread ID
    let threadId = currentThreadId;
    if (!threadId) {
      threadId = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      console.log(`📱 Layout: Created new thread ID: ${threadId}`);
      setCurrentThreadId(threadId);
    }

    const widgetMessage: Message = {
      id: `widget_${widgetType}_layout_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: widgetType,
      type: 'widget',
      timestamp: new Date(),
      threadId: threadId,
      widgetData: {}
    };

    console.log(`📱 Layout: Created widget message:`, widgetMessage);

    setMessages(prev => [...prev, widgetMessage]);
    setActiveWidgetId(widgetMessage.id);

    console.log(`📱 Layout: Widget ${widgetType} created successfully`);
  };

  return (
    <div className="flex h-screen bg-black text-white font-sans antialiased">
      {/* Chat History Bar - Slides out to cover sidebar */}
      <ChatHistoryBar
        currentThreadId={currentThreadId}
        onThreadSelect={(threadId) => {
          console.log('🔄 Layout: Switching to thread:', threadId);
          setCurrentThreadId(threadId);
          setActiveWidgetId(null);
        }}
        onNewChat={() => {
          console.log('🆕 Layout: New Chat clicked - starting fresh conversation');
          const newThreadId = `thread_${Date.now()}`;
          console.log('🔄 Layout: Generated new thread ID:', newThreadId);
          setCurrentThreadId(newThreadId);
          setActiveWidgetId(null);
        }}
        getChatThreads={getChatThreads}
      />

      {/* Sidebar */}
      <div className="w-80 bg-[#0A0A0A] p-6 flex flex-col border-r border-[#181818] sticky top-0 h-screen overflow-y-auto transition-all duration-300 animate-slide-in">
          <div className="flex items-center gap-2 mb-10">
            <div className="flex items-center gap-2">
              <img 
                src="https://pump.mypinata.cloud/ipfs/QmY2HUM8HMPytN2cJDr9XxpGDzoCmp9WNszFpeB8N15NR9?img-width=800&img-dpr=2&img-onerror=redirect"
                alt="Chad GPT"
                className="w-8 h-8 rounded-lg object-cover shadow-sm shadow-black/20 ring-1 ring-[#181818] hover:ring-[#22c55e] transition-colors"
              />
              <div>
                <div className="font-semibold tracking-tight text-sm">Chad GPT</div>
                <div className="flex items-center gap-1.5">
                  <span className="text-[11px] bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">BETA</span>
                  <span className="text-[11px] text-[#666] font-medium">v0.1.4</span>
                </div>
              </div>
            </div>
            <div className="ml-auto">
              <SettingsModal />
            </div>
          </div>

          {/* Apps Grid */}
          <div className="mb-8">
            <h2 className="text-xs text-[#666] mb-4 font-medium tracking-wide uppercase flex items-center gap-2 pl-1">
              <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
              Apps
            </h2>
            <div className="grid grid-cols-4 gap-2 animate-fade-in mb-4">
              <button
                onClick={() => {
                  console.log('📱 Layout: External Wallet button clicked!');
                  createWidget('external_wallet');
                }}
                className="p-3 rounded-lg bg-[#111] hover:bg-[#181818] transition-all duration-200 hover:shadow-lg hover:shadow-black/10 group hover:scale-105 active:scale-95"
              >
                <div className="w-full h-full rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center transition-all group-hover:brightness-110">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
              </button>
              <button
                onClick={() => {
                  console.log('📱 Layout: Phantom button clicked!');
                  createWidget('phantom');
                }}
                className="p-3 rounded-lg bg-[#111] hover:bg-[#181818] transition-all duration-200 hover:shadow-lg hover:shadow-black/10 group hover:scale-105 active:scale-95"
              >
                <img 
                  src="https://i.postimg.cc/mgVdkZsV/phantom.png" 
                  alt="Phantom" 
                  className="w-full h-full rounded-lg object-contain bg-[#222] transition-all group-hover:brightness-110"
                />
              </button>
              <button
                onClick={() => {
                  console.log('📱 Layout: Jupiter button clicked!');
                  createWidget('jupiter');
                }}
                className="p-3 rounded-lg bg-[#111] hover:bg-[#181818] transition-all duration-200 hover:shadow-lg hover:shadow-black/10 group hover:scale-105 active:scale-95"
              >
                <img 
                  src="https://jup.ag/favicon.ico" 
                  alt="Jupiter" 
                  className="w-full h-full rounded-lg transition-all group-hover:brightness-110" 
                />
              </button>
              <button
                onClick={() => {
                  console.log('📱 Layout: DexScreener button clicked!');
                  createWidget('dexscreener');
                }}
                className="p-3 rounded-lg bg-[#111] hover:bg-[#181818] transition-all duration-200 hover:shadow-lg hover:shadow-black/10 group hover:scale-105 active:scale-95"
              >
                <img 
                  src="https://dexscreener.com/favicon.ico" 
                  alt="DexScreener" 
                  className="w-full h-full rounded-lg transition-all group-hover:brightness-110" 
                />
              </button>
              <button
                onClick={() => {
                  console.log('📱 Layout: PumpFun button clicked!');
                  createWidget('pumpfun');
                }}
                className="p-3 rounded-lg bg-[#111] hover:bg-[#181818] transition-all duration-200 hover:shadow-lg hover:shadow-black/10 group hover:scale-105 active:scale-95"
              >
                <img 
                  src="https://upload.wikimedia.org/wikipedia/en/b/bd/Pump_fun_logo.png" 
                  alt="pump.fun" 
                  className="w-full h-full rounded-lg transition-all group-hover:brightness-110" 
                />
              </button>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => {
                  showAlert('success', 'This is a success message!');
                  setTimeout(() => showAlert('error', 'This is an error message!'), 1000);
                  setTimeout(() => showAlert('info', 'This is an info message!'), 2000);
                  setTimeout(() => showAlert('warning', 'This is a warning message!'), 3000);
                }}
                className="w-full bg-[#111] hover:bg-[#181818] rounded-lg p-4 text-sm text-[#666] hover:text-white transition-all duration-200 flex items-center gap-2"
              >
                <AlertCircle className="w-4 h-4" />
                <span>Test Alerts</span>
              </button>
            </div>
          </div>

          {/* View Content */}
          <div className="space-y-6">
            <div className="space-y-6">
              <h2 className="text-xs text-[#666] mb-3 font-medium tracking-wide uppercase flex items-center gap-2 pl-1">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                Navigation
              </h2>
              <nav className="space-y-2">
                <Link
                  to="/"
                  className={clsx(
                    "flex items-center gap-2.5 px-4 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 active:scale-95",
                    location.pathname === "/"
                      ? "bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10"
                      : "text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10"
                  )}
                >
                  <Home size={16} />
                  <span>Home</span>
                </Link>
                <Link
                  to="/agents"
                  className={clsx(
                    "flex items-center gap-2.5 px-4 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 active:scale-95",
                    location.pathname === "/agents"
                      ? "bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10"
                      : "text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10"
                  )}
                >
                  <Users size={16} />
                  <span>Agents</span>
                  <span className="ml-auto text-xs text-[#666]">3</span>
                </Link>
              </nav>
            </div>

            <div className="space-y-6">
              <h2 className="text-xs text-[#666] mb-3 font-medium tracking-wide uppercase flex items-center gap-2 pl-1">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                Recent Chats
              </h2>
              <div className="space-y-2">
                {(() => {
                  const threads = getChatThreads();
                  return threads.length > 0 ? (
                    threads.slice(0, 3).map((thread) => (
                      <div
                        key={thread.id}
                        className="flex items-center gap-3 px-4 py-2.5 rounded-lg hover:bg-[#111] transition-all duration-200 group hover:ring-1 hover:ring-[#22c55e] hover:shadow-lg hover:shadow-black/10 active:scale-95 cursor-pointer"
                        onClick={() => setCurrentThreadId(thread.id)}
                      >
                        <div className="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 bg-gradient-to-br from-indigo-500 to-purple-500">
                          <svg
                            viewBox="0 0 24 24"
                            fill="none"
                            className="w-4 h-4 text-white"
                            stroke="currentColor"
                            strokeWidth="2"
                          >
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                          </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate text-[#888] group-hover:text-white transition-colors">
                            {thread.title}
                          </div>
                          <div className="text-[11px] text-[#666] flex items-center gap-1.5">
                            {thread.messageCount} messages • {new Date(thread.lastActivity).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-[#666] px-4 py-2">No chat history yet</div>
                  );
                })()}
              </div>
            </div>
          </div>

          <div className="mt-auto pt-4">
            <div className="flex items-center gap-3 bg-[#111] p-3 rounded-xl hover:ring-1 hover:ring-[#22c55e] transition-all duration-200 group hover:shadow-lg hover:shadow-black/10 active:scale-95">
              <img 
                src="https://pump.mypinata.cloud/ipfs/QmY2HUM8HMPytN2cJDr9XxpGDzoCmp9WNszFpeB8N15NR9?img-width=800&img-dpr=2&img-onerror=redirect"
                alt="Chad GPT"
                className="w-10 h-10 rounded-xl object-cover shadow-sm shadow-black/20 ring-1 ring-[#181818] group-hover:ring-[#22c55e] transition-colors"
              />
              <div>
                <div className="text-sm font-medium">@chadgpt</div>
                <div className="text-[11px] text-[#666] truncate w-40 font-mono group-hover:text-[#22c55e] transition-colors cursor-copy" onClick={() => navigator.clipboard.writeText('EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA')}>EUxS8Kqvbe8zDr8bG1RYtgjVNzEhMTG6B6H3WwrCHfwA</div>
              </div>
            </div>
          </div>
        </div>

      {/* Main Content */}
      <Outlet />
    </div>
  );
}