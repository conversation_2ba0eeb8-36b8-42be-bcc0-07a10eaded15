"""
Cryptocurrency Analysis Agent for Chad GPT Deep Agents.
Specialized agent for comprehensive crypto and DeFi analysis.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from ...src.core.platform_agent_loop import PlatformAgentLoop
from ...src.core.workflow_planning import WorkflowPlanner
from .pumpfun_integration import PumpFunAnalyzer
from .defi_analyzer import DeFiAnalyzer
from .market_analyzer import MarketAnalyzer


class CryptocurrencyAgent:
    """
    Specialized agent for cryptocurrency and DeFi analysis.
    Provides comprehensive token evaluation, market analysis, and trading insights.
    """

    def __init__(self, platform_agent: PlatformAgentLoop):
        """Initialize cryptocurrency agent with platform integration."""
        self.platform_agent = platform_agent
        self.workflow_planner = WorkflowPlanner()
        self.pumpfun_analyzer = PumpFunAnalyzer()
        self.defi_analyzer = DeFiAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        
        # Analysis templates and frameworks
        self.analysis_frameworks = {
            "token_evaluation": {
                "fundamental_analysis": [
                    "tokenomics_review",
                    "team_assessment", 
                    "technology_evaluation",
                    "market_positioning"
                ],
                "technical_analysis": [
                    "price_action_analysis",
                    "volume_analysis",
                    "support_resistance_levels",
                    "trend_identification"
                ],
                "on_chain_analysis": [
                    "holder_distribution",
                    "transaction_patterns",
                    "liquidity_analysis",
                    "whale_activity"
                ],
                "risk_assessment": [
                    "smart_contract_risks",
                    "liquidity_risks",
                    "regulatory_risks",
                    "market_risks"
                ]
            },
            "defi_protocol_analysis": {
                "protocol_mechanics": [
                    "yield_generation_mechanism",
                    "tokenomics_sustainability",
                    "governance_structure",
                    "security_measures"
                ],
                "financial_metrics": [
                    "tvl_analysis",
                    "yield_calculations",
                    "fee_structure",
                    "token_emissions"
                ],
                "competitive_analysis": [
                    "market_position",
                    "competitive_advantages",
                    "differentiation_factors",
                    "market_share"
                ]
            }
        }
    
    async def analyze_token(
        self,
        token_address: str,
        chain: str = "solana",
        analysis_depth: str = "comprehensive",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive token analysis.
        
        Args:
            token_address: Contract address of the token
            chain: Blockchain network
            analysis_depth: Level of analysis (quick, standard, comprehensive)
            thread_id: Conversation thread for widget integration
            
        Returns:
            Comprehensive analysis results
        """
        try:
            # Create analysis workflow
            workflow_id = await self._create_token_analysis_workflow(
                token_address, chain, analysis_depth, thread_id
            )
            
            # Execute analysis steps
            results = {}
            
            # Step 1: Basic token information
            basic_info = await self._get_basic_token_info(token_address, chain)
            results["basic_info"] = basic_info
            
            # Step 2: Price and market data
            market_data = await self.market_analyzer.get_market_data(token_address, chain)
            results["market_data"] = market_data
            
            # Step 3: On-chain analysis
            if analysis_depth in ["standard", "comprehensive"]:
                onchain_data = await self._perform_onchain_analysis(token_address, chain)
                results["onchain_analysis"] = onchain_data
            
            # Step 4: Technical analysis
            if analysis_depth == "comprehensive":
                technical_analysis = await self._perform_technical_analysis(token_address, chain)
                results["technical_analysis"] = technical_analysis
            
            # Step 5: Risk assessment
            risk_assessment = await self._assess_risks(token_address, chain, results)
            results["risk_assessment"] = risk_assessment
            
            # Step 6: Generate recommendations
            recommendations = await self._generate_recommendations(results)
            results["recommendations"] = recommendations
            
            # Step 7: Create widgets if thread_id provided
            if thread_id:
                await self._create_analysis_widgets(token_address, chain, thread_id, results)
            
            # Save analysis to file system
            await self._save_analysis_report(token_address, results)
            
            return {
                "success": True,
                "workflow_id": workflow_id,
                "token_address": token_address,
                "chain": chain,
                "analysis": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "token_address": token_address,
                "chain": chain
            }
    
    async def analyze_pumpfun_trends(
        self,
        category: str = "new",
        limit: int = 20,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze trending tokens on Pump.fun.
        
        Args:
            category: Token category to analyze
            limit: Number of tokens to analyze
            thread_id: Conversation thread for widget integration
            
        Returns:
            Analysis of trending tokens
        """
        try:
            # Get trending tokens from PumpFun
            trending_tokens = await self.pumpfun_analyzer.get_trending_tokens(category, limit)
            
            # Analyze each token
            token_analyses = []
            for token in trending_tokens[:10]:  # Limit to top 10 for detailed analysis
                analysis = await self.analyze_token(
                    token_address=token["mint"],
                    chain="solana",
                    analysis_depth="quick"
                )
                token_analyses.append(analysis)
            
            # Identify patterns and trends
            trend_analysis = await self._analyze_market_trends(token_analyses)
            
            # Create PumpFun widget if thread_id provided
            if thread_id:
                await self._create_pumpfun_widget(category, thread_id)
            
            return {
                "success": True,
                "category": category,
                "trending_tokens": trending_tokens,
                "detailed_analyses": token_analyses,
                "trend_analysis": trend_analysis,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "category": category
            }
    
    async def portfolio_analysis(
        self,
        wallet_address: str,
        chain: str = "solana",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze a cryptocurrency portfolio.
        
        Args:
            wallet_address: Wallet address to analyze
            chain: Blockchain network
            thread_id: Conversation thread for widget integration
            
        Returns:
            Portfolio analysis results
        """
        try:
            # Get portfolio holdings
            holdings = await self._get_portfolio_holdings(wallet_address, chain)
            
            # Analyze each holding
            holding_analyses = []
            for holding in holdings:
                if holding["value_usd"] > 10:  # Only analyze holdings > $10
                    analysis = await self.analyze_token(
                        token_address=holding["mint"],
                        chain=chain,
                        analysis_depth="standard"
                    )
                    holding_analyses.append({
                        "holding": holding,
                        "analysis": analysis
                    })
            
            # Portfolio-level analysis
            portfolio_metrics = await self._calculate_portfolio_metrics(holdings, holding_analyses)
            
            # Risk analysis
            portfolio_risks = await self._assess_portfolio_risks(holding_analyses)
            
            # Recommendations
            portfolio_recommendations = await self._generate_portfolio_recommendations(
                portfolio_metrics, portfolio_risks
            )
            
            return {
                "success": True,
                "wallet_address": wallet_address,
                "chain": chain,
                "holdings": holdings,
                "holding_analyses": holding_analyses,
                "portfolio_metrics": portfolio_metrics,
                "risk_analysis": portfolio_risks,
                "recommendations": portfolio_recommendations,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "wallet_address": wallet_address,
                "chain": chain
            }
    
    async def defi_yield_opportunities(
        self,
        chain: str = "solana",
        min_apy: float = 5.0,
        max_risk_level: str = "medium",
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Find and analyze DeFi yield opportunities.
        
        Args:
            chain: Blockchain network to search
            min_apy: Minimum APY requirement
            max_risk_level: Maximum acceptable risk level
            thread_id: Conversation thread for widget integration
            
        Returns:
            DeFi yield opportunities analysis
        """
        try:
            # Get yield opportunities
            opportunities = await self.defi_analyzer.find_yield_opportunities(
                chain, min_apy, max_risk_level
            )
            
            # Analyze each opportunity
            opportunity_analyses = []
            for opp in opportunities:
                analysis = await self.defi_analyzer.analyze_protocol(opp["protocol"])
                opportunity_analyses.append({
                    "opportunity": opp,
                    "analysis": analysis
                })
            
            # Risk-adjusted rankings
            ranked_opportunities = await self._rank_yield_opportunities(opportunity_analyses)
            
            return {
                "success": True,
                "chain": chain,
                "criteria": {
                    "min_apy": min_apy,
                    "max_risk_level": max_risk_level
                },
                "opportunities": opportunities,
                "detailed_analyses": opportunity_analyses,
                "ranked_opportunities": ranked_opportunities,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chain": chain
            }
    
    async def _create_token_analysis_workflow(
        self,
        token_address: str,
        chain: str,
        analysis_depth: str,
        thread_id: Optional[str]
    ) -> str:
        """Create workflow for token analysis."""
        workflow_definition = {
            "name": f"Token Analysis: {token_address}",
            "description": f"Comprehensive {analysis_depth} analysis of {chain} token",
            "steps": [
                {"id": "basic_info", "name": "Get Basic Token Information"},
                {"id": "market_data", "name": "Fetch Market Data"},
                {"id": "onchain_analysis", "name": "Perform On-chain Analysis"},
                {"id": "technical_analysis", "name": "Technical Analysis"},
                {"id": "risk_assessment", "name": "Risk Assessment"},
                {"id": "recommendations", "name": "Generate Recommendations"},
                {"id": "create_widgets", "name": "Create Analysis Widgets"}
            ]
        }
        
        return await self.workflow_planner.create_workflow(
            definition=workflow_definition,
            thread_id=thread_id or "crypto_analysis"
        )
    
    async def _get_basic_token_info(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Get basic token information."""
        # Implementation would fetch from appropriate APIs
        return {
            "address": token_address,
            "chain": chain,
            "name": "Token Name",
            "symbol": "TOKEN",
            "decimals": 9,
            "total_supply": 1000000000,
            "creation_date": datetime.now().isoformat()
        }
    
    async def _perform_onchain_analysis(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Perform on-chain analysis."""
        return {
            "holder_count": 1500,
            "top_holders": [],
            "transaction_volume_24h": 50000,
            "unique_traders_24h": 250,
            "liquidity_pools": []
        }
    
    async def _perform_technical_analysis(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Perform technical analysis."""
        return {
            "trend": "bullish",
            "support_levels": [0.001, 0.0008],
            "resistance_levels": [0.0015, 0.002],
            "rsi": 65,
            "moving_averages": {
                "ma_20": 0.0012,
                "ma_50": 0.0011,
                "ma_200": 0.001
            }
        }
    
    async def _assess_risks(self, token_address: str, chain: str, analysis_data: Dict) -> Dict[str, Any]:
        """Assess token risks."""
        return {
            "overall_risk": "medium",
            "liquidity_risk": "low",
            "smart_contract_risk": "medium",
            "market_risk": "high",
            "regulatory_risk": "low",
            "risk_factors": [
                "High volatility",
                "Limited trading history",
                "Concentrated ownership"
            ]
        }
    
    async def _generate_recommendations(self, analysis_data: Dict) -> Dict[str, Any]:
        """Generate investment recommendations."""
        return {
            "recommendation": "HOLD",
            "confidence": 0.7,
            "target_price": 0.002,
            "stop_loss": 0.0008,
            "time_horizon": "3-6 months",
            "reasoning": [
                "Strong technical indicators",
                "Growing community",
                "Solid tokenomics"
            ]
        }
    
    async def _create_analysis_widgets(
        self,
        token_address: str,
        chain: str,
        thread_id: str,
        results: Dict[str, Any]
    ):
        """Create widgets for analysis visualization."""
        # Create token chart widget
        await self.platform_agent.widget_manager.create_widget(
            widget_type="token_chart",
            thread_id=thread_id,
            initial_data={
                "token_address": token_address,
                "chain": chain,
                "chart_provider": "gmgn"
            }
        )
        
        # Create PumpFun widget if Solana token
        if chain == "solana":
            await self.platform_agent.widget_manager.create_widget(
                widget_type="pumpfun",
                thread_id=thread_id,
                initial_data={"category": "new"}
            )
    
    async def _save_analysis_report(self, token_address: str, results: Dict[str, Any]):
        """Save analysis report to file system."""
        report_path = f"agent_workspace/analysis/crypto/{token_address}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        await self.platform_agent.file_manager.write_file(
            path=report_path,
            content=json.dumps(results, indent=2)
        )
    
    async def _analyze_market_trends(self, token_analyses: List[Dict]) -> Dict[str, Any]:
        """Analyze market trends from multiple token analyses."""
        return {
            "overall_sentiment": "bullish",
            "common_patterns": ["meme token surge", "low market cap focus"],
            "risk_trends": ["increased volatility", "pump and dump risks"],
            "opportunity_trends": ["early stage projects", "community-driven tokens"]
        }
    
    async def _create_pumpfun_widget(self, category: str, thread_id: str):
        """Create PumpFun widget for trend analysis."""
        await self.platform_agent.widget_manager.create_widget(
            widget_type="pumpfun",
            thread_id=thread_id,
            initial_data={"category": category, "auto_refresh": True}
        )


def create_crypto_agent(platform_agent: PlatformAgentLoop) -> CryptocurrencyAgent:
    """Factory function to create cryptocurrency agent."""
    return CryptocurrencyAgent(platform_agent)
