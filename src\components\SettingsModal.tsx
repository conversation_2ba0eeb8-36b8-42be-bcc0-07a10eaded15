import { Settings, X } from 'lucide-react';
import { useState } from 'react';
import { createPortal } from 'react-dom';

export function SettingsModal() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
      >
        <Settings className="w-5 h-5 text-[#666] group-hover:text-white" />
      </button>

      {isOpen && createPortal(
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-[10000]"
          onClick={() => setIsOpen(false)}
        >
          <div
            className="bg-[#111] rounded-2xl w-full max-w-md p-6 relative animate-fade-in z-[10001] mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold">Settings</h2>
              <button
                onClick={() => setIsOpen(false)}
                className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-6">
              <div className="text-center text-[#666] py-8">
                <p>Settings panel - features coming soon</p>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}