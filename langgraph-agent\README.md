# Chad GPT LangGraph Agent

A REACT agent powered by LangGraph and OpenRouter, designed to integrate seamlessly with the Chad GPT frontend.

## Features

- **REACT Agent**: Reasoning and Acting capabilities using LangGraph
- **OpenRouter Integration**: Uses moonshotai/kimi-k2 model via OpenRouter API
- **Widget Tools**: Supports PumpFun and DexScreener widget integration
- **Conversation Memory**: Thread-based conversation persistence
- **REST API**: FastAPI server for frontend integration
- **Fallback Support**: Graceful fallback to direct OpenRouter calls

## Setup

### Prerequisites

- Python 3.8+
- pip (Python package manager)

### Installation

1. **Navigate to the agent directory:**
   ```bash
   cd langgraph-agent
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Configuration:**
   The `.env` file is already configured with:
   ```
   OPENROUTER_API_KEY=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
   MODEL_NAME=moonshotai/kimi-k2
   PORT=8001
   ```

### Running the Agent

**Option 1: Using the startup script**
```bash
python start.py
```

**Option 2: Direct execution**
```bash
python main.py
```

The agent will start on `http://localhost:8001`

## API Endpoints

### POST /api/chat

Main chat endpoint for processing messages through the REACT agent.

**Request:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Show me pump.fun data",
      "image": "optional_image_url"
    }
  ],
  "threadId": "optional_thread_id"
}
```

**Response:**
```json
{
  "content": "I'll show you the PumpFun widget for token analysis.",
  "function_call": {
    "name": "showPumpFunWidget",
    "arguments": {}
  }
}
```

### GET /health

Health check endpoint.

## Architecture

### Agent Components

1. **LangGraph REACT Agent**: Core reasoning and action loop
2. **OpenRouter LLM**: moonshotai/kimi-k2 model via OpenRouter API
3. **Custom Tools**: Widget integration tools
4. **Memory Management**: Thread-based conversation persistence
5. **FastAPI Server**: REST API for frontend integration

### Tools Available

- `show_pumpfun_widget`: Displays PumpFun widget for token analysis
- `show_dexscreener_widget`: Displays DexScreener widget for DEX data

### Integration with Frontend

The agent integrates with the existing Chad GPT frontend by:

1. **API Compatibility**: Maintains the same request/response format as the original `getChatCompletion` function
2. **Widget Support**: Returns function calls that trigger widget display
3. **Thread Management**: Supports conversation threading via `threadId`
4. **Fallback Mechanism**: Falls back to direct OpenRouter calls if the agent fails

## Development

### File Structure

```
langgraph-agent/
├── main.py          # FastAPI server
├── agent.py         # LangGraph agent implementation
├── tools.py         # Widget tools definition
├── requirements.txt # Python dependencies
├── .env            # Environment configuration
├── start.py        # Startup script
└── README.md       # This file
```

### Adding New Tools

To add new tools:

1. Define the tool in `tools.py`:
```python
@tool
def new_tool() -> Dict[str, Any]:
    """Tool description."""
    return {"widget_type": "new_widget"}
```

2. Add to the TOOLS list:
```python
TOOLS = [show_pumpfun_widget, show_dexscreener_widget, new_tool]
```

3. Update the frontend to handle the new widget type.

### Debugging

- Check logs in the terminal where the agent is running
- Use `/health` endpoint to verify the service is running
- Monitor the console output for request/response details

## Troubleshooting

### Common Issues

1. **Port 8001 already in use:**
   - Change the PORT in `.env` file
   - Update the LANGGRAPH_API_URL in `src/lib/ai.ts`

2. **OpenRouter API errors:**
   - Verify the API key in `.env`
   - Check OpenRouter account credits/limits

3. **Frontend not connecting:**
   - Ensure the agent is running on port 8001
   - Check CORS configuration in `main.py`
   - Verify the API URL in the frontend

4. **Agent not responding:**
   - Check the agent logs for errors
   - Verify LangGraph dependencies are installed
   - Test the `/health` endpoint

### Performance Notes

- The REACT agent may be slower than direct API calls due to reasoning steps
- Consider implementing caching for frequently asked questions
- Monitor response times and optimize as needed

## Production Deployment

For production deployment:

1. Use a production WSGI server like Gunicorn
2. Set up proper logging and monitoring
3. Configure environment variables securely
4. Consider containerization with Docker
5. Set up load balancing if needed

## License

This project is part of the Chad GPT application.
